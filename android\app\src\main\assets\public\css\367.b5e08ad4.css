.card-small[data-v-022da4db]{width:200px;height:200px;border-radius:var(--radius-xl)}.card-medium[data-v-022da4db]{width:219px;height:218px;border-radius:var(--radius-md)}.card-large[data-v-022da4db]{width:428px;height:428px;border-radius:var(--radius-xl);border-width:9px}.card-extra-large[data-v-022da4db]{width:2000px;height:197px;border-radius:var(--radius-md);border-width:2px}.card-vertical[data-v-022da4db]{flex-direction:column}.card-horizontal[data-v-022da4db]{flex-direction:row}.icon-container-small[data-v-022da4db]{width:80px;height:80px}.icon-container-medium[data-v-022da4db]{width:91px;height:131px;margin-bottom:var(--spacing-xs)}.icon-container-large[data-v-022da4db]{width:320px;height:206px;margin-bottom:34px;position:relative;top:-35px}.icon-container-extra-large[data-v-022da4db]{width:190px;height:156px;margin-left:43px;flex-shrink:0}.icon-image[data-v-022da4db]{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.placeholder-medium[data-v-022da4db],.placeholder-small[data-v-022da4db]{width:60px;height:60px;font-size:var(--font-size-xs)}.placeholder-large[data-v-022da4db]{width:160px;height:160px;font-size:var(--font-size-xl)}.placeholder-extra-large[data-v-022da4db]{width:120px;height:120px;font-size:var(--font-size-md)}.card-content[data-v-022da4db]{display:flex;align-items:center;justify-content:center}.content-vertical[data-v-022da4db]{flex-direction:column;text-align:center}.content-horizontal[data-v-022da4db]{flex:1;justify-content:space-between;padding:0 85px 0 48px}.card-title[data-v-022da4db]{text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.title-small[data-v-022da4db]{font-weight:var(--font-weight-medium);font-size:var(--font-size-xs);max-width:180px}.title-medium[data-v-022da4db]{font-weight:var(--font-weight-normal);font-size:var(--font-size-sm);max-width:200px}.title-large[data-v-022da4db]{font-weight:var(--font-weight-medium);font-size:var(--font-size-md);max-width:324px;position:absolute;bottom:60px;left:50%;transform:translateX(-50%)}.title-extra-large[data-v-022da4db]{font-weight:var(--font-weight-light);font-size:var(--font-size-lg);text-align:left}.card-subtitle[data-v-022da4db]{text-align:center}.subtitle-extra-large[data-v-022da4db]{font-weight:var(--font-weight-light);font-size:var(--font-size-lg);text-align:right}.card-facility[data-v-022da4db]{filter:drop-shadow(4px 4px 4px rgba(0,0,0,.25))}.card-facility[data-v-022da4db]:hover{filter:drop-shadow(0 8px 20px rgba(0,0,0,.3))}.card-shop[data-v-022da4db]{filter:drop-shadow(2px 2px 4px rgba(0,0,0,.25))}.card-shop[data-v-022da4db]:hover{filter:drop-shadow(0 10px 25px rgba(0,0,0,.3))}.card-transport.card-small[data-v-022da4db]{gap:16px;box-shadow:4px 4px 8px 0 rgba(0,0,0,.15),var(--shadow-glass-inset)}.card-transport.card-small[data-v-022da4db]:hover{transform:var(--transform-lift-sm) var(--transform-scale-hover);box-shadow:0 12px 24px 0 rgba(0,0,0,.25),inset 0 2px 4px 0 hsla(0,0%,100%,.15),inset 0 -2px 4px 0 rgba(0,0,0,.15)}@media screen and (max-width:2160px){.card-extra-large[data-v-022da4db]{transform:scale(.0463vw);transform-origin:left top}}.app-title[data-v-584e57c6]{font-family:Inter,sans-serif;font-weight:500;color:#fff;font-size:128px;line-height:1.21;margin:0}.map-container[data-v-584e57c6]{position:relative;width:1941px;height:1885px;margin:0 auto 60px;background:hsla(0,0%,100%,.2);border-radius:20px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);overflow:hidden;box-shadow:4px 4px 4px 0 rgba(0,0,0,.25),inset 0 4px 4px 0 hsla(0,0%,100%,.1),inset 0 -2px 4px 0 rgba(0,0,0,.2)}.map-placeholder[data-v-584e57c6]{width:100%;height:100%;position:relative;display:flex;align-items:center;justify-content:center}.map-image[data-v-584e57c6]{position:absolute;left:0;top:282px;width:1921px;height:1332px;-o-object-fit:cover;object-fit:cover}.facilities-grid[data-v-584e57c6]{position:relative;width:1941px;margin:0 auto;display:grid;grid-template-columns:repeat(8,219px);grid-template-rows:repeat(auto,218px);gap:27px;align-content:start;justify-content:start}.facility-container[data-v-584e57c6] .facility-card:hover,.facility-container[data-v-584e57c6] .glass-button:hover{transform:translateY(-4px);box-shadow:0 8px 20px rgba(0,0,0,.3),inset 0 4px 4px 0 hsla(0,0%,100%,.15),inset 0 -2px 4px 0 rgba(0,0,0,.25)}