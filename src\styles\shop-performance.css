/* Shop页面极端性能优化 - 移除所有动效 */

/* 禁用Shop页面所有过渡和动画，但保持布局 */
.shop-container * {
  transition: none !important;
  animation: none !important;
  transform: none !important;
  will-change: auto !important;
}

/* 确保网格布局不受影响 */
.shop-container .shop-grid,
.shop-container .shop-card-optimized {
  display: revert !important;
  width: revert !important;
  height: revert !important;
  grid-template-columns: revert !important;
  gap: revert !important;
}

/* 保留走马灯动画 */
.marquee-text,
.marquee-container,
.bottom-marquee * {
  transition: revert !important;
  animation: revert !important;
  transform: revert !important;
}

/* 极简化的卡片样式 - 移除所有视觉效果 */
.shop-container .card-shop,
.shop-container .shop-card-optimized {
  /* 确保卡片尺寸正确 */
  width: 100% !important;
  aspect-ratio: 1 !important;
  max-width: 380px !important;

  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
  box-shadow: none !important;
  backdrop-filter: none !important;

  /* 强制禁用所有变换 */
  transform: none !important;
  transition: none !important;
  will-change: auto !important;

  /* 移除优化渲染以避免布局问题 */
  contain: none !important;
  backface-visibility: visible !important;
}

/* 移除所有hover效果 */
.shop-container .card-shop:hover,
.shop-container .card-base:hover,
.shop-container .base-card:hover {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 极简化滚动容器 */
.shop-container .content-area {
  /* 移除复杂的滚动优化 */
  -webkit-overflow-scrolling: auto !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  
  /* 简化硬件加速 */
  transform: none !important;
  will-change: auto !important;
  
  /* 移除contain以减少复杂度 */
  contain: none !important;
  
  /* 简化滚动条 */
  scrollbar-width: none !important;
}

/* 隐藏滚动条以减少渲染负担 */
.shop-container .content-area::-webkit-scrollbar {
  display: none !important;
}

/* 确保网格布局正常工作 */
.shop-container .shop-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  width: 100% !important;
  max-width: 1600px !important;
  margin: 0 auto !important;

  /* 移除性能优化属性，但保持布局 */
  contain: none !important;
  transform: none !important;
  will-change: auto !important;
}

/* 禁用所有按钮动效 */
.shop-container button,
.shop-container .retry-button {
  transition: none !important;
  transform: none !important;
  backdrop-filter: none !important;
  
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.shop-container button:hover,
.shop-container .retry-button:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transform: none !important;
}

.shop-container button:active,
.shop-container .retry-button:active {
  transform: none !important;
}

/* 简化加载动画 - 使用最简单的实现 */
.shop-container .loading-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-top: 2px solid #00EEFF !important;
  border-radius: 50% !important;
  animation: simple-spin 2s linear infinite !important;
}

@keyframes simple-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移除所有阴影和模糊效果 */
.shop-container * {
  box-shadow: none !important;
  filter: none !important;
  backdrop-filter: none !important;
}

/* 简化字体渲染 */
.shop-container * {
  text-rendering: optimizeSpeed !important;
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important;
}

/* 强制使用最简单的背景 */
.shop-container .card-shop,
.shop-container .card-base {
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
}

/* 移除图片的所有效果 */
.shop-container img {
  filter: none !important;
  transform: none !important;
  transition: none !important;
  
  /* 优化图片渲染 */
  image-rendering: optimizeSpeed !important;
  image-rendering: pixelated !important;
}

/* 禁用所有复杂的CSS属性 */
.shop-container .glass-effect,
.shop-container .glass-effect-heavy {
  backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: none !important;
}

/* 强制移除所有变换 */
.shop-container .card-base,
.shop-container .base-card,
.shop-container .shop-card {
  transform: none !important;
  transition: none !important;
  animation: none !important;
  will-change: auto !important;
  backface-visibility: visible !important;
  perspective: none !important;
}

/* 简化主容器 */
.shop-container .main-content {
  /* 移除复杂的定位和变换 */
  transform: none !important;
  will-change: auto !important;
  contain: none !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

/* 移动端特殊优化 */
@media (max-width: 768px) {
  .shop-container * {
    transform: none !important;
    transition: none !important;
    animation: none !important;
    will-change: auto !important;
    contain: none !important;
    backdrop-filter: none !important;
    filter: none !important;
    box-shadow: none !important;
  }
}

/* 强制禁用GPU加速以避免移动端问题 */
.shop-container * {
  transform: none !important;
  transform3d: none !important;
  translateZ: none !important;
  will-change: auto !important;
}

/* 最简化的卡片内容 */
.shop-container .card-content,
.shop-container .card-title,
.shop-container .card-icon {
  transform: none !important;
  transition: none !important;
  will-change: auto !important;
}

/* 禁用所有伪元素效果 */
.shop-container *::before,
.shop-container *::after {
  display: none !important;
}
