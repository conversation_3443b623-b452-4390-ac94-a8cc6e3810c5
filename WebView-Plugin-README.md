# WebView 插件使用说明

## 功能概述
这个自定义的Capacitor插件实现了在Vue应用内嵌入原生WebView，可以加载任何网页内容，不受X-Frame-Options限制。

## 主要特性
- ✅ 原生WebView实现，绕过iframe限制
- ✅ 支持JavaScript交互
- ✅ 可配置的位置和大小
- ✅ 页面加载状态监听
- ✅ 支持缩放和滚动
- ✅ 自动处理HTTPS/HTTP混合内容

## 使用方法

### 1. 构建项目
```bash
# 构建Vue项目
npm run build:prod

# 同步到Android
npx cap sync android

# 打开Android Studio
npx cap open android
```

### 2. 在Android Studio中运行
- 等待Gradle同步完成
- 连接Android设备或启动模拟器
- 点击运行按钮

### 3. 测试WebView功能
- 打开应用后，在首页点击"Web"按钮
- WebView将自动加载香港机场航班信息页面
- 可以正常浏览、缩放和交互

## 自定义配置

### 修改加载的URL
在 `src/views/Web.vue` 中修改：
```typescript
const result = await WebView.showEmbedded({
  url: '您想要加载的URL',
  javascript: true,
  position: {
    top: 350
  }
})
```

### 调整WebView位置
修改 `position.top` 值来调整WebView距离顶部的距离。

## 注意事项
1. WebView插件仅在原生应用中可用，Web开发模式下会显示提示信息
2. 确保在AndroidManifest.xml中有网络权限
3. 某些网站可能需要特定的User-Agent才能正常显示

## 后续优化建议
1. 添加导航控制按钮（前进、后退、刷新）
2. 实现URL输入功能
3. 添加进度条显示
4. 支持文件下载处理
5. 添加iOS平台支持