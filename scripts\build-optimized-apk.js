#!/usr/bin/env node

/**
 * 优化的APK构建脚本
 * 专门针对性能问题进行优化
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始构建优化的APK...')

// 1. 清理缓存
console.log('🧹 清理缓存...')
try {
  execSync('npm run clean', { stdio: 'inherit' })
} catch (error) {
  console.warn('清理缓存失败，继续构建...')
}

// 2. 设置生产环境变量
process.env.NODE_ENV = 'production'
process.env.VUE_APP_PERFORMANCE_MODE = 'optimized'

// 3. 构建Web应用
console.log('📦 构建Web应用...')
execSync('npm run build:prod', { stdio: 'inherit' })

// 4. 优化构建产物
console.log('⚡ 优化构建产物...')
optimizeBuildOutput()

// 5. 同步到Capacitor
console.log('🔄 同步到Capacitor...')
execSync('cap sync android', { stdio: 'inherit' })

// 6. 优化Android配置
console.log('🤖 优化Android配置...')
optimizeAndroidConfig()

// 7. 打开Android Studio进行构建
console.log('📱 打开Android Studio...')
console.log('请在Android Studio中手动构建Release APK')
execSync('cap open android', { stdio: 'inherit' })

console.log('✅ 优化配置完成！')
console.log('📍 请在Android Studio中：')
console.log('   1. 选择 Build > Generate Signed Bundle / APK')
console.log('   2. 选择 APK')
console.log('   3. 选择 release 构建类型')
console.log('   4. 构建完成后APK位置: android/app/build/outputs/apk/release/')

function optimizeBuildOutput() {
  const distPath = path.join(__dirname, '../dist')
  
  // 移除不必要的文件
  const unnecessaryFiles = [
    'favicon.ico',
    '*.map',
    'robots.txt'
  ]
  
  unnecessaryFiles.forEach(pattern => {
    try {
      if (pattern.includes('*')) {
        // 处理通配符
        const files = fs.readdirSync(distPath)
        files.forEach(file => {
          if (file.match(pattern.replace('*', '.*'))) {
            fs.unlinkSync(path.join(distPath, file))
            console.log(`  删除: ${file}`)
          }
        })
      } else {
        const filePath = path.join(distPath, pattern)
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath)
          console.log(`  删除: ${pattern}`)
        }
      }
    } catch (error) {
      console.warn(`  删除文件失败: ${pattern}`)
    }
  })
  
  // 压缩HTML文件
  const indexPath = path.join(distPath, 'index.html')
  if (fs.existsSync(indexPath)) {
    let html = fs.readFileSync(indexPath, 'utf8')
    
    // 移除注释和多余空白
    html = html
      .replace(/<!--[\s\S]*?-->/g, '')
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim()
    
    fs.writeFileSync(indexPath, html)
    console.log('  优化HTML文件')
  }
}

function optimizeAndroidConfig() {
  // 优化MainActivity.java
  const mainActivityPath = path.join(__dirname, '../android/app/src/main/java/com/example/emapai/MainActivity.java')
  
  if (fs.existsSync(mainActivityPath)) {
    let content = fs.readFileSync(mainActivityPath, 'utf8')
    
    // 如果还没有优化过，则添加性能优化代码
    if (!content.includes('// Performance optimizations')) {
      const optimizations = `
    // Performance optimizations
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 启用硬件加速
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        );
        
        // 优化WebView性能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(false);
        }
    }
    
    @Override
    public void onStart() {
        super.onStart();
        
        // 优化内存使用
        if (getBridge() != null && getBridge().getWebView() != null) {
            WebView webView = getBridge().getWebView();
            WebSettings settings = webView.getSettings();
            
            // 启用缓存
            settings.setCacheMode(WebSettings.LOAD_DEFAULT);
            settings.setDatabaseEnabled(true);
            settings.setDomStorageEnabled(true);
            
            // 优化渲染
            settings.setRenderPriority(WebSettings.RenderPriority.HIGH);
            settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
            
            // 禁用不必要的功能
            settings.setGeolocationEnabled(false);
            settings.setAllowFileAccess(false);
            settings.setAllowContentAccess(false);
        }
    }`
      
      // 在类的末尾添加优化代码
      content = content.replace(
        /}\s*$/,
        optimizations + '\n}'
      )
      
      // 添加必要的import
      if (!content.includes('import android.webkit.WebView;')) {
        content = content.replace(
          'import com.getcapacitor.BridgeActivity;',
          `import com.getcapacitor.BridgeActivity;
import android.webkit.WebView;
import android.webkit.WebSettings;
import android.view.WindowManager;
import android.os.Build;`
        )
      }
      
      fs.writeFileSync(mainActivityPath, content)
      console.log('  优化MainActivity.java')
    }
  }
  
  // 优化build.gradle
  const buildGradlePath = path.join(__dirname, '../android/app/build.gradle')
  if (fs.existsSync(buildGradlePath)) {
    let content = fs.readFileSync(buildGradlePath, 'utf8')
    
    // 添加性能优化配置
    if (!content.includes('// Performance optimizations')) {
      const optimizations = `
    // Performance optimizations
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 启用R8优化
            useProguard false
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }`
      
      // 在android块中添加优化配置
      content = content.replace(
        /android\s*{/,
        `android {
${optimizations}`
      )
      
      fs.writeFileSync(buildGradlePath, content)
      console.log('  优化build.gradle')
    }
  }
  
  // 创建proguard规则文件
  const proguardPath = path.join(__dirname, '../android/app/proguard-rules.pro')
  if (!fs.existsSync(proguardPath)) {
    const proguardRules = `
# 保留Capacitor相关类
-keep class com.getcapacitor.** { *; }
-keep class com.capacitorjs.** { *; }

# 保留WebView相关类
-keep class android.webkit.** { *; }

# 优化混淆
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# 移除日志
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}
`
    fs.writeFileSync(proguardPath, proguardRules)
    console.log('  创建proguard-rules.pro')
  }
}
