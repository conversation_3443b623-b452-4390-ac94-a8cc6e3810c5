package com.example.emapai;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {
    private static final String TAG = "MainActivity";
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 处理intent
        handleIntent(getIntent());
        
        // 注册自定义插件
        try {
            Log.d(TAG, "Registering WebViewPlugin...");
            registerPlugin(WebViewPlugin.class);
            Log.d(TAG, "WebViewPlugin registered successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to register WebViewPlugin", e);
        }
    }
    
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }
    
    private void handleIntent(Intent intent) {
        if (intent != null && intent.getData() != null) {
            Uri data = intent.getData();
            Log.d(TAG, "Received intent with data: " + data.toString());
            
            if ("webview".equals(data.getScheme())) {
                Log.d(TAG, "Handling webview scheme");
                // 直接启动WebViewActivity
                Intent webViewIntent = new Intent(this, WebViewActivity.class);
                webViewIntent.putExtra("url", "https://www.hongkongairport.com/en/flights/arrivals/passenger.page");
                startActivity(webViewIntent);
            }
        }
    }
}
