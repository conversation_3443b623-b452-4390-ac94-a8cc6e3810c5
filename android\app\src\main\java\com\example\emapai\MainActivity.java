package com.example.emapai;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import com.getcapacitor.BridgeActivity;
import android.webkit.WebView;
import android.webkit.WebSettings;
import android.view.WindowManager;
import android.os.Build;

public class MainActivity extends BridgeActivity {
    private static final String TAG = "MainActivity";
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 处理intent
        handleIntent(getIntent());
        
        // 注册自定义插件
        try {
            Log.d(TAG, "Registering WebViewPlugin...");
            registerPlugin(WebViewPlugin.class);
            Log.d(TAG, "WebViewPlugin registered successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to register WebViewPlugin", e);
        }
    }
    
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }
    
    private void handleIntent(Intent intent) {
        if (intent != null && intent.getData() != null) {
            Uri data = intent.getData();
            Log.d(TAG, "Received intent with data: " + data.toString());
            
            if ("webview".equals(data.getScheme())) {
                Log.d(TAG, "Handling webview scheme");
                // 直接启动WebViewActivity
                Intent webViewIntent = new Intent(this, WebViewActivity.class);
                webViewIntent.putExtra("url", "https://www.hongkongairport.com/en/flights/arrivals/passenger.page");
                startActivity(webViewIntent);
            }
        }
    }

    // Performance optimizations
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 启用硬件加速
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        );
        
        // 优化WebView性能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(false);
        }
    }
    
    @Override
    public void onStart() {
        super.onStart();
        
        // 优化内存使用
        if (getBridge() != null && getBridge().getWebView() != null) {
            WebView webView = getBridge().getWebView();
            WebSettings settings = webView.getSettings();
            
            // 启用缓存
            settings.setCacheMode(WebSettings.LOAD_DEFAULT);
            settings.setDatabaseEnabled(true);
            settings.setDomStorageEnabled(true);
            
            // 优化渲染
            settings.setRenderPriority(WebSettings.RenderPriority.HIGH);
            settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
            
            // 禁用不必要的功能
            settings.setGeolocationEnabled(false);
            settings.setAllowFileAccess(false);
            settings.setAllowContentAccess(false);
        }
    }
}