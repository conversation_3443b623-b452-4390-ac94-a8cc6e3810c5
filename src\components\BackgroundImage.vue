<template>
  <div 
    class="background-image" 
    :style="backgroundStyle"
    :class="{ 'with-offset': hasOffset }"
  ></div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { mapGetters } from 'vuex'

@Component({
  computed: {
    ...mapGetters('config', ['backgroundImageUrl', 'hasConfig'])
  }
})
export default class BackgroundImage extends Vue {
  @Prop({ default: 0.9 })
  opacity!: number
  
  @Prop({ default: false })
  hasOffset!: boolean
  
  // 从store获取的计算属性
  backgroundImageUrl!: string
  hasConfig!: boolean
  
  get backgroundStyle() {
    // 优先使用远程配置的背景图片，没有配置时使用本地默认图片
    const imageUrl = this.getBackgroundImageUrl()
    
    return {
      backgroundImage: `url('${imageUrl}')`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      opacity: this.opacity
    }
  }
  
  /**
   * 获取背景图片URL
   * 优先级：远程配置 > 本地默认图片
   */
  getBackgroundImageUrl(): string {
    // 如果有远程配置且有背景图片URL，使用远程图片
    if (this.hasConfig && this.backgroundImageUrl) {
      console.log('使用远程背景图片:', this.backgroundImageUrl)
      return this.backgroundImageUrl
    }
    
    // 否则使用本地默认图片
    console.log('使用本地默认背景图片')
    return '/img/bg.png'
  }
}
</script>

<style scoped>
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  /* 添加过渡效果，背景切换时更平滑 */
  transition: background-image 0.5s ease-in-out;
}

.background-image.with-offset {
  left: -360px;
  width: 2880px;
  height: 3840px;
}
</style> 