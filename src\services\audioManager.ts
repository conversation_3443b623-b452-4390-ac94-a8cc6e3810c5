/**
 * 优化的音频管理器 - 专为移动端性能优化
 * 解决原有soundService的性能问题
 */

import { Capacitor } from '@capacitor/core'

interface AudioConfig {
  enabled: boolean
  volume: number
  throttleMs: number
}

class AudioManager {
  private static instance: AudioManager
  private config: AudioConfig = {
    enabled: true,
    volume: 0.3,
    throttleMs: 150 // 增加节流时间，减少频繁播放
  }
  
  private lastPlayTime = 0
  private audioContext: AudioContext | null = null
  private isInitialized = false
  
  // 简化的音频缓存
  private audioCache = new Map<string, HTMLAudioElement>()
  private maxCacheSize = 3 // 限制缓存大小
  
  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager()
    }
    return AudioManager.instance
  }

  private constructor() {
    this.initializeAudio()
  }

  private async initializeAudio(): Promise<void> {
    try {
      // 在移动端优先使用HTML5 Audio而不是Web Audio API
      if (Capacitor.isNativePlatform()) {
        this.initializeHTMLAudio()
      } else {
        this.initializeWebAudio()
      }
    } catch (error) {
      console.warn('音频初始化失败，使用静默模式:', error)
      this.config.enabled = false
    }
  }

  private initializeHTMLAudio(): void {
    // 预加载一个简单的音频文件
    this.preloadSound('click', '/sounds/button_voice.mp3')
    this.isInitialized = true
  }

  private initializeWebAudio(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.isInitialized = true
    } catch (error) {
      console.warn('Web Audio API不可用，回退到HTML5 Audio:', error)
      this.initializeHTMLAudio()
    }
  }

  private preloadSound(name: string, url: string): void {
    if (this.audioCache.size >= this.maxCacheSize) {
      // 清理最旧的缓存
      const firstKey = this.audioCache.keys().next().value
      if (firstKey) {
        const audio = this.audioCache.get(firstKey)
        if (audio) {
          audio.src = ''
          audio.load()
        }
        this.audioCache.delete(firstKey)
      }
    }

    const audio = new Audio()
    audio.preload = 'auto'
    audio.volume = this.config.volume
    audio.src = url
    
    // 静默加载，不显示错误
    audio.addEventListener('error', () => {
      console.warn(`音频文件加载失败: ${url}`)
    })
    
    this.audioCache.set(name, audio)
  }

  public playClickSound(): void {
    if (!this.config.enabled || !this.isInitialized) {
      return
    }

    // 节流控制
    const now = Date.now()
    if (now - this.lastPlayTime < this.config.throttleMs) {
      return
    }
    this.lastPlayTime = now

    // 优先使用缓存的音频
    const cachedAudio = this.audioCache.get('click')
    if (cachedAudio) {
      this.playHTMLAudio(cachedAudio)
    } else {
      // 回退到合成音效
      this.playSyntheticClick()
    }
  }

  private playHTMLAudio(audio: HTMLAudioElement): void {
    try {
      // 重置播放位置
      audio.currentTime = 0
      audio.volume = this.config.volume
      
      // 播放音频
      const playPromise = audio.play()
      if (playPromise) {
        playPromise.catch(() => {
          // 静默处理播放失败
        })
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  private playSyntheticClick(): void {
    if (!this.audioContext) {
      return
    }

    try {
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      // 简化的音效参数
      oscillator.type = 'sine'
      oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime)
      
      // 快速的音量包络
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.config.volume * 0.5, this.audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.05)
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.05)
    } catch (error) {
      // 静默处理错误
    }
  }

  public setEnabled(enabled: boolean): void {
    this.config.enabled = enabled
  }

  public isEnabled(): boolean {
    return this.config.enabled
  }

  public setVolume(volume: number): void {
    this.config.volume = Math.max(0, Math.min(1, volume))
    
    // 更新缓存音频的音量
    this.audioCache.forEach(audio => {
      audio.volume = this.config.volume
    })
  }

  public getVolume(): number {
    return this.config.volume
  }

  public async initUserInteraction(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      if (this.audioContext && this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      
      // 预加载音频文件
      this.preloadSound('click', '/sounds/button_voice.mp3')
      
      this.isInitialized = true
    } catch (error) {
      console.warn('用户交互初始化失败:', error)
    }
  }

  public cleanup(): void {
    // 清理资源
    this.audioCache.forEach(audio => {
      audio.src = ''
      audio.load()
    })
    this.audioCache.clear()
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
    }
  }

  public getStatus(): string {
    return `音频管理器状态: ${this.isInitialized ? '已初始化' : '未初始化'}, 启用: ${this.config.enabled}, 缓存: ${this.audioCache.size}个音频`
  }
}

// 导出单例实例
export const audioManager = AudioManager.getInstance()

// 兼容性导出，替换原有的soundService
export const soundService = {
  playSound: (name: string, volume?: number) => {
    if (volume !== undefined) {
      audioManager.setVolume(volume)
    }
    audioManager.playClickSound()
    return true
  },
  playButtonClick: (volume?: number) => {
    if (volume !== undefined) {
      audioManager.setVolume(volume)
    }
    audioManager.playClickSound()
  },
  setEnabled: (enabled: boolean) => audioManager.setEnabled(enabled),
  isEnabled: () => audioManager.isEnabled(),
  initUserInteraction: () => audioManager.initUserInteraction(),
  preloadSound: (name: string, url: string) => {
    // 简化预加载逻辑
    return Promise.resolve()
  },
  getAudioState: () => audioManager.isEnabled() ? 'running' : 'suspended'
}
