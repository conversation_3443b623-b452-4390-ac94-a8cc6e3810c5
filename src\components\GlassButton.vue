<template>
  <button 
    class="glass-button"
    :class="[sizeClass, variantClass, { 'glass-button-active': active }]"
    @click="handleClick"
    @mousedown="$emit('mousedown', $event)"
    @mouseup="$emit('mouseup', $event)"
    @mouseleave="$emit('mouseleave', $event)"
    @touchstart="$emit('touchstart', $event)"
    @touchend="$emit('touchend', $event)"
    @touchcancel="$emit('touchcancel', $event)"
  >
    <slot></slot>
  </button>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { soundService } from '@/services/soundService'

@Component
export default class GlassButton extends Vue {
  @Prop({ default: 'medium' }) size!: 'small' | 'medium' | 'large'
  @Prop({ default: 'normal' }) variant!: 'normal' | 'highlight'
  @Prop({ default: false }) active!: boolean
  @Prop({ default: true }) enableSound!: boolean

  get sizeClass(): string {
    const sizes = {
      small: 'small-btn',
      medium: 'medium-btn',
      large: 'large-btn'
    }
    return sizes[this.size]
  }

  get variantClass(): string {
    const variants = {
      normal: 'glass-normal',
      highlight: 'glass-highlight'
    }
    return variants[this.variant]
  }

  private handleClick(event: Event) {
    // 播放按钮点击音效
    if (this.enableSound) {
      // 优先使用预加载的音频文件，如果文件未加载则使用合成音效作为备用
      soundService.playSound('buttonClick', 0.4) || soundService.playButtonClick()
    }
    
    // 触发原有的click事件
    this.$emit('click', event)
  }

  mounted() {
    // 初始化音频上下文（需要用户交互）
    soundService.initUserInteraction()
  }
}
</script>

<style scoped>
.glass-button {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  color: var(--color-white);
  line-height: var(--line-height-standard);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  border: 5px solid transparent;
  backdrop-filter: var(--blur-light);
  transition: var(--transition-standard);
  cursor: pointer;
  
  /* 统一的边框渐变效果 */
  border-image: var(--border-gradient-glass) 1;
  
  /* 内阴影效果 */
  box-shadow: var(--shadow-glass-inset);
}

.small-btn {
  width: 200px;
  height: 203px;
  font-size: var(--font-size-lg);
}

.medium-btn {
  width: 550px;
  height: 550px;
  font-size: var(--font-size-2xl);
}

.large-btn {
  width: 300px;
  height: 300px;
  font-size: var(--font-size-xl);
}

/* 高亮变体 */
.glass-highlight {
  background: var(--glass-bg-normal);
}

/* 普通变体 */
.glass-normal {
  background: var(--glass-bg-light);
}

.glass-button:hover {
  transform: var(--transform-lift-md);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-button-hover);
}

.glass-highlight:hover {
  background: var(--glass-bg-medium);
}

.glass-normal:hover {
  background: rgba(255, 255, 255, 0.07);
}

.glass-button:active {
  transform: var(--transform-press);
}

.glass-button-active {
  transform: var(--transform-press);
}
</style> 