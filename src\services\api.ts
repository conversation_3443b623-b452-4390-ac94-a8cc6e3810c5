// API 服务
export interface VideoConfig {
  id?: number
  title: string
  description: string
  url: string
  duration: string
  category: string
}

export interface ImageFormat {
  name: string
  hash: string
  ext: string
  mime: string
  path: string | null
  width: number
  height: number
  size: number
  sizeInBytes: number
  url: string
}

export interface ImageConfig {
  id: number
  documentId: string
  name: string
  alternativeText: string | null
  caption: string | null
  width: number
  height: number
  formats: {
    thumbnail: ImageFormat
    small: ImageFormat
    medium: ImageFormat
    large: ImageFormat
  }
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl: string | null
  provider: string
  provider_metadata: any
  createdAt: string
  updatedAt: string
  publishedAt: string
}

export interface EMapConfig {
  id: number
  documentId: string
  baseColor: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  video: VideoConfig[]
  backgrondImage: ImageConfig
  poster: ImageConfig[]
}

export interface ApiResponse {
  data: EMapConfig
  meta: any
}

class ApiService {
  private readonly baseUrl = 'https://testapi.bwaiwork.xyz'
  private readonly cacheKey = 'emap_config_cache'
  private readonly cacheTimestampKey = 'emap_config_timestamp'
  private readonly cacheValidTime = 24 * 60 * 60 * 1000 // 24小时缓存时间

  /**
   * 创建兼容的超时AbortController
   */
  private createTimeoutController(timeoutMs: number): AbortController {
    const controller = new AbortController()
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, timeoutMs)
    
    // 清理超时，避免内存泄漏
    const originalAbort = controller.abort.bind(controller)
    controller.abort = () => {
      clearTimeout(timeoutId)
      originalAbort()
    }
    
    return controller
  }

  /**
   * 获取配置数据（优先从缓存，网络不佳时使用缓存）
   */
  async getConfig(): Promise<EMapConfig | null> {
    try {
      // 先尝试获取远程数据
      const remoteConfig = await this.fetchRemoteConfig()
      if (remoteConfig) {
        // 成功获取远程数据，更新缓存
        this.saveToCache(remoteConfig)
        return remoteConfig
      }
    } catch (error) {
      console.warn('获取远程配置失败，尝试使用缓存:', error)
    }

    // 远程获取失败，使用缓存
    const cachedConfig = this.getCachedConfig()
    if (cachedConfig) {
      console.log('使用缓存配置')
      return cachedConfig
    }

    console.error('无法获取配置数据（远程和缓存都失败）')
    return null
  }

  /**
   * 从远程API获取配置
   */
  private async fetchRemoteConfig(): Promise<EMapConfig | null> {
    const controller = this.createTimeoutController(10000) // 10秒超时
    
    try {
      const response = await fetch(`${this.baseUrl}/api/emap-setting?populate=*`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const apiResponse: ApiResponse = await response.json()
      console.log('成功获取远程配置')
      return apiResponse.data
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('请求超时')
        throw new Error('网络请求超时')
      }
      console.error('获取远程配置时出错:', error)
      throw error
    } finally {
      // 确保清理资源
      controller.abort()
    }
  }

  /**
   * 从缓存获取配置
   */
  private getCachedConfig(): EMapConfig | null {
    try {
      const cachedData = localStorage.getItem(this.cacheKey)
      const cachedTimestamp = localStorage.getItem(this.cacheTimestampKey)

      if (!cachedData || !cachedTimestamp) {
        return null
      }

      // 检查缓存是否过期
      const timestamp = parseInt(cachedTimestamp, 10)
      const now = Date.now()
      const isExpired = (now - timestamp) > this.cacheValidTime

      if (isExpired) {
        console.log('缓存已过期')
        this.clearCache()
        return null
      }

      return JSON.parse(cachedData) as EMapConfig
    } catch (error) {
      console.error('读取缓存时出错:', error)
      this.clearCache()
      return null
    }
  }

  /**
   * 保存配置到缓存
   */
  private saveToCache(config: EMapConfig): void {
    try {
      localStorage.setItem(this.cacheKey, JSON.stringify(config))
      localStorage.setItem(this.cacheTimestampKey, Date.now().toString())
      console.log('配置已保存到缓存')
    } catch (error) {
      console.error('保存缓存时出错:', error)
    }
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    try {
      localStorage.removeItem(this.cacheKey)
      localStorage.removeItem(this.cacheTimestampKey)
      console.log('缓存已清除')
    } catch (error) {
      console.error('清除缓存时出错:', error)
    }
  }

  /**
   * 构建完整的图片URL
   */
  buildImageUrl(imageUrl: string): string {
    if (!imageUrl) return ''
    
    // 如果已经是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl
    }
    
    // 拼接base URL
    return `${this.baseUrl}${imageUrl}`
  }

  /**
   * 获取最佳尺寸的图片URL
   */
  getBestImageUrl(image: ImageConfig, preferredSize: 'thumbnail' | 'small' | 'medium' | 'large' = 'large'): string {
    if (!image) return ''

    // 优先使用指定尺寸
    if (image.formats && image.formats[preferredSize]) {
      return this.buildImageUrl(image.formats[preferredSize].url)
    }

    // 回退到原图
    return this.buildImageUrl(image.url)
  }

  /**
   * 强制刷新配置（清除缓存后重新获取）
   */
  async forceRefresh(): Promise<EMapConfig | null> {
    this.clearCache()
    return this.getConfig()
  }

  /**
   * 检查网络连接状态
   */
  async checkNetworkConnection(): Promise<boolean> {
    try {
      const controller = this.createTimeoutController(3000) // 3秒超时
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'HEAD',
        signal: controller.signal
      })
      return response.ok
    } catch (error) {
      console.warn('网络连接检查失败:', error)
      return false
    }
  }

  /**
   * 获取缓存状态信息
   */
  getCacheStatus(): { hasCache: boolean; cacheAge: number; isExpired: boolean } {
    const cachedTimestamp = localStorage.getItem(this.cacheTimestampKey)
    const hasCache = !!localStorage.getItem(this.cacheKey) && !!cachedTimestamp
    
    if (!hasCache) {
      return { hasCache: false, cacheAge: 0, isExpired: true }
    }
    
    const timestamp = parseInt(cachedTimestamp!, 10)
    const cacheAge = Date.now() - timestamp
    const isExpired = cacheAge > this.cacheValidTime
    
    return { hasCache, cacheAge, isExpired }
  }
}

export const apiService = new ApiService()
export default apiService 