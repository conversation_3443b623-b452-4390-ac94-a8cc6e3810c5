"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[884],{4884:(t,e,s)=>{s.r(e),s.d(e,{default:()=>$});var i=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"poster-container responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--poster"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.poster")))])]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--standard"},[e("div",{staticClass:"poster-swiper-container"},[e("swiper",{ref:"posterSwiper",staticClass:"poster-swiper",attrs:{options:t.swiperOptions}},t._l(t.displayPosters,function(s,i){return e("swiper-slide",{key:s.id||i,staticClass:"poster-slide"},[e("div",{staticClass:"poster-item"},[e("img",{staticClass:"poster-image",attrs:{src:t.getPosterImageUrl(s),alt:s.title},on:{error:t.onImageError}}),e("div",{staticClass:"poster-overlay"},[e("div",{staticClass:"poster-info"},[e("h3",{staticClass:"poster-name"},[t._v(t._s(s.title))]),e("p",{staticClass:"poster-description"},[t._v(t._s(s.description))])])])])])}),1),e("div",{staticClass:"swiper-pagination"})],1),e("div",{staticClass:"poster-controls"},[e("div",{staticClass:"control-buttons"},[e("button",{staticClass:"control-btn",attrs:{disabled:!t.canGoPrevious},on:{click:t.previousPoster}},[e("span",{staticClass:"control-icon"},[t._v("◀")]),e("span",{staticClass:"control-text"},[t._v(t._s(t.$t("poster.previous")))])]),e("button",{staticClass:"control-btn",class:{active:t.isPaused},on:{click:t.pauseAutoplay}},[e("span",{staticClass:"control-icon"},[t._v(t._s(t.isPaused?"▶":"⏸"))]),e("span",{staticClass:"control-text"},[t._v(t._s(t.isPaused?t.$t("poster.play"):t.$t("poster.pause")))])]),e("button",{staticClass:"control-btn",attrs:{disabled:!t.canGoNext},on:{click:t.nextPoster}},[e("span",{staticClass:"control-icon"},[t._v("▶")]),e("span",{staticClass:"control-text"},[t._v(t._s(t.$t("poster.next")))])])]),e("div",{staticClass:"poster-counter"},[e("span",{staticClass:"counter-text"},[t._v(t._s(t.currentPosterIndex+1)+" / "+t._s(t.displayPosters.length))])])])])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),e("BottomMarquee")],1)},o=[],r=s(1635),a=s(9603),n=s(5353),l=s(3452),p=s(3205),c=s(4184),d=s(256),u=s(5185),h=s(7959),g=s(4276),C=s(6296);let m=class extends((0,a.Xe)(u.A,h.A)){posters;hasConfig;currentPosterIndex=0;isPaused=!1;getBackgroundColor(){return"#FF6B6B"}get defaultPosters(){return[{id:"1",title:this.$t("posterContent.splus.title"),description:this.$t("posterContent.splus.description"),url:"img/posters/poster02.jpg",type:"image"},{id:"2",title:this.$t("posterContent.ikea.title"),description:this.$t("posterContent.ikea.description"),url:"img/posters/poster01.jpg",type:"image"},{id:"3",title:this.$t("posterContent.more.title"),description:this.$t("posterContent.more.description"),url:"img/posters/poster02.jpg",type:"image"}]}get displayPosters(){return this.hasConfig&&this.posters&&this.posters.length>0?(console.log("使用远程海报配置"),this.posters.map((t,e)=>({id:String(t.id),title:t.alternativeText||`${this.$t("poster.defaultTitle")} ${e+1}`,description:t.caption||`${this.$t("poster.defaultDescription")} ${e+1}`,url:C.A.buildImageUrl(t.url),type:"image"}))):(console.log("使用本地默认海报"),this.defaultPosters)}getPosterImageUrl(t){return t.url.startsWith("http://")||t.url.startsWith("https://")||t.url.startsWith("/")?t.url:`/${t.url}`}get displayPostersWatch(){return this.displayPosters.length}swiperOptions={direction:"horizontal",slidesPerView:1,spaceBetween:0,centeredSlides:!0,loop:!0,initialSlide:0,autoplay:{delay:3e3,disableOnInteraction:!1,pauseOnMouseEnter:!0},effect:"slide",speed:800,pagination:{el:".swiper-pagination",clickable:!0,dynamicBullets:!0},touchRatio:1,touchAngle:45,simulateTouch:!0,allowTouchMove:!0,touchStartPreventDefault:!1,touchMoveStopPropagation:!1,touchReleaseOnEdges:!1,mousewheel:{invert:!1,sensitivity:1,thresholdDelta:50},keyboard:{enabled:!0,onlyInViewport:!0},on:{init:()=>{this.$nextTick(()=>{this.updateCurrentIndex()})},slideChange:()=>{this.updateCurrentIndex()}}};get canGoPrevious(){return this.currentPosterIndex>0||this.displayPosters.length>1}get canGoNext(){return this.currentPosterIndex<this.displayPosters.length-1||this.displayPosters.length>1}mounted(){console.log("Poster组件已挂载，海报数量:",this.displayPosters.length),this.$nextTick(()=>{setTimeout(()=>{this.initializeSwiper()},100)})}initializeSwiper(){const t=this.$refs.posterSwiper?.$swiper;t?(console.log("Swiper 初始化成功"),t.slideTo(0,0),this.updateCurrentIndex(),t.autoplay&&!this.isPaused&&t.autoplay.start()):console.error("Swiper 初始化失败")}updateCurrentIndex(){const t=this.$refs.posterSwiper?.$swiper;t&&(this.currentPosterIndex=t.realIndex)}previousPoster(){const t=this.$refs.posterSwiper?.$swiper;t&&t.slidePrev()}nextPoster(){const t=this.$refs.posterSwiper?.$swiper;t&&t.slideNext()}pauseAutoplay(){const t=this.$refs.posterSwiper?.$swiper;t&&(this.isPaused?(t.autoplay.start(),this.isPaused=!1):(t.autoplay.stop(),this.isPaused=!0))}goToPoster(t){const e=this.$refs.posterSwiper?.$swiper;e&&t>=0&&t<this.displayPosters.length&&e.slideTo(t)}onImageError(t){const e=t.target;e&&(e.src="/img/placeholder-poster.png",console.warn("海报图片加载失败，使用占位图片"))}};m=(0,r.Cg)([(0,a.uA)({components:{TopBar:l.A,BottomBar:p.A,BottomMarquee:c.A,BackgroundImage:d.A,Swiper:g.Swiper,SwiperSlide:g.SwiperSlide},computed:{...(0,n.L8)("config",["posters","hasConfig"])},watch:{displayPosters:{handler(t){t&&t.length>0&&(console.log("海报数据更新，重新初始化 Swiper"),this.$nextTick(()=>{setTimeout(()=>{this.initializeSwiper()},200)}))},immediate:!1}}})],m);const P=m,v=P;var w=s(1656),y=(0,w.A)(v,i,o,!1,null,"2211b401",null);const $=y.exports}}]);