<template>
  <div class="worldtime-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- WorldTime主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--worldtime">
      <h1 class="app-title">{{ $t('pageTitle.worldTime') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 内容区域 -->
        <div class="worldtime-content">
          <div class="worldtime-card">
            <h2 class="content-title">{{ $t('worldTime.title') }}</h2>
            
            <div class="features">
              <h3 class="section-title">{{ $t('worldTime.realtimeTitle') }}</h3>
              <div class="time-zones">
                <div 
                  v-for="timezone in timezones" 
                  :key="timezone.id"
                  class="time-zone"
                >
                  <div class="city-name">{{ timezone.displayName }}</div>
                  <div class="current-time">{{ getTimeForTimezone(timezone.timezone) }}</div>
                  <div class="timezone-info">{{ timezone.timezoneInfo }}</div>
                  <div class="date-info">{{ getDateForTimezone(timezone.timezone) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

interface TimezoneConfig {
  id: string
  displayName: string
  timezone: string
  timezoneInfo: string
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class WorldTime extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#E67E22' // WorldTime页面的橙色背景
  }

  private timeUpdateInterval: any = null

  // 时区配置数据
  get timezones(): TimezoneConfig[] {
    return [
      {
        id: 'hongkong',
        displayName: this.$t('cities.hongkong'),
        timezone: 'Asia/Hong_Kong',
        timezoneInfo: 'HKT (UTC+8)'
      },
      {
        id: 'tokyo',
        displayName: this.$t('cities.tokyo'),
        timezone: 'Asia/Tokyo',
        timezoneInfo: 'JST (UTC+9)'
      },
      {
        id: 'newyork',
        displayName: this.$t('cities.newyork'),
        timezone: 'America/New_York',
        timezoneInfo: 'EST/EDT (UTC-5/-4)'
      },
      {
        id: 'london',
        displayName: this.$t('cities.london'),
        timezone: 'Europe/London',
        timezoneInfo: 'GMT/BST (UTC+0/+1)'
      },
      {
        id: 'paris',
        displayName: this.$t('cities.paris'),
        timezone: 'Europe/Paris',
        timezoneInfo: 'CET/CEST (UTC+1/+2)'
      },
      {
        id: 'sydney',
        displayName: this.$t('cities.sydney'),
        timezone: 'Australia/Sydney',
        timezoneInfo: 'AEST/AEDT (UTC+10/+11)'
      },
      {
        id: 'beijing',
        displayName: this.$t('cities.beijing'),
        timezone: 'Asia/Shanghai',
        timezoneInfo: 'CST (UTC+8)'
      },
      {
        id: 'seoul',
        displayName: this.$t('cities.seoul'),
        timezone: 'Asia/Seoul',
        timezoneInfo: 'KST (UTC+9)'
      },
      {
        id: 'dubai',
        displayName: this.$t('cities.dubai'),
        timezone: 'Asia/Dubai',
        timezoneInfo: 'GST (UTC+4)'
      },
      {
        id: 'losangeles',
        displayName: this.$t('cities.losangeles'),
        timezone: 'America/Los_Angeles',
        timezoneInfo: 'PST/PDT (UTC-8/-7)'
      }
    ]
  }

  // 获取指定时区的时间
  getTimeForTimezone(timezone: string): string {
    try {
      const now = new Date()
      const timeString = now.toLocaleTimeString('zh-TW', {
        timeZone: timezone,
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      return timeString
    } catch (error) {
      console.error(`获取时区 ${timezone} 时间失败:`, error)
      return '--:--:--'
    }
  }

  // 获取指定时区的日期
  getDateForTimezone(timezone: string): string {
    try {
      const now = new Date()
      const dateString = now.toLocaleDateString('zh-TW', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'short'
      })
      return dateString
    } catch (error) {
      console.error(`获取时区 ${timezone} 日期失败:`, error)
      return '----/--/--'
    }
  }

  // 强制更新组件（用于定时器）
  forceUpdate() {
    this.$forceUpdate()
  }

  mounted() {
    console.log('WorldTime 组件已挂载，开始时间更新')
    // 每秒更新时间
    this.timeUpdateInterval = setInterval(() => {
      this.forceUpdate()
    }, 1000)
  }

  beforeDestroy() {
    // 清理定时器
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval)
      this.timeUpdateInterval = null
      console.log('WorldTime 组件销毁，清理定时器')
    }
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
.worldtime-content {
  width: 1800px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600px;
}

.worldtime-card {
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  backdrop-filter: blur(20px);
  padding: 80px;
  text-align: center;
  width: 100%;
  
  /* 毛玻璃效果 */
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
}

.content-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 96px;
  color: #FFFFFF;
  margin: 0 0 60px 0;
  line-height: 1.2;
}

.section-title {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 72px;
  color: #FFFFFF;
  margin: 0 0 60px 0;
  line-height: 1.2;
}

.features {
  margin-top: 60px;
}

.time-zones {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.time-zone {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 40px 30px;
  color: #FFFFFF;
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
  text-align: center;
}

.time-zone:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.city-name {
  font-size: 42px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #00EEFF;
}

.current-time {
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #FFFFFF;
  font-family: 'Courier New', monospace;
}

.timezone-info {
  font-size: 32px;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.date-info {
  font-size: 28px;
  font-weight: 400;
  color: rgba(0, 238, 255, 0.8);
  margin-top: 10px;
}
</style> 