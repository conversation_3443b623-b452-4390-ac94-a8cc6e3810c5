// Japanese language pack
export default {
  // 共通
  common: {
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    cancel: 'キャンセル',
    confirm: '確認',
    back: '戻る',
    next: '次へ',
    previous: '前へ',
    close: '閉じる',
    save: '保存',
    delete: '削除',
    edit: '編集',
    search: '検索',
    filter: 'フィルター',
    all: 'すべて',
    none: 'なし',
    yes: 'はい',
    no: 'いいえ'
  },

  // ナビゲーション
  nav: {
    home: 'ホーム',
    shop: 'ショップ',
    food: 'フード',
    office: 'オフィス',
    facility: '施設',
    poster: 'ポスター',
    transport: '交通',
    worldTime: '世界時計',
    video: 'ビデオ',
    about: 'について',
    web: 'ウェブ',
    aiSearch: 'AI検索'
  },

  // ページタイトル
  pageTitle: {
    eMap: 'eMap',
    shop: 'ショップ',
    food: 'フード',
    office: 'オフィス',
    facility: '施設',
    poster: 'ポスター',
    transport: '交通',
    worldTime: '世界時計',
    video: 'スポーツ動画',
    about: 'について',
    web: 'ウェブブラウザ',
    airQuality: '大気質指数',
    aiSearch: '検索',
    aiChat: 'AIアシスタント',
    shopDetail: 'ショップ'
  },

  // ショップ関連
  shop: {
    name: 'ショップ名',
    location: '場所',
    hours: '営業時間',
    description: '説明',
    searchPlaceholder: 'ショップ名を検索',
    noResults: 'ショップが見つかりません',
    tryOtherKeywords: '他のキーワードをお試しください',
    startSearch: '検索開始',
    searchPrompt: '上の検索ボックスにショップ名を入力してお探しのショップを見つけてください'
  },

  // オフィス関連
  office: {
    companyName: '会社名',
    roomNumber: '部屋番号',
    floor: '階',
    byFloor: '階別',
    byName: '名前別',
    filterBy: 'フィルター'
  },

  // 施設関連
  facility: {
    men: '男性',
    women: '女性',
    baby: '赤ちゃん',
    services: 'サービス',
    lift: 'エレベーター',
    escalator: 'エスカレーター',
    accessibly: 'アクセシビリティ',
    locker: 'ロッカー'
  },

  // ポスター関連
  poster: {
    title: 'タイトル',
    description: '説明',
    previous: '前へ',
    next: '次へ',
    pause: '一時停止',
    play: '再生',
    autoplay: '自動再生',
    defaultTitle: 'ポスター',
    defaultDescription: '興味深いコンテンツを見る'
  },

  // 交通関連
  transport: {
    bus: 'バス',
    mtr: 'MTR',
    lightRail: 'ライトレール',
    miniBus: 'ミニバス',
    nearby: '近くの交通機関',
    schedule: 'スケジュール',
    route: 'ルート'
  },

  // フード関連
  food: {
    title: 'レストランサービス',
    comingSoon: '近日公開'
  },

  // アバウトページ関連
  about: {
    title: 'eMap AIについて',
    techStack: '技術スタック',
    features: '機能',
    version: 'バージョン情報',
    team: 'チーム情報'
  },

  // 世界時計関連
  worldTime: {
    title: '世界時計',
    realtimeTitle: 'リアルタイム世界時計',
    hongkong: '香港',
    tokyo: '東京',
    newyork: 'ニューヨーク',
    london: 'ロンドン',
    paris: 'パリ',
    sydney: 'シドニー',
    beijing: '北京',
    seoul: 'ソウル',
    dubai: 'ドバイ',
    currentTime: '現在時刻',
    timezone: 'タイムゾーン'
  },

  // ビデオ関連
  video: {
    title: 'タイトル',
    description: '説明',
    duration: '時間',
    category: 'カテゴリ',
    mute: 'ミュート',
    unmute: 'ミュート解除',
    fullscreen: 'フルスクリーン',
    mutedNotice: 'ビデオはデフォルトでミュート再生されます'
  },

  // 天気関連
  weather: {
    temperature: '気温',
    feelsLike: '体感気温',
    humidity: '湿度',
    sunny: '晴れ',
    cloudy: '曇り',
    rainy: '雨',
    snowy: '雪',
    stormy: '嵐'
  },

  // 言語関連
  language: {
    current: '現在の言語',
    switch: '言語を変更',
    traditionalChinese: '繁体字中国語',
    english: '英語',
    spanish: 'スペイン語',
    japanese: '日本語',
    korean: '韓国語',
    thai: 'タイ語',
    short: 'JA'
  },

  // アバウトページ詳細コンテンツ
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - プログレッシブJavaScriptフレームワーク',
      typescript: 'TypeScript - 型システム付きJavaScriptスーパーセット',
      tailwind: 'TailwindCSS - ユーティリティファーストCSSフレームワーク',
      capacitor: 'Capacitor - クロスプラットフォームネイティブアプリビルドツール'
    },
    features: {
      smartNavigation: 'スマートナビゲーションシステム',
      realtimeLocation: 'リアルタイム位置サービス',
      multiLanguage: '多言語サポート',
      crossPlatform: 'クロスプラットフォーム対応'
    },
    version: {
      current: '現在のバージョン: v2.1.0',
      releaseDate: 'リリース日: 2024年',
      updateFrequency: '更新頻度: 月次更新',
      supportedPlatforms: 'サポートプラットフォーム: iOS, Android, Web'
    },
    team: {
      frontend: 'フロントエンド開発: Vue.js + TypeScript',
      mobile: 'モバイル開発: Capacitor クロスプラットフォーム',
      design: 'UI/UXデザイン: モダンガラススタイル',
      data: 'データサポート: リアルタイム同期'
    }
  },

  // 都市名
  cities: {
    hongkong: '香港',
    tokyo: '東京',
    newyork: 'ニューヨーク',
    london: 'ロンドン',
    paris: 'パリ',
    sydney: 'シドニー',
    beijing: '北京',
    seoul: 'ソウル',
    dubai: 'ドバイ',
    losangeles: 'ロサンゼルス'
  },

  // ポスターコンテンツ
  posterContent: {
    splus: {
      title: 'S+ REWARDSメンバー',
      description: '生活にプラスを、今すぐS+ REWARDSメンバーに登録して継続的な驚きと報酬を'
    },
    ikea: {
      title: 'IKEAホームアイデア',
      description: 'HomeSquare IKEA プロモーション活動、見逃せない家具割引'
    },
    more: {
      title: 'その他のプロモーション情報',
      description: 'ショッピングモールのプロモーションやイベント詳細をもっと見る'
    }
  },

  // ビデオ関連
  videoContent: {
    sound: {
      on: '音を有効にする',
      off: '音を無効にする',
      notice: '下のボタンまたはビデオプレーヤーの音アイコンをクリックしてボリュームを有効にしてください'
    },
    videos: {
      basketball: {
        title: 'ホテルスポーツイベント - バスケットボールハイライト',
        description: 'ホテルスポーツバスケットボール大会の興奮する瞬間の振り返り',
        category: 'バスケットボール'
      },
      swimming: {
        title: '水泳競技ハイライト',
        description: '水泳試合の激しい競争と興奮するパフォーマンス',
        category: '水泳'
      },
      tennis: {
        title: 'テニス選手権決勝',
        description: 'テニス選手権決勝の興奮する対戦',
        category: 'テニス'
      }
    }
  },

  // AI検索
  aiSearch: {
    placeholder: 'ショップを検索...'
  },

  // AIチャット
  aiChat: {
    welcomeTitle: 'こんにちは、私はWinnieです！',
    welcomeMessage: 'このショッピングモールのスマートカスタマーサービスアシスタントです。どのようにお手伝いできますか？',
    inputPlaceholder: 'ご質問をご入力ください...',
    listening: '聞いています...',
    sendMessage: '送信',
    voiceInput: '音声入力',
    voiceMessage: '[音声メッセージ]',
    typing: 'Winnieが入力中...',
    error: '申し訳ございません。現在メッセージにお答えできません。後でもう一度お試しください。',
    newChat: '新しいチャット',
    clearChat: 'チャットをクリア',
    recordingGuide: '録音中...お話しください...'
  },

  // Webページ
  web: {
    loading: '読み込み中...',
    error: '読み込みに失敗しました',
    refresh: '更新',
    back: '戻る'
  }
}