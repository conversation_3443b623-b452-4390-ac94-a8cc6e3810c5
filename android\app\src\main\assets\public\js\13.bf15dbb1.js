"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[13],{13:(t,a,e)=>{e.r(a),e.d(a,{default:()=>m});var s=function(){var t=this,a=t._self._c;t._self._setupProxy;return a("div",{staticClass:"food-page responsive-page-container",style:t.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--food"},[a("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.food")))])]),a("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--standard"},[a("div",{staticClass:"food-content"},[a("div",{staticClass:"food-card"},[a("h2",{staticClass:"content-title"},[t._v(t._s(t.$t("food.title")))]),a("div",{staticClass:"features"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("food.comingSoon")))])])])])])]),a("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},o=[],n=e(635),i=e(233),c=e(14),l=e(958),r=e(256),d=e(185),g=e(959);let p=class extends((0,i.Xe)(d.A,g.A)){getBackgroundColor(){return"#DC2626"}};p=(0,n.Cg)([(0,i.uA)({components:{TopBar:c.A,BottomBar:l.A,BackgroundImage:r.A}})],p);const u=p,C=u;var f=e(656),v=(0,f.A)(C,s,o,!1,null,"285191ca",null);const m=v.exports}}]);