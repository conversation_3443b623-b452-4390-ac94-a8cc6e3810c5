"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[521],{2521:(t,e,i)=>{i.r(e),i.d(e,{default:()=>F});var o=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"web2-container responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.airQuality")))])]),e("div",{staticClass:"content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content"},[e("div",{staticClass:"chart-container"},[e("h3",{staticClass:"chart-title"},[t._v("空氣質量指數趨勢")]),e("canvas",{ref:"lineChart",attrs:{id:"lineChart"}})]),e("div",{staticClass:"chart-container"},[e("h3",{staticClass:"chart-title"},[t._v("污染物濃度")]),e("canvas",{ref:"barChart",attrs:{id:"barChart"}})])])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),e("BottomMarquee")],1)},a=[],s=i(1635),r=i(9603),n=i(3452),l=i(3205),h=i(4184),c=i(256),d=i(5185),C=i(7959);let g=null,p=class extends((0,r.Xe)(d.A,C.A)){lineChart=null;barChart=null;getBackgroundColor(){return"#1a1a2e"}async mounted(){try{await this.loadChartJS(),this.initCharts(),window.addEventListener("resize",this.handleResize)}catch(t){console.error("Failed to load Chart.js:",t)}}beforeDestroy(){this.lineChart&&this.lineChart.destroy(),this.barChart&&this.barChart.destroy(),window.removeEventListener("resize",this.handleResize)}async loadChartJS(){if("undefined"!==typeof window&&!g)return new Promise((t,e)=>{const i=document.createElement("script");i.src="https://cdn.jsdelivr.net/npm/chart.js",i.onload=()=>{g=window.Chart,t(g)},i.onerror=e,document.head.appendChild(i)})}initCharts(){g&&(this.initLineChart(),this.initBarChart())}getResponsiveFontSize(t){const e=window.innerWidth,i=Math.max(.5,Math.min(2.5,e/1920));return Math.round(t*i*3)}handleResize=()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.lineChart&&this.barChart&&(this.lineChart.destroy(),this.barChart.destroy(),this.initCharts())},300)};resizeTimeout=null;initLineChart(){const t=this.$refs.lineChart.getContext("2d");t&&(this.lineChart=new g(t,{type:"line",data:{labels:["00:00","04:00","08:00","12:00","16:00","20:00","24:00"],datasets:[{label:"空氣質量指數 (AQI)",data:[50,70,90,120,100,80,60],borderColor:"#00EEFF",backgroundColor:"rgba(0, 238, 255, 0.2)",fill:!0,tension:.4,pointBackgroundColor:"#00EEFF",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"#00EEFF"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(14)}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#00EEFF",bodyColor:"#00EEFF",titleFont:{size:this.getResponsiveFontSize(14)},bodyFont:{size:this.getResponsiveFontSize(12)}}},scales:{x:{ticks:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(12)}},grid:{color:"rgba(255, 255, 255, 0.1)"}},y:{ticks:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(12)}},grid:{color:"rgba(255, 255, 255, 0.1)"}}}}}))}initBarChart(){const t=this.$refs.barChart.getContext("2d");t&&(this.barChart=new g(t,{type:"bar",data:{labels:["PM2.5","PM10","NO2","SO2","O3"],datasets:[{label:"污染物濃度 (μg/m³)",data:[35,50,40,20,60],backgroundColor:["rgba(0, 238, 255, 0.6)","rgba(0, 204, 255, 0.6)","rgba(255, 102, 204, 0.6)","rgba(255, 204, 0, 0.6)","rgba(102, 255, 102, 0.6)"],borderColor:"#fff",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(16)}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#00EEFF",bodyColor:"#00EEFF",titleFont:{size:this.getResponsiveFontSize(16)},bodyFont:{size:this.getResponsiveFontSize(16)}}},scales:{x:{ticks:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(16)}},grid:{color:"rgba(255, 255, 255, 0.1)"}},y:{ticks:{color:"#e0e0e0",font:{size:this.getResponsiveFontSize(14)}},grid:{color:"rgba(255, 255, 255, 0.1)"}}}}}))}};p=(0,s.Cg)([(0,r.uA)({components:{TopBar:n.A,BottomBar:l.A,BottomMarquee:h.A,BackgroundImage:c.A}})],p);const u=p,b=u;var v=i(1656),f=(0,v.A)(b,o,a,!1,null,"5e848029",null);const F=f.exports}}]);