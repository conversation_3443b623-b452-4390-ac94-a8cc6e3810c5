/**
 * 性能监控工具 - 帮助诊断APK性能问题
 */

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  scrollPerformance: number
  audioLatency: number
  renderTime: number
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private isMonitoring = false
  private metrics: PerformanceMetrics = {
    fps: 0,
    memoryUsage: 0,
    scrollPerformance: 0,
    audioLatency: 0,
    renderTime: 0
  }

  private frameCount = 0
  private lastTime = performance.now()
  private animationId: number | null = null

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  public startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.monitorFPS()
    this.monitorMemory()
    this.monitorScrollPerformance()

    console.log('🔍 性能监控已启动')
  }

  public stopMonitoring(): void {
    this.isMonitoring = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    console.log('⏹️ 性能监控已停止')
  }

  private monitorFPS(): void {
    const measureFPS = () => {
      if (!this.isMonitoring) return

      const currentTime = performance.now()
      this.frameCount++

      if (currentTime - this.lastTime >= 1000) {
        this.metrics.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime))
        this.frameCount = 0
        this.lastTime = currentTime

        // 如果FPS过低，输出警告
        if (this.metrics.fps < 30) {
          console.warn(`⚠️ FPS过低: ${this.metrics.fps}`)
        }
      }

      this.animationId = requestAnimationFrame(measureFPS)
    }

    measureFPS()
  }

  private monitorMemory(): void {
    if ('memory' in performance) {
      setInterval(() => {
        if (!this.isMonitoring) return

        const memory = (performance as any).memory
        this.metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024)

        // 如果内存使用过高，输出警告
        if (this.metrics.memoryUsage > 100) {
          console.warn(`⚠️ 内存使用过高: ${this.metrics.memoryUsage}MB`)
        }
      }, 5000)
    }
  }

  private monitorScrollPerformance(): void {
    let scrollStartTime = 0
    let isScrolling = false

    const onScrollStart = () => {
      if (!isScrolling) {
        scrollStartTime = performance.now()
        isScrolling = true
      }
    }

    const onScrollEnd = () => {
      if (isScrolling) {
        const scrollDuration = performance.now() - scrollStartTime
        this.metrics.scrollPerformance = scrollDuration

        if (scrollDuration > 16) { // 超过一帧的时间
          console.warn(`⚠️ 滚动性能问题: ${scrollDuration.toFixed(2)}ms`)
        }

        isScrolling = false
      }
    }

    // 监听滚动事件
    document.addEventListener('scroll', onScrollStart, { passive: true })
    document.addEventListener('scrollend', onScrollEnd, { passive: true })

    // 备用方案：使用定时器检测滚动结束
    let scrollTimer: number | null = null
    document.addEventListener('scroll', () => {
      onScrollStart()

      if (scrollTimer) {
        clearTimeout(scrollTimer)
      }

      scrollTimer = window.setTimeout(onScrollEnd, 150)
    }, { passive: true })
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  public logMetrics(): void {
    console.group('📊 性能指标')
    console.log(`FPS: ${this.metrics.fps}`)
    console.log(`内存使用: ${this.metrics.memoryUsage}MB`)
    console.log(`滚动性能: ${this.metrics.scrollPerformance.toFixed(2)}ms`)
    console.log(`音频延迟: ${this.metrics.audioLatency.toFixed(2)}ms`)
    console.log(`渲染时间: ${this.metrics.renderTime.toFixed(2)}ms`)
    console.groupEnd()
  }

  public measureAudioLatency(): void {
    const startTime = performance.now()

    // 模拟音频播放测量
    setTimeout(() => {
      this.metrics.audioLatency = performance.now() - startTime
    }, 0)
  }

  public measureRenderTime(callback: () => void): void {
    const startTime = performance.now()

    requestAnimationFrame(() => {
      callback()
      requestAnimationFrame(() => {
        this.metrics.renderTime = performance.now() - startTime
      })
    })
  }

  public generateReport(): string {
    const report = `
性能报告 (${new Date().toLocaleString()})
=====================================
FPS: ${this.metrics.fps} ${this.metrics.fps < 30 ? '❌' : '✅'}
内存使用: ${this.metrics.memoryUsage}MB ${this.metrics.memoryUsage > 100 ? '❌' : '✅'}
滚动性能: ${this.metrics.scrollPerformance.toFixed(2)}ms ${this.metrics.scrollPerformance > 16 ? '❌' : '✅'}
音频延迟: ${this.metrics.audioLatency.toFixed(2)}ms ${this.metrics.audioLatency > 100 ? '❌' : '✅'}
渲染时间: ${this.metrics.renderTime.toFixed(2)}ms ${this.metrics.renderTime > 16 ? '❌' : '✅'}

建议:
${this.metrics.fps < 30 ? '- 优化动画和过渡效果\n' : ''}
${this.metrics.memoryUsage > 100 ? '- 检查内存泄漏\n' : ''}
${this.metrics.scrollPerformance > 16 ? '- 优化滚动容器\n' : ''}
${this.metrics.audioLatency > 100 ? '- 优化音频系统\n' : ''}
${this.metrics.renderTime > 16 ? '- 减少DOM复杂度\n' : ''}
`
    return report
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 开发环境自动启动监控
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.startMonitoring()

  // 每30秒输出一次性能指标
  setInterval(() => {
    performanceMonitor.logMetrics()
  }, 30000)
}