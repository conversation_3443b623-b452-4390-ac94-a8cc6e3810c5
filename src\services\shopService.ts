import axios, { AxiosResponse } from 'axios'

export interface ShopApiData {
  id: number
  documentId: string
  name: string
  name_tc: string
  name_zh: string
  shopno: string
  openinghours: string
  tel: string
  image_url?: string
  logo_url?: string
  search_index?: string | null
  createdAt: string
  updatedAt: string
  publishedAt: string
  desc: string
}

export interface ShopApiResponse {
  data: ShopApiData[]
}

export interface ShopData {
  id: string
  name: string
  name_tc: string
  name_zh: string
  location: string
  hours: string
  tel: string
  description: string
  logo?: string
}

export interface ShopBasicInfo {
  id: string
  name: string
  name_tc: string
  name_zh: string
  logo?: string
}

const API_BASE_URL = 'https://testapi.bwaiwork.xyz/api'
const SHOPS_ENDPOINT = '/eshowcaseshops'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 转换API数据为内部数据格式
const transformShopData = (apiData: ShopApiData): ShopData => {
  return {
    id: apiData.documentId,
    name: apiData.name,
    name_tc: apiData.name_tc,
    name_zh: apiData.name_zh,
    location: apiData.shopno,
    hours: apiData.openinghours,
    tel: apiData.tel,
    description: apiData.desc,
    logo: apiData.logo_url || undefined // 优先使用logo_url，没有就不设置，使用占位符
  }
}

// 转换为基本信息格式
const transformShopBasicInfo = (apiData: ShopApiData): ShopBasicInfo => {
  return {
    id: apiData.documentId,
    name: apiData.name,
    name_tc: apiData.name_tc,
    name_zh: apiData.name_zh,
    logo: apiData.logo_url || undefined // 优先使用logo_url，没有就不设置，使用占位符
  }
}

/**
 * 获取所有商店数据
 */
export const fetchShopsData = async (): Promise<ShopData[]> => {
  try {
    console.log('正在获取商店数据...')
    const response: AxiosResponse<ShopApiResponse> = await apiClient.get(SHOPS_ENDPOINT)
    
    if (!response.data || !response.data.data) {
      throw new Error('API响应格式错误')
    }
    
    const shops = response.data.data.map(transformShopData)
    console.log(`成功获取${shops.length}个商店数据`)
    return shops
  } catch (error) {
    console.error('获取商店数据失败:', error)
    throw error
  }
}

/**
 * 获取所有商店基本信息（用于列表显示）
 */
export const fetchShopsBasicInfo = async (): Promise<ShopBasicInfo[]> => {
  try {
    const response: AxiosResponse<ShopApiResponse> = await apiClient.get(SHOPS_ENDPOINT)
    
    if (!response.data || !response.data.data) {
      throw new Error('API响应格式错误')
    }
    
    return response.data.data.map(transformShopBasicInfo)
  } catch (error) {
    console.error('获取商店基本信息失败:', error)
    throw error
  }
}

/**
 * 根据ID获取单个商店数据
 */
export const fetchShopData = async (shopId: string): Promise<ShopData | null> => {
  try {
    const shops = await fetchShopsData()
    return shops.find(shop => shop.id === shopId) || null
  } catch (error) {
    console.error(`获取商店${shopId}数据失败:`, error)
    return null
  }
}

/**
 * 检查商店ID是否有效
 */
export const isValidShopId = async (shopId: string): Promise<boolean> => {
  try {
    const shop = await fetchShopData(shopId)
    return shop !== null
  } catch (error) {
    console.error(`验证商店ID ${shopId} 失败:`, error)
    return false
  }
}

// 缓存管理
class ShopCache {
  private static instance: ShopCache
  private cache: ShopData[] | null = null
  private cacheTime: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  static getInstance(): ShopCache {
    if (!ShopCache.instance) {
      ShopCache.instance = new ShopCache()
    }
    return ShopCache.instance
  }

  async getShops(): Promise<ShopData[]> {
    const now = Date.now()
    
    // 检查缓存是否有效
    if (this.cache && (now - this.cacheTime) < this.CACHE_DURATION) {
      console.log('使用缓存的商店数据')
      return this.cache
    }
    
    // 重新获取数据
    try {
      this.cache = await fetchShopsData()
      this.cacheTime = now
      return this.cache
    } catch (error) {
      // 如果有旧的缓存数据，在网络错误时返回旧数据
      if (this.cache) {
        console.log('网络错误，使用旧的缓存数据')
        return this.cache
      }
      throw error
    }
  }

  async getShopBasicInfo(): Promise<ShopBasicInfo[]> {
    const shops = await this.getShops()
    return shops.map(shop => ({
      id: shop.id,
      name: shop.name,
      name_tc: shop.name_tc,
      name_zh: shop.name_zh,
      logo: shop.logo
    }))
  }

  async getShopById(shopId: string): Promise<ShopData | null> {
    const shops = await this.getShops()
    return shops.find(shop => shop.id === shopId) || null
  }

  clearCache(): void {
    this.cache = null
    this.cacheTime = 0
  }
}

// 导出缓存实例方法
const shopCache = ShopCache.getInstance()

export const getShopsData = () => shopCache.getShops()
export const getShopsBasicInfo = () => shopCache.getShopBasicInfo()
export const getShopData = (shopId: string) => shopCache.getShopById(shopId)
export const clearShopCache = () => shopCache.clearCache()