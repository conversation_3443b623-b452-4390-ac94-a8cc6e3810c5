(()=>{"use strict";var e={14:(e,t,o)=>{o.d(t,{A:()=>f});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"top-bar"},[e._m(0),t("div",{staticClass:"datetime-section"},[e._v(" "+e._s(e.currentDateTime)+" ")]),t("div",{staticClass:"weather-section"},[t("div",{staticClass:"weather-icon"},[e._v(e._s(e.weatherIcon))]),t("div",{staticClass:"weather-temp"},[e._v(e._s(e.temperature)+"°C")])])])},n=[function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"logo-section"},[t("img",{staticClass:"logo-image",attrs:{src:"/img/logo.png",alt:"Logo"}})])}],i=o(635),r=o(233);class s{baseUrl="https://wttr.in";cacheKey="hk_weather_cache";cacheTimestampKey="hk_weather_timestamp";cacheValidTime=6e5;async getHongKongWeather(){try{const e=this.getCachedWeather();if(e)return e;const t=await this.fetchWeatherData();return t?(this.saveToCache(t),t):this.getDefaultWeather()}catch(e){return this.getDefaultWeather()}}async fetchWeatherData(){try{const e=`${this.baseUrl}/Hong+Kong?format=j1`,t=new AbortController,o=setTimeout(()=>{t.abort()},15e3),a=await fetch(e,{method:"GET",signal:t.signal,headers:{Accept:"application/json","User-Agent":"Mozilla/5.0 (compatible; eMap App)"}});if(clearTimeout(o),!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const n=await a.json(),i=n.current_condition[0],r=i.weatherDesc[0].value,s=parseInt(i.temp_C),c=parseInt(i.humidity),l=parseInt(i.FeelsLikeC),u=i.weatherCode,d=this.mapWeatherCodeToIcon(u);return{temperature:s,weatherMain:this.getWeatherMain(u),weatherDescription:r,icon:d,humidity:c,feels_like:l,city:"香港"}}catch(e){return null}}getCachedWeather(){try{const e=localStorage.getItem(this.cacheKey),t=localStorage.getItem(this.cacheTimestampKey);if(!e||!t)return null;const o=Date.now()-parseInt(t);return o>this.cacheValidTime?(this.clearCache(),null):JSON.parse(e)}catch(e){return this.clearCache(),null}}saveToCache(e){try{localStorage.setItem(this.cacheKey,JSON.stringify(e)),localStorage.setItem(this.cacheTimestampKey,Date.now().toString())}catch(t){}}clearCache(){try{localStorage.removeItem(this.cacheKey),localStorage.removeItem(this.cacheTimestampKey)}catch(e){}}getDefaultWeather(){return{temperature:25,weatherMain:"Clouds",weatherDescription:"多雲",icon:"02d",humidity:70,feels_like:28,city:"香港"}}mapWeatherCodeToIcon(e){const t={113:"01d",116:"02d",119:"03d",122:"04d",143:"50d",176:"10d",179:"13d",182:"13d",185:"13d",200:"11d",227:"13d",230:"13d",248:"50d",260:"50d",263:"09d",266:"09d",281:"13d",284:"13d",293:"10d",296:"10d",299:"09d",302:"09d",305:"09d",308:"09d",311:"13d",314:"13d",317:"13d",320:"13d",323:"13d",326:"13d",329:"13d",332:"13d",335:"13d",338:"13d",350:"13d",353:"10d",356:"09d",359:"09d",362:"13d",365:"13d",368:"13d",371:"13d",374:"13d",377:"13d",386:"11d",389:"11d",392:"11d",395:"11d"};return t[e]||"02d"}getWeatherMain(e){const t=parseInt(e);return 113===t?"Clear":t>=116&&t<=119||t>=122&&t<=122?"Clouds":t>=143&&t<=260?"Mist":t>=263&&t<=320?"Rain":t>=323&&t<=395?"Snow":t>=386&&t<=395?"Thunderstorm":"Clouds"}getWeatherIcon(e){const t={"01d":"☀️","01n":"🌙","02d":"⛅","02n":"☁️","03d":"☁️","03n":"☁️","04d":"☁️","04n":"☁️","09d":"🌧️","09n":"🌧️","10d":"🌦️","10n":"🌦️","11d":"⛈️","11n":"⛈️","13d":"❄️","13n":"❄️","50d":"🌫️","50n":"🌫️"};return t[e]||"🌤️"}async forceRefresh(){return this.clearCache(),await this.getHongKongWeather()}}const c=new s,l=c;var u=o(959);let d=class extends((0,r.Xe)(u.A)){currentDateTime="";weatherData=null;weatherUpdateInterval=null;get temperature(){return this.weatherData?.temperature||25}get weatherIcon(){return this.weatherData?l.getWeatherIcon(this.weatherData.icon):"🌤️"}mounted(){this.updateDateTime(),this.loadWeatherData(),setInterval(this.updateDateTime,1e3),this.weatherUpdateInterval=setInterval(this.loadWeatherData,6e5),this.$root.$on("language-changed",this.updateDateTime)}beforeDestroy(){this.weatherUpdateInterval&&clearInterval(this.weatherUpdateInterval),this.$root.$off("language-changed",this.updateDateTime)}updateDateTime(){const e=new Date;let t;switch(this.$currentLanguage){case"en":t={year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0};break;case"es":t={year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1};break;case"zh-TW":default:t={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1};break}const o=this.getLocaleForLanguage();this.currentDateTime=e.toLocaleString(o,t)}getLocaleForLanguage(){switch(this.$currentLanguage){case"en":return"en-US";case"es":return"es-ES";case"zh-TW":default:return"zh-TW"}}async loadWeatherData(){try{this.weatherData=await l.getHongKongWeather()}catch(e){}}};d=(0,i.Cg)([r.uA],d);const g=d,m=g;var h=o(656),p=(0,h.A)(m,a,n,!1,null,"7caba744",null);const f=p.exports},185:(e,t,o)=>{o.d(t,{A:()=>r});var a=o(635),n=o(233);let i=class extends n.lD{scaleRatio=1;mounted(){this.calculateScale(),window.addEventListener("resize",this.calculateScale)}beforeDestroy(){window.removeEventListener("resize",this.calculateScale)}calculateScale(){const e=window.innerWidth,t=window.innerHeight,o=2160;this.scaleRatio=e/o,console.log(`[ResponsiveMixin] 缩放计算: 屏幕尺寸${e}×${t}px, 缩放比例${this.scaleRatio}`)}get containerStyle(){const e=100/this.scaleRatio+"vh";return{transform:`scale(${this.scaleRatio})`,transformOrigin:"top left",width:"2160px",height:e,position:"relative",overflow:"hidden",fontFamily:"Inter, sans-serif",backgroundColor:this.getBackgroundColor()}}getBackgroundColor(){return"#016513"}get contentAreaStyle(){const e=200,t=155,o=340-e,a=523,n=e+o+t+140,i=60;return{position:"absolute",top:`${n}px`,left:"0",right:"0",bottom:`${a+i}px`,overflowY:"auto",overflowX:"hidden",zIndex:10}}onHomeClick(){"/"!==this.$route.path&&this.$router.push("/")}onLanguageChange(e){console.log("语言切换到:",e)}onAIClick(){console.log("打开AI助手"),this.$router.push("/AIChat")}};i=(0,a.Cg)([n.uA],i);const r=i},256:(e,t,o)=>{o.d(t,{A:()=>m});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"background-image",class:{"with-offset":e.hasOffset},style:e.backgroundStyle})},n=[],i=o(635),r=o(233),s=o(353);let c=class extends r.lD{opacity;hasOffset;backgroundImageUrl;hasConfig;get backgroundStyle(){const e=this.getBackgroundImageUrl();return{backgroundImage:`url('${e}')`,backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",opacity:this.opacity}}getBackgroundImageUrl(){return this.hasConfig&&this.backgroundImageUrl?(console.log("使用远程背景图片:",this.backgroundImageUrl),this.backgroundImageUrl):(console.log("使用本地默认背景图片"),"/img/bg.png")}};(0,i.Cg)([(0,r.kv)({default:.9})],c.prototype,"opacity",void 0),(0,i.Cg)([(0,r.kv)({default:!1})],c.prototype,"hasOffset",void 0),c=(0,i.Cg)([(0,r.uA)({computed:{...(0,s.L8)("config",["backgroundImageUrl","hasConfig"])}})],c);const l=c,u=l;var d=o(656),g=(0,d.A)(u,a,n,!1,null,"4665a938",null);const m=g.exports},296:(e,t,o)=>{o.d(t,{A:()=>i});class a{baseUrl="https://testapi.bwaiwork.xyz";cacheKey="emap_config_cache";cacheTimestampKey="emap_config_timestamp";cacheValidTime=864e5;createTimeoutController(e){const t=new AbortController,o=setTimeout(()=>{t.abort()},e),a=t.abort.bind(t);return t.abort=()=>{clearTimeout(o),a()},t}async getConfig(){try{const e=await this.fetchRemoteConfig();if(e)return this.saveToCache(e),e}catch(t){console.warn("获取远程配置失败，尝试使用缓存:",t)}const e=this.getCachedConfig();return e?(console.log("使用缓存配置"),e):(console.error("无法获取配置数据（远程和缓存都失败）"),null)}async fetchRemoteConfig(){const e=this.createTimeoutController(1e4);try{const t=await fetch(`${this.baseUrl}/api/emap-setting?populate=*`,{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=await t.json();return console.log("成功获取远程配置"),o.data}catch(t){if(t instanceof Error&&"AbortError"===t.name)throw console.error("请求超时"),new Error("网络请求超时");throw console.error("获取远程配置时出错:",t),t}finally{e.abort()}}getCachedConfig(){try{const e=localStorage.getItem(this.cacheKey),t=localStorage.getItem(this.cacheTimestampKey);if(!e||!t)return null;const o=parseInt(t,10),a=Date.now(),n=a-o>this.cacheValidTime;return n?(console.log("缓存已过期"),this.clearCache(),null):JSON.parse(e)}catch(e){return console.error("读取缓存时出错:",e),this.clearCache(),null}}saveToCache(e){try{localStorage.setItem(this.cacheKey,JSON.stringify(e)),localStorage.setItem(this.cacheTimestampKey,Date.now().toString()),console.log("配置已保存到缓存")}catch(t){console.error("保存缓存时出错:",t)}}clearCache(){try{localStorage.removeItem(this.cacheKey),localStorage.removeItem(this.cacheTimestampKey),console.log("缓存已清除")}catch(e){console.error("清除缓存时出错:",e)}}buildImageUrl(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:`${this.baseUrl}${e}`:""}getBestImageUrl(e,t="large"){return e?e.formats&&e.formats[t]?this.buildImageUrl(e.formats[t].url):this.buildImageUrl(e.url):""}async forceRefresh(){return this.clearCache(),this.getConfig()}async checkNetworkConnection(){try{const e=this.createTimeoutController(3e3),t=await fetch(`${this.baseUrl}/api/health`,{method:"HEAD",signal:e.signal});return t.ok}catch(e){return console.warn("网络连接检查失败:",e),!1}}getCacheStatus(){const e=localStorage.getItem(this.cacheTimestampKey),t=!!localStorage.getItem(this.cacheKey)&&!!e;if(!t)return{hasCache:!1,cacheAge:0,isExpired:!0};const o=parseInt(e,10),a=Date.now()-o,n=a>this.cacheValidTime;return{hasCache:t,cacheAge:a,isExpired:n}}}const n=new a,i=n},317:(e,t,o)=>{o.d(t,{A:()=>g});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("button",{staticClass:"glass-button",class:[e.sizeClass,e.variantClass,{"glass-button-active":e.active}],on:{click:function(t){return e.$emit("click")},mousedown:function(t){return e.$emit("mousedown",t)},mouseup:function(t){return e.$emit("mouseup",t)},mouseleave:function(t){return e.$emit("mouseleave",t)},touchstart:function(t){return e.$emit("touchstart",t)},touchend:function(t){return e.$emit("touchend",t)},touchcancel:function(t){return e.$emit("touchcancel",t)}}},[e._t("default")],2)},n=[],i=o(635),r=o(233);let s=class extends r.lD{size;variant;active;get sizeClass(){const e={small:"small-btn",medium:"medium-btn",large:"large-btn"};return e[this.size]}get variantClass(){const e={normal:"glass-normal",highlight:"glass-highlight"};return e[this.variant]}};(0,i.Cg)([(0,r.kv)({default:"medium"})],s.prototype,"size",void 0),(0,i.Cg)([(0,r.kv)({default:"normal"})],s.prototype,"variant",void 0),(0,i.Cg)([(0,r.kv)({default:!1})],s.prototype,"active",void 0),s=(0,i.Cg)([r.uA],s);const c=s,l=c;var u=o(656),d=(0,u.A)(l,a,n,!1,null,"2a84b44f",null);const g=d.exports},636:(e,t,o)=>{o.d(t,{A5:()=>s,Ay:()=>c,Fc:()=>a,Nm:()=>n});const a=[{code:"zh-TW",name:"Traditional Chinese",nativeName:"繁體中文"},{code:"en",name:"English",nativeName:"English"},{code:"es",name:"Spanish",nativeName:"Español"},{code:"ja",name:"Japanese",nativeName:"日本語"},{code:"ko",name:"Korean",nativeName:"한국어"},{code:"th",name:"Thai",nativeName:"ภาษาไทย"}],n="zh-TW",i="emap_language";class r{currentLanguage=n;translations={};constructor(){this.loadLanguage()}loadLanguage(){try{const e=localStorage.getItem(i);e&&this.isValidLanguage(e)&&(this.currentLanguage=e)}catch(e){console.warn("無法載入語言設置，使用預設語言")}}isValidLanguage(e){return a.some(t=>t.code===e)}setTranslations(e,t){this.translations[e]=t}getCurrentLanguage(){return this.currentLanguage}setCurrentLanguage(e){if(this.isValidLanguage(e)){this.currentLanguage=e;try{localStorage.setItem(i,e)}catch(t){console.warn("無法儲存語言設置")}}}t(e,t){const o=this.getNestedTranslation(e);return o||t||e}getNestedTranslation(e){const t=e.split(".");let o=this.translations[this.currentLanguage];if(!o)return console.warn(`翻譯包未加載: ${this.currentLanguage}`),null;for(const a of t){if(!o||"object"!==typeof o||!(a in o))return null;o=o[a]}return"string"===typeof o?o:null}getLanguageConfig(e){const t=e||this.currentLanguage;return a.find(e=>e.code===t)||null}getSupportedLanguages(){return a}}const s=new r,c=s},939:(e,t,o)=>{var a=o(471),n=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},i=[],r=o(635),s=o(233);let c=class extends s.lD{};c=(0,r.Cg)([s.uA],c);const l=c,u=l;var d=o(656),g=(0,d.A)(u,n,i,!1,null,null,null);const m=g.exports;var h=o(173),p=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"home-container responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--home"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.eMap")))])]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--home"},[t("div",{staticClass:"function-grid"},[t("GlassButton",{attrs:{size:"medium",variant:"highlight"},on:{click:function(t){return e.navigateTo("shop")}}},[e._v(" "+e._s(e.$t("nav.shop"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"normal"},on:{click:function(t){return e.navigateTo("food")}}},[e._v(" "+e._s(e.$t("nav.food"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"highlight"},on:{click:function(t){return e.navigateTo("office")}}},[e._v(" "+e._s(e.$t("nav.office"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"normal"},on:{click:function(t){return e.navigateTo("facility")}}},[e._v(" "+e._s(e.$t("nav.facility"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"normal"},on:{click:function(t){return e.navigateTo("poster")}}},[e._v(" "+e._s(e.$t("nav.poster"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"normal"},on:{click:function(t){return e.navigateTo("transport")}}},[e._v(" "+e._s(e.$t("nav.transport"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"normal"},on:{click:function(t){return e.navigateTo("web")}}},[e._v(" "+e._s(e.$t("nav.web"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"highlight"},on:{click:function(t){return e.navigateTo("video")}}},[e._v(" "+e._s(e.$t("nav.video"))+" ")]),t("GlassButton",{attrs:{size:"medium",variant:"highlight"},on:{click:function(t){return e.navigateTo("worldtime")}}},[t("span",{staticClass:"text-center"},[e._v(" "+e._s(e.$t("nav.worldTime"))+" ")])])],1)])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}})],1)},f=[],y=o(317),v=o(14),b=o(958),w=o(256),C=o(185),S=o(959),T=o(546);let k=class extends((0,s.Xe)(C.A,S.A)){getBackgroundColor(){return"#016513"}async navigateTo(e){if(console.log("導航到:",e),"web"===e)if(T.Ii.isNativePlatform())try{console.log("=== 使用簡化WebView方案 ==="),console.log("平台:",T.Ii.getPlatform()),console.log("嘗試觸發自定義URL scheme..."),window.location.href="webview://hongkongairport",console.log("URL scheme已觸發")}catch(t){console.error("Intent調用失敗，嘗試其他方案:",t);try{window.location.href="webview://open?url="+encodeURIComponent("https://www.hongkongairport.com/en/flights/arrivals/passenger.page")}catch(o){console.error("URL scheme也失敗:",o),alert("無法打開WebView")}}else window.open("https://www.hongkongairport.com/en/flights/arrivals/passenger.page","_blank");else this.$router.push(`/${e}`)}};k=(0,r.Cg)([(0,s.uA)({components:{GlassButton:y.A,TopBar:v.A,BottomBar:b.A,BackgroundImage:w.A}})],k);const A=k,I=A;var L=(0,d.A)(I,p,f,!1,null,"44a8acf5",null);const E=L.exports;a["default"].use(h.Ay);const D=[{path:"/",name:"Home",component:E},{path:"/about",name:"About",component:()=>o.e(126).then(o.bind(o,126))},{path:"/shop",name:"Shop",component:()=>o.e(576).then(o.bind(o,576))},{path:"/shop/:id",name:"ShopDetail",component:()=>o.e(654).then(o.bind(o,654))},{path:"/food",name:"Food",component:()=>o.e(13).then(o.bind(o,13))},{path:"/office",name:"Office",component:()=>o.e(325).then(o.bind(o,325))},{path:"/facility",name:"Facility",component:()=>o.e(367).then(o.bind(o,367))},{path:"/transport",name:"Transport",component:()=>o.e(918).then(o.bind(o,918))},{path:"/video",name:"Video",component:()=>o.e(78).then(o.bind(o,78))},{path:"/worldtime",name:"WorldTime",component:()=>o.e(253).then(o.bind(o,253))},{path:"/poster",name:"Poster",component:()=>Promise.all([o.e(121),o.e(968)]).then(o.bind(o,968))},{path:"/ai-search",name:"AISearch",component:()=>o.e(103).then(o.bind(o,103))},{path:"/ai-chat",name:"AIChat",component:()=>Promise.all([o.e(121),o.e(695)]).then(o.bind(o,695))}],$=new h.Ay({mode:"hash",base:"",routes:D}),_=$;var N=o(353),R=o(296);const P={namespaced:!0,state:()=>({config:null,loading:!1,error:null,lastUpdateTime:null}),getters:{baseColor:e=>e.config?.baseColor||"#016513",videos:e=>e.config?.video||[],backgroundImage:e=>e.config?.backgrondImage||null,posters:e=>e.config?.poster||[],backgroundImageUrl:(e,t)=>{const o=t.backgroundImage;return o?R.A.getBestImageUrl(o,"large"):""},firstVideo:(e,t)=>{const o=t.videos;return o.length>0?o[0]:null},secondVideo:(e,t)=>{const o=t.videos;return o.length>1?o[1]:null},hasConfig:e=>!!e.config,isLoading:e=>e.loading,errorMessage:e=>e.error},mutations:{SET_LOADING(e,t){e.loading=t},SET_CONFIG(e,t){e.config=t,e.error=null,e.lastUpdateTime=Date.now()},SET_ERROR(e,t){e.error=t,e.loading=!1},CLEAR_ERROR(e){e.error=null},UPDATE_BASE_COLOR(e,t){e.config&&(e.config.baseColor=t)}},actions:{async initConfig({commit:e,dispatch:t}){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const o=await R.A.getConfig();o?(e("SET_CONFIG",o),t("applyBaseColor",o.baseColor),console.log("配置初始化成功")):e("SET_ERROR","无法获取配置数据")}catch(o){console.error("初始化配置失败:",o),e("SET_ERROR",o instanceof Error?o.message:"初始化配置失败")}finally{e("SET_LOADING",!1)}},async refreshConfig({commit:e,dispatch:t}){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const o=await R.A.forceRefresh();o?(e("SET_CONFIG",o),t("applyBaseColor",o.baseColor),console.log("配置刷新成功")):e("SET_ERROR","无法刷新配置数据")}catch(o){console.error("刷新配置失败:",o),e("SET_ERROR",o instanceof Error?o.message:"刷新配置失败")}finally{e("SET_LOADING",!1)}},applyBaseColor({commit:e},t){try{document.documentElement.style.setProperty("--color-background",t);const o=document.getElementById("dynamic-background-colors");o&&o.remove();const a=document.createElement("style");a.id="dynamic-background-colors",a.innerHTML=`\n          .responsive-page-container {\n            background-color: ${t} !important;\n          }\n        `,document.head.appendChild(a),e("UPDATE_BASE_COLOR",t),console.log("基础颜色已应用到responsive-page-container:",t)}catch(o){console.error("应用基础颜色失败:",o)}},getPosterUrl({state:e},t){return e.config?.poster&&e.config.poster[t]?R.A.getBestImageUrl(e.config.poster[t],"large"):""}}},M=P;a["default"].use(N.Ay);const B=new N.Ay.Store({state:{},mutations:{},actions:{},modules:{config:M}});var W=o(636);function x(){if("undefined"===typeof window||!window.navigator)return null;const e=window.navigator.language;return e||(window.navigator.userLanguage||window.navigator.browserLanguage||null)}function O(e){if(!e)return null;const t=e.toLowerCase(),o={zh:"zh-TW","zh-tw":"zh-TW","zh-hk":"zh-TW","zh-hant":"zh-TW","zh-cn":"zh-TW","zh-hans":"zh-TW",en:"en","en-us":"en","en-gb":"en","en-au":"en","en-ca":"en",es:"es","es-es":"es","es-mx":"es","es-ar":"es","es-co":"es"};if(o[t])return o[t];const a=t.split("-")[0];if(o[a])return o[a];const n=W.Fc.find(e=>e.code.toLowerCase()===t);return n?n.code:null}function z(e){const t=localStorage.getItem("emap_language");if(t&&W.Fc.some(e=>e.code===t))return console.log("使用已儲存的語言:",t),W.A5.setCurrentLanguage(t),e&&e.$root&&e.$root.$emit("language-changed",t),t;const o=x();console.log("檢測到瀏覽器語言:",o);const a=O(o||"");console.log("映射後的語言:",a);const n=a||W.Nm;return W.A5.setCurrentLanguage(n),e&&e.$root&&(e.$root.$emit("language-changed",n),e.$root.$forceUpdate(),setTimeout(()=>{e.$root.$emit("language-changed",n)},100)),console.log("已設置全局語言為:",n),n}function U(){const e=x(),t=O(e||""),o=W.A5.getCurrentLanguage();return{browserLanguage:e,detectedLanguage:t||W.Nm,currentLanguage:o,isAutoDetected:!localStorage.getItem("emap_language")}}const F={common:{loading:"載入中...",error:"錯誤",success:"成功",cancel:"取消",confirm:"確認",back:"返回",next:"下一步",previous:"上一步",close:"關閉",save:"儲存",delete:"刪除",edit:"編輯",search:"搜尋",filter:"篩選",all:"全部",none:"無",yes:"是",no:"否"},nav:{home:"首頁",shop:"商店",food:"美食",office:"辦公室",facility:"設施",poster:"海報",transport:"交通",worldTime:"世界時間",video:"影片",about:"關於",web:"網站",aiSearch:"AI 搜尋"},pageTitle:{eMap:"eMap",shop:"商店",food:"美食",office:"辦公室",facility:"設施",poster:"海报",transport:"交通",worldTime:"世界時間",video:"運動影片",about:"關於",web:"香港機場航班資訊",aiSearch:"AI 搜尋",aiChat:"AI 對話",shopDetail:"商店詳情"},shop:{name:"商店名稱",location:"位置",hours:"營業時間",description:"描述",searchPlaceholder:"搜尋商店名稱",noResults:"找不到相關商店",tryOtherKeywords:"請嘗試使用其他關鍵詞搜尋",startSearch:"開始搜尋",searchPrompt:"在上方搜尋框中輸入商店名稱來查找您想要的商店"},office:{companyName:"公司名稱",roomNumber:"房間號碼",floor:"樓層",byFloor:"依樓層",byName:"依名稱",filterBy:"篩選方式"},facility:{men:"男廁",women:"女廁",baby:"育嬰室",services:"服務",lift:"電梯",escalator:"手扶梯",accessibly:"無障礙設施",locker:"置物櫃"},poster:{title:"標題",description:"描述",previous:"上一張",next:"下一張",pause:"暫停",play:"播放",autoplay:"自動播放",defaultTitle:"海報",defaultDescription:"查看精彩內容"},transport:{bus:"巴士",mtr:"地鐵",lightRail:"輕鐵",miniBus:"小巴",nearby:"附近交通",schedule:"時間表",route:"路線"},food:{title:"餐飲服務",comingSoon:"即將推出"},about:{title:"關於 eMap AI",techStack:"技術棧",features:"功能特色",version:"版本信息",team:"團隊信息"},worldTime:{title:"世界時間",realtimeTitle:"實時世界時間",hongkong:"香港",tokyo:"東京",newyork:"紐約",london:"倫敦",paris:"巴黎",sydney:"雪梨",beijing:"北京",seoul:"首爾",dubai:"杜拜",currentTime:"目前時間",timezone:"時區"},video:{title:"標題",description:"描述",duration:"時長",category:"分類",mute:"靜音",unmute:"取消靜音",fullscreen:"全螢幕",mutedNotice:"視頻預設靜音播放"},weather:{temperature:"溫度",feelsLike:"體感溫度",humidity:"濕度",sunny:"晴天",cloudy:"多雲",rainy:"雨天",snowy:"雪天",stormy:"雷雨"},language:{current:"目前語言",switch:"切換語言",traditionalChinese:"繁體中文",english:"英文",spanish:"西班牙文",short:"中"},aboutDetail:{techStack:{vue:"Vue 2 - 漸進式 JavaScript 框架",typescript:"TypeScript - JavaScript 的超集，添加了類型系統",tailwind:"TailwindCSS - 實用優先的 CSS 框架",capacitor:"Capacitor - 跨平台原生應用構建工具"},features:{smartNavigation:"智能導航系統",realtimeLocation:"實時位置服務",multiLanguage:"多語言支持",crossPlatform:"跨平台兼容"},version:{current:"當前版本: v2.1.0",releaseDate:"發布日期: 2024年",updateFrequency:"更新頻率: 月度更新",supportedPlatforms:"支持平台: iOS, Android, Web"},team:{frontend:"前端開發: Vue.js + TypeScript",mobile:"移動開發: Capacitor 跨平台",design:"UI/UX 設計: 現代化玻璃風格",data:"數據支持: 實時同步"}},cities:{hongkong:"香港 Hong Kong",tokyo:"東京 Tokyo",newyork:"紐約 New York",london:"倫敦 London",paris:"巴黎 Paris",sydney:"雪梨 Sydney",beijing:"北京 Beijing",seoul:"首爾 Seoul",dubai:"杜拜 Dubai",losangeles:"洛杉磯 Los Angeles"},posterContent:{splus:{title:"S+ REWARDS會員",description:"為生活十多一點，立即登記成為S+ REWARDS會員，驚喜獎賞不斷"},ikea:{title:"IKEA家新意",description:"HomeSquare IKEA 促銷活動，家具優惠不容錯過"},more:{title:"更多促銷信息",description:"查看更多商場促銷和活動詳情"}},videoContent:{sound:{on:"開啟聲音",off:"關閉聲音",notice:"點擊下方按鈕或視頻播放器上的聲音圖標開啟音量"},videos:{basketball:{title:"Hotel Sport Event - 籃球比賽精彩集錦",description:"酒店體育活動籃球比賽的精彩瞬間回顧",category:"籃球"},swimming:{title:"游泳比賽精彩瞬間",description:"游泳比賽的激烈角逐和精彩表現",category:"游泳"},tennis:{title:"網球錦標賽決賽",description:"網球錦標賽決賽的精彩對決",category:"網球"}}},aiSearch:{placeholder:"搜索商店..."},aiChat:{welcomeTitle:"您好，我是Winnie！",welcomeMessage:"我是這個商場的智能客服助手，有什麼可以幫助您的嗎？",inputPlaceholder:"請輸入您的問題...",listening:"正在聆聽...",sendMessage:"發送",voiceInput:"語音輸入",voiceMessage:"[語音消息]",typing:"Winnie正在回覆...",error:"抱歉，我現在無法回覆您的消息。請稍後再試。",newChat:"新對話",clearChat:"清除對話",recordingGuide:"正在錄音中，請說話..."},web:{loading:"正在載入...",error:"載入失敗",refresh:"重新整理",back:"返回"}},H={common:{loading:"Loading...",error:"Error",success:"Success",cancel:"Cancel",confirm:"Confirm",back:"Back",next:"Next",previous:"Previous",close:"Close",save:"Save",delete:"Delete",edit:"Edit",search:"Search",filter:"Filter",all:"All",none:"None",yes:"Yes",no:"No"},nav:{home:"Home",shop:"Shop",food:"Food",office:"Office",facility:"Facility",poster:"Poster",transport:"Transport",worldTime:"World Time",video:"Video",about:"About",web:"Web",aiSearch:"AI Search"},pageTitle:{eMap:"eMap",shop:"Shop",food:"Food",office:"Office",facility:"Facility",poster:"Poster",transport:"Transport",worldTime:"World Time",video:"Sport Video",about:"About",web:"Web Browser",aiSearch:"Search",aiChat:"AI Assistant",shopDetail:"Shop"},shop:{name:"Shop Name",location:"Location",hours:"Opening Hours",description:"Description",searchPlaceholder:"Search shop name",noResults:"No shops found",tryOtherKeywords:"Please try other keywords",startSearch:"Start Search",searchPrompt:"Enter shop name in the search box above to find your desired shop"},office:{companyName:"Company Name",roomNumber:"Room Number",floor:"Floor",byFloor:"by Floor",byName:"by Name",filterBy:"Filter by"},facility:{men:"Men",women:"Women",baby:"Baby",services:"Services",lift:"Lift",escalator:"Escalator",accessibly:"Accessibly",locker:"Locker"},poster:{title:"Title",description:"Description",previous:"Previous",next:"Next",pause:"Pause",play:"Play",autoplay:"Autoplay",defaultTitle:"Poster",defaultDescription:"View exciting content"},transport:{bus:"Bus",mtr:"MTR",lightRail:"Light Rail",miniBus:"Mini Bus",nearby:"Nearby Transport",schedule:"Schedule",route:"Route"},food:{title:"Dining Services",comingSoon:"Coming Soon"},about:{title:"About eMap AI",techStack:"Tech Stack",features:"Features",version:"Version Info",team:"Team Info"},worldTime:{title:"World Time",realtimeTitle:"Real-time World Time",hongkong:"Hong Kong",tokyo:"Tokyo",newyork:"New York",london:"London",paris:"Paris",sydney:"Sydney",beijing:"Beijing",seoul:"Seoul",dubai:"Dubai",currentTime:"Current Time",timezone:"Timezone"},video:{title:"Title",description:"Description",duration:"Duration",category:"Category",mute:"Mute",unmute:"Unmute",fullscreen:"Fullscreen",mutedNotice:"Videos play muted by default"},weather:{temperature:"Temperature",feelsLike:"Feels Like",humidity:"Humidity",sunny:"Sunny",cloudy:"Cloudy",rainy:"Rainy",snowy:"Snowy",stormy:"Stormy"},language:{current:"Current Language",switch:"Switch Language",traditionalChinese:"Traditional Chinese",english:"English",spanish:"Spanish",short:"EN"},aboutDetail:{techStack:{vue:"Vue 2 - Progressive JavaScript Framework",typescript:"TypeScript - JavaScript superset with type system",tailwind:"TailwindCSS - Utility-first CSS framework",capacitor:"Capacitor - Cross-platform native app building tool"},features:{smartNavigation:"Smart Navigation System",realtimeLocation:"Real-time Location Service",multiLanguage:"Multi-language Support",crossPlatform:"Cross-platform Compatibility"},version:{current:"Current Version: v2.1.0",releaseDate:"Release Date: 2024",updateFrequency:"Update Frequency: Monthly Updates",supportedPlatforms:"Supported Platforms: iOS, Android, Web"},team:{frontend:"Frontend Development: Vue.js + TypeScript",mobile:"Mobile Development: Capacitor Cross-platform",design:"UI/UX Design: Modern Glass Style",data:"Data Support: Real-time Sync"}},cities:{hongkong:"Hong Kong",tokyo:"Tokyo",newyork:"New York",london:"London",paris:"Paris",sydney:"Sydney",beijing:"Beijing",seoul:"Seoul",dubai:"Dubai",losangeles:"Los Angeles"},posterContent:{splus:{title:"S+ REWARDS Members",description:"Add more to life, register as S+ REWARDS member now for continuous surprises and rewards"},ikea:{title:"IKEA Home Ideas",description:"HomeSquare IKEA promotional activities, furniture discounts not to be missed"},more:{title:"More Promotional Information",description:"View more mall promotions and event details"}},videoContent:{sound:{on:"Turn On Sound",off:"Turn Off Sound",notice:"Click the button below or the sound icon on the video player to enable volume"},videos:{basketball:{title:"Hotel Sport Event - Basketball Highlights",description:"Exciting moments review of hotel sports basketball competition",category:"Basketball"},swimming:{title:"Swimming Competition Highlights",description:"Intense competition and exciting performance of swimming matches",category:"Swimming"},tennis:{title:"Tennis Championship Final",description:"Exciting showdown of tennis championship final",category:"Tennis"}}},aiSearch:{placeholder:"Search shops..."},aiChat:{welcomeTitle:"Hello, I'm Winnie!",welcomeMessage:"I'm the smart customer service assistant for this mall. How can I help you?",inputPlaceholder:"Please enter your question...",listening:"Listening...",sendMessage:"Send",voiceInput:"Voice Input",voiceMessage:"[Voice Message]",typing:"Winnie is typing...",error:"Sorry, I can't respond to your message right now. Please try again later.",newChat:"New Chat",clearChat:"Clear Chat",recordingGuide:"Recording... Please speak..."}},K={common:{loading:"Cargando...",error:"Error",success:"Éxito",cancel:"Cancelar",confirm:"Confirmar",back:"Atrás",next:"Siguiente",previous:"Anterior",close:"Cerrar",save:"Guardar",delete:"Eliminar",edit:"Editar",search:"Buscar",filter:"Filtrar",all:"Todo",none:"Ninguno",yes:"Sí",no:"No"},nav:{home:"Inicio",shop:"Tienda",food:"Comida",office:"Oficina",facility:"Instalación",poster:"Cartel",transport:"Transporte",worldTime:"Hora Mundial",video:"Vídeo",about:"Acerca de",web:"Web",aiSearch:"Búsqueda IA"},pageTitle:{eMap:"eMap",shop:"Tienda",food:"Comida",office:"Oficina",facility:"Instalación",poster:"Cartel",transport:"Transporte",worldTime:"Hora Mundial",video:"Video Deportivo",about:"Acerca de",web:"Navegador Web",aiSearch:"Búsqueda",aiChat:"Asistente IA",shopDetail:"Tienda"},shop:{name:"Nombre de la Tienda",location:"Ubicación",hours:"Horario de Apertura",description:"Descripción",searchPlaceholder:"Buscar nombre de tienda",noResults:"No se encontraron tiendas",tryOtherKeywords:"Pruebe con otras palabras clave",startSearch:"Iniciar Búsqueda",searchPrompt:"Ingrese el nombre de la tienda en el cuadro de búsqueda para encontrar la tienda deseada"},office:{companyName:"Nombre de la Empresa",roomNumber:"Número de Habitación",floor:"Piso",byFloor:"por Piso",byName:"por Nombre",filterBy:"Filtrar por"},facility:{men:"Hombres",women:"Mujeres",baby:"Bebé",services:"Servicios",lift:"Ascensor",escalator:"Escalera Mecánica",accessibly:"Accesibilidad",locker:"Casillero"},poster:{title:"Título",description:"Descripción",previous:"Anterior",next:"Siguiente",pause:"Pausar",play:"Reproducir",autoplay:"Reproducción Automática",defaultTitle:"Cartel",defaultDescription:"Ver contenido emocionante"},transport:{bus:"Autobús",mtr:"Metro",lightRail:"Tren Ligero",miniBus:"Minibús",nearby:"Transporte Cercano",schedule:"Horario",route:"Ruta"},food:{title:"Servicios de Restauración",comingSoon:"Próximamente"},about:{title:"Acerca de eMap AI",techStack:"Stack Tecnológico",features:"Características",version:"Información de Versión",team:"Información del Equipo"},worldTime:{title:"Hora Mundial",realtimeTitle:"Hora Mundial en Tiempo Real",hongkong:"Hong Kong",tokyo:"Tokio",newyork:"Nueva York",london:"Londres",paris:"París",sydney:"Sídney",beijing:"Pekín",seoul:"Seúl",dubai:"Dubái",currentTime:"Hora Actual",timezone:"Zona Horaria"},video:{title:"Título",description:"Descripción",duration:"Duración",category:"Categoría",mute:"Silenciar",unmute:"Activar Sonido",fullscreen:"Pantalla Completa",mutedNotice:"Los videos se reproducen silenciados por defecto"},weather:{temperature:"Temperatura",feelsLike:"Sensación Térmica",humidity:"Humedad",sunny:"Soleado",cloudy:"Nublado",rainy:"Lluvioso",snowy:"Nevado",stormy:"Tormentoso"},language:{current:"Idioma Actual",switch:"Cambiar Idioma",traditionalChinese:"Chino Tradicional",english:"Inglés",spanish:"Español",short:"ES"},aboutDetail:{techStack:{vue:"Vue 2 - Framework JavaScript Progresivo",typescript:"TypeScript - Superconjunto de JavaScript con sistema de tipos",tailwind:"TailwindCSS - Framework CSS utility-first",capacitor:"Capacitor - Herramienta de construcción de aplicaciones nativas multiplataforma"},features:{smartNavigation:"Sistema de Navegación Inteligente",realtimeLocation:"Servicio de Ubicación en Tiempo Real",multiLanguage:"Soporte Multiidioma",crossPlatform:"Compatibilidad Multiplataforma"},version:{current:"Versión Actual: v2.1.0",releaseDate:"Fecha de Lanzamiento: 2024",updateFrequency:"Frecuencia de Actualización: Actualizaciones Mensuales",supportedPlatforms:"Plataformas Compatibles: iOS, Android, Web"},team:{frontend:"Desarrollo Frontend: Vue.js + TypeScript",mobile:"Desarrollo Móvil: Capacitor Multiplataforma",design:"Diseño UI/UX: Estilo Moderno de Cristal",data:"Soporte de Datos: Sincronización en Tiempo Real"}},cities:{hongkong:"Hong Kong",tokyo:"Tokio",newyork:"Nueva York",london:"Londres",paris:"París",sydney:"Sídney",beijing:"Pekín",seoul:"Seúl",dubai:"Dubái",losangeles:"Los Ángeles"},posterContent:{splus:{title:"Miembros S+ REWARDS",description:"Agregue más a la vida, regístrese como miembro S+ REWARDS ahora para sorpresas y recompensas continuas"},ikea:{title:"Ideas para el Hogar IKEA",description:"Actividades promocionales de HomeSquare IKEA, descuentos en muebles que no se pueden perder"},more:{title:"Más Información Promocional",description:"Ver más promociones y detalles de eventos del centro comercial"}},videoContent:{sound:{on:"Activar Sonido",off:"Desactivar Sonido",notice:"Haga clic en el botón de abajo o en el ícono de sonido del reproductor de video para habilitar el volumen"},videos:{basketball:{title:"Evento Deportivo del Hotel - Mejores Momentos de Baloncesto",description:"Revisión de momentos emocionantes de la competencia de baloncesto de deportes del hotel",category:"Baloncesto"},swimming:{title:"Mejores Momentos de Competencia de Natación",description:"Competencia intensa y actuación emocionante de partidos de natación",category:"Natación"},tennis:{title:"Final del Campeonato de Tenis",description:"Enfrentamiento emocionante de la final del campeonato de tenis",category:"Tenis"}}},aiSearch:{placeholder:"Buscar tiendas..."},aiChat:{welcomeTitle:"¡Hola, soy Winnie!",welcomeMessage:"Soy el asistente de servicio al cliente inteligente de este centro comercial. ¿Cómo puedo ayudarte?",inputPlaceholder:"Por favor ingrese su pregunta...",listening:"Escuchando...",sendMessage:"Enviar",voiceInput:"Entrada de voz",voiceMessage:"[Mensaje de voz]",typing:"Winnie está escribiendo...",error:"Lo siento, no puedo responder a tu mensaje en este momento. Por favor, inténtalo de nuevo más tarde.",newChat:"Nuevo chat",clearChat:"Borrar chat",recordingGuide:"Grabando... Por favor habla..."}},j={common:{loading:"読み込み中...",error:"エラー",success:"成功",cancel:"キャンセル",confirm:"確認",back:"戻る",next:"次へ",previous:"前へ",close:"閉じる",save:"保存",delete:"削除",edit:"編集",search:"検索",filter:"フィルター",all:"すべて",none:"なし",yes:"はい",no:"いいえ"},nav:{home:"ホーム",shop:"ショップ",food:"フード",office:"オフィス",facility:"施設",poster:"ポスター",transport:"交通",worldTime:"世界時計",video:"ビデオ",about:"について",web:"ウェブ",aiSearch:"AI検索"},pageTitle:{eMap:"eMap",shop:"ショップ",food:"フード",office:"オフィス",facility:"施設",poster:"ポスター",transport:"交通",worldTime:"世界時計",video:"スポーツ動画",about:"について",web:"ウェブブラウザ",aiSearch:"検索",aiChat:"AIアシスタント",shopDetail:"ショップ"},shop:{name:"ショップ名",location:"場所",hours:"営業時間",description:"説明",searchPlaceholder:"ショップ名を検索",noResults:"ショップが見つかりません",tryOtherKeywords:"他のキーワードをお試しください",startSearch:"検索開始",searchPrompt:"上の検索ボックスにショップ名を入力してお探しのショップを見つけてください"},office:{companyName:"会社名",roomNumber:"部屋番号",floor:"階",byFloor:"階別",byName:"名前別",filterBy:"フィルター"},facility:{men:"男性",women:"女性",baby:"赤ちゃん",services:"サービス",lift:"エレベーター",escalator:"エスカレーター",accessibly:"アクセシビリティ",locker:"ロッカー"},poster:{title:"タイトル",description:"説明",previous:"前へ",next:"次へ",pause:"一時停止",play:"再生",autoplay:"自動再生",defaultTitle:"ポスター",defaultDescription:"興味深いコンテンツを見る"},transport:{bus:"バス",mtr:"MTR",lightRail:"ライトレール",miniBus:"ミニバス",nearby:"近くの交通機関",schedule:"スケジュール",route:"ルート"},food:{title:"レストランサービス",comingSoon:"近日公開"},about:{title:"eMap AIについて",techStack:"技術スタック",features:"機能",version:"バージョン情報",team:"チーム情報"},worldTime:{title:"世界時計",realtimeTitle:"リアルタイム世界時計",hongkong:"香港",tokyo:"東京",newyork:"ニューヨーク",london:"ロンドン",paris:"パリ",sydney:"シドニー",beijing:"北京",seoul:"ソウル",dubai:"ドバイ",currentTime:"現在時刻",timezone:"タイムゾーン"},video:{title:"タイトル",description:"説明",duration:"時間",category:"カテゴリ",mute:"ミュート",unmute:"ミュート解除",fullscreen:"フルスクリーン",mutedNotice:"ビデオはデフォルトでミュート再生されます"},weather:{temperature:"気温",feelsLike:"体感気温",humidity:"湿度",sunny:"晴れ",cloudy:"曇り",rainy:"雨",snowy:"雪",stormy:"嵐"},language:{current:"現在の言語",switch:"言語を変更",traditionalChinese:"繁体字中国語",english:"英語",spanish:"スペイン語",japanese:"日本語",korean:"韓国語",thai:"タイ語",short:"JA"},aboutDetail:{techStack:{vue:"Vue 2 - プログレッシブJavaScriptフレームワーク",typescript:"TypeScript - 型システム付きJavaScriptスーパーセット",tailwind:"TailwindCSS - ユーティリティファーストCSSフレームワーク",capacitor:"Capacitor - クロスプラットフォームネイティブアプリビルドツール"},features:{smartNavigation:"スマートナビゲーションシステム",realtimeLocation:"リアルタイム位置サービス",multiLanguage:"多言語サポート",crossPlatform:"クロスプラットフォーム対応"},version:{current:"現在のバージョン: v2.1.0",releaseDate:"リリース日: 2024年",updateFrequency:"更新頻度: 月次更新",supportedPlatforms:"サポートプラットフォーム: iOS, Android, Web"},team:{frontend:"フロントエンド開発: Vue.js + TypeScript",mobile:"モバイル開発: Capacitor クロスプラットフォーム",design:"UI/UXデザイン: モダンガラススタイル",data:"データサポート: リアルタイム同期"}},cities:{hongkong:"香港",tokyo:"東京",newyork:"ニューヨーク",london:"ロンドン",paris:"パリ",sydney:"シドニー",beijing:"北京",seoul:"ソウル",dubai:"ドバイ",losangeles:"ロサンゼルス"},posterContent:{splus:{title:"S+ REWARDSメンバー",description:"生活にプラスを、今すぐS+ REWARDSメンバーに登録して継続的な驚きと報酬を"},ikea:{title:"IKEAホームアイデア",description:"HomeSquare IKEA プロモーション活動、見逃せない家具割引"},more:{title:"その他のプロモーション情報",description:"ショッピングモールのプロモーションやイベント詳細をもっと見る"}},videoContent:{sound:{on:"音を有効にする",off:"音を無効にする",notice:"下のボタンまたはビデオプレーヤーの音アイコンをクリックしてボリュームを有効にしてください"},videos:{basketball:{title:"ホテルスポーツイベント - バスケットボールハイライト",description:"ホテルスポーツバスケットボール大会の興奮する瞬間の振り返り",category:"バスケットボール"},swimming:{title:"水泳競技ハイライト",description:"水泳試合の激しい競争と興奮するパフォーマンス",category:"水泳"},tennis:{title:"テニス選手権決勝",description:"テニス選手権決勝の興奮する対戦",category:"テニス"}}},aiSearch:{placeholder:"ショップを検索..."},aiChat:{welcomeTitle:"こんにちは、私はWinnieです！",welcomeMessage:"このショッピングモールのスマートカスタマーサービスアシスタントです。どのようにお手伝いできますか？",inputPlaceholder:"ご質問をご入力ください...",listening:"聞いています...",sendMessage:"送信",voiceInput:"音声入力",voiceMessage:"[音声メッセージ]",typing:"Winnieが入力中...",error:"申し訳ございません。現在メッセージにお答えできません。後でもう一度お試しください。",newChat:"新しいチャット",clearChat:"チャットをクリア",recordingGuide:"録音中...お話しください..."},web:{loading:"読み込み中...",error:"読み込みに失敗しました",refresh:"更新",back:"戻る"}},V={common:{loading:"로딩 중...",error:"오류",success:"성공",cancel:"취소",confirm:"확인",back:"뒤로",next:"다음",previous:"이전",close:"닫기",save:"저장",delete:"삭제",edit:"편집",search:"검색",filter:"필터",all:"모두",none:"없음",yes:"예",no:"아니오"},nav:{home:"홈",shop:"상점",food:"음식",office:"사무실",facility:"시설",poster:"포스터",transport:"교통",worldTime:"세계시간",video:"비디오",about:"소개",web:"웹",aiSearch:"AI 검색"},pageTitle:{eMap:"eMap",shop:"상점",food:"음식",office:"사무실",facility:"시설",poster:"포스터",transport:"교통",worldTime:"세계시간",video:"스포츠 비디오",about:"소개",web:"웹 브라우저",aiSearch:"검색",aiChat:"AI 어시스턴트",shopDetail:"상점"},shop:{name:"상점명",location:"위치",hours:"운영시간",description:"설명",searchPlaceholder:"상점명 검색",noResults:"상점을 찾을 수 없습니다",tryOtherKeywords:"다른 키워드를 사용해 보세요",startSearch:"검색 시작",searchPrompt:"위의 검색 상자에 상점명을 입력하여 원하는 상점을 찾으세요"},office:{companyName:"회사명",roomNumber:"방 번호",floor:"층",byFloor:"층별",byName:"이름별",filterBy:"필터링"},facility:{men:"남성",women:"여성",baby:"아기",services:"서비스",lift:"엘리베이터",escalator:"에스컬레이터",accessibly:"접근성",locker:"사물함"},poster:{title:"제목",description:"설명",previous:"이전",next:"다음",pause:"일시정지",play:"재생",autoplay:"자동재생",defaultTitle:"포스터",defaultDescription:"흥미로운 콘텐츠 보기"},transport:{bus:"버스",mtr:"지하철",lightRail:"경전철",miniBus:"미니버스",nearby:"근처 교통수단",schedule:"시간표",route:"노선"},food:{title:"레스토랑 서비스",comingSoon:"곧 출시"},about:{title:"eMap AI 소개",techStack:"기술 스택",features:"기능",version:"버전 정보",team:"팀 정보"},worldTime:{title:"세계시간",realtimeTitle:"실시간 세계시간",hongkong:"홍콩",tokyo:"도쿄",newyork:"뉴욕",london:"런던",paris:"파리",sydney:"시드니",beijing:"베이징",seoul:"서울",dubai:"두바이",currentTime:"현재 시간",timezone:"시간대"},video:{title:"제목",description:"설명",duration:"길이",category:"카테고리",mute:"음소거",unmute:"음소거 해제",fullscreen:"전체화면",mutedNotice:"비디오는 기본적으로 음소거되어 재생됩니다"},weather:{temperature:"온도",feelsLike:"체감온도",humidity:"습도",sunny:"맑음",cloudy:"흐림",rainy:"비",snowy:"눈",stormy:"폭풍"},language:{current:"현재 언어",switch:"언어 변경",traditionalChinese:"중국어 번체",english:"영어",spanish:"스페인어",japanese:"일본어",korean:"한국어",thai:"태국어",short:"KO"},aboutDetail:{techStack:{vue:"Vue 2 - 프로그레시브 JavaScript 프레임워크",typescript:"TypeScript - 타입 시스템이 있는 JavaScript 슈퍼셋",tailwind:"TailwindCSS - 유틸리티 우선 CSS 프레임워크",capacitor:"Capacitor - 크로스 플랫폼 네이티브 앱 빌드 도구"},features:{smartNavigation:"스마트 내비게이션 시스템",realtimeLocation:"실시간 위치 서비스",multiLanguage:"다국어 지원",crossPlatform:"크로스 플랫폼 호환성"},version:{current:"현재 버전: v2.1.0",releaseDate:"출시일: 2024년",updateFrequency:"업데이트 빈도: 월간 업데이트",supportedPlatforms:"지원 플랫폼: iOS, Android, Web"},team:{frontend:"프론트엔드 개발: Vue.js + TypeScript",mobile:"모바일 개발: Capacitor 크로스 플랫폼",design:"UI/UX 디자인: 모던 글래스 스타일",data:"데이터 지원: 실시간 동기화"}},cities:{hongkong:"홍콩",tokyo:"도쿄",newyork:"뉴욕",london:"런던",paris:"파리",sydney:"시드니",beijing:"베이징",seoul:"서울",dubai:"두바이",losangeles:"로스앤젤레스"},posterContent:{splus:{title:"S+ REWARDS 멤버",description:"생활에 더 많은 것을 추가하고, 지금 S+ REWARDS 멤버로 등록하여 지속적인 놀라움과 보상을 받으세요"},ikea:{title:"IKEA 홈 아이디어",description:"HomeSquare IKEA 프로모션 활동, 놓치지 말아야 할 가구 할인"},more:{title:"더 많은 프로모션 정보",description:"쇼핑몰 프로모션 및 이벤트 세부 정보 더 보기"}},videoContent:{sound:{on:"소리 켜기",off:"소리 끄기",notice:"아래 버튼이나 비디오 플레이어의 소리 아이콘을 클릭하여 볼륨을 활성화하세요"},videos:{basketball:{title:"호텔 스포츠 이벤트 - 농구 하이라이트",description:"호텔 스포츠 농구 경기의 흥미진진한 순간들 되돌아보기",category:"농구"},swimming:{title:"수영 경기 하이라이트",description:"수영 경기의 치열한 경쟁과 흥미진진한 성과",category:"수영"},tennis:{title:"테니스 챔피언십 결승",description:"테니스 챔피언십 결승의 흥미진진한 대결",category:"테니스"}}},aiSearch:{placeholder:"상점 검색..."},aiChat:{welcomeTitle:"안녕하세요, 저는 Winnie입니다!",welcomeMessage:"이 쇼핑몰의 스마트 고객 서비스 어시스턴트입니다. 어떻게 도와드릴까요?",inputPlaceholder:"질문을 입력해 주세요...",listening:"듣고 있습니다...",sendMessage:"보내기",voiceInput:"음성 입력",voiceMessage:"[음성 메시지]",typing:"Winnie가 입력 중...",error:"죄송합니다. 지금은 메시지에 응답할 수 없습니다. 나중에 다시 시도해 주세요.",newChat:"새 채팅",clearChat:"채팅 지우기",recordingGuide:"녹음 중... 말씀해 주세요..."},web:{loading:"로딩 중...",error:"로딩 실패",refresh:"새로고침",back:"뒤로"}},G={common:{loading:"กำลังโหลด...",error:"ข้อผิดพลาด",success:"สำเร็จ",cancel:"ยกเลิก",confirm:"ยืนยัน",back:"กลับ",next:"ถัดไป",previous:"ก่อนหน้า",close:"ปิด",save:"บันทึก",delete:"ลบ",edit:"แก้ไข",search:"ค้นหา",filter:"กรอง",all:"ทั้งหมด",none:"ไม่มี",yes:"ใช่",no:"ไม่"},nav:{home:"หน้าหลัก",shop:"ร้านค้า",food:"อาหาร",office:"สำนักงาน",facility:"สิ่งอำนวยความสะดวก",poster:"โปสเตอร์",transport:"การขนส่ง",worldTime:"เวลาโลก",video:"วิดีโอ",about:"เกี่ยวกับ",web:"เว็บ",aiSearch:"ค้นหา AI"},pageTitle:{eMap:"eMap",shop:"ร้านค้า",food:"อาหาร",office:"สำนักงาน",facility:"สิ่งอำนวยความสะดวก",poster:"โปสเตอร์",transport:"การขนส่ง",worldTime:"เวลาโลก",video:"วิดีโอกีฬา",about:"เกี่ยวกับ",web:"เว็บเบราว์เซอร์",aiSearch:"ค้นหา",aiChat:"ผู้ช่วย AI",shopDetail:"ร้านค้า"},shop:{name:"ชื่อร้าน",location:"สถานที่",hours:"เวลาทำการ",description:"รายละเอียด",searchPlaceholder:"ค้นหาชื่อร้าน",noResults:"ไม่พบร้านค้า",tryOtherKeywords:"โปรดลองใช้คำค้นหาอื่น",startSearch:"เริ่มค้นหา",searchPrompt:"ป้อนชื่อร้านในช่องค้นหาด้านบนเพื่อค้นหาร้านที่ต้องการ"},office:{companyName:"ชื่อบริษัท",roomNumber:"หมายเลขห้อง",floor:"ชั้น",byFloor:"ตามชั้น",byName:"ตามชื่อ",filterBy:"กรองโดย"},facility:{men:"ผู้ชาย",women:"ผู้หญิง",baby:"เด็ก",services:"บริการ",lift:"ลิฟต์",escalator:"บันไดเลื่อน",accessibly:"การเข้าถึง",locker:"ล็อกเกอร์"},poster:{title:"ชื่อเรื่อง",description:"รายละเอียด",previous:"ก่อนหน้า",next:"ถัดไป",pause:"หยุดชั่วคราว",play:"เล่น",autoplay:"เล่นอัตโนมัติ",defaultTitle:"โปสเตอร์",defaultDescription:"ดูเนื้อหาที่น่าสนใจ"},transport:{bus:"รถบัส",mtr:"รถไฟฟ้า",lightRail:"รถไฟฟ้าเบา",miniBus:"รถมินิบัส",nearby:"การขนส่งใกล้เคียง",schedule:"ตารางเวลา",route:"เส้นทาง"},food:{title:"บริการร้านอาหาร",comingSoon:"เร็วๆ นี้"},about:{title:"เกี่ยวกับ eMap AI",techStack:"เทคโนโลยี",features:"คุณสมบัติ",version:"ข้อมูลเวอร์ชัน",team:"ข้อมูลทีม"},worldTime:{title:"เวลาโลก",realtimeTitle:"เวลาโลกแบบเรียลไทม์",hongkong:"ฮ่องกง",tokyo:"โตเกียว",newyork:"นิวยอร์ก",london:"ลอนดอน",paris:"ปารีส",sydney:"ซิดนีย์",beijing:"ปักกิ่ง",seoul:"โซล",dubai:"ดูไบ",currentTime:"เวลาปัจจุบัน",timezone:"เขตเวลา"},video:{title:"ชื่อเรื่อง",description:"รายละเอียด",duration:"ความยาว",category:"หมวดหมู่",mute:"ปิดเสียง",unmute:"เปิดเสียง",fullscreen:"เต็มหน้าจอ",mutedNotice:"วิดีโอเล่นโดยปิดเสียงตามค่าเริ่มต้น"},weather:{temperature:"อุณหภูมิ",feelsLike:"รู้สึกเหมือน",humidity:"ความชื้น",sunny:"แสงแดด",cloudy:"มีเมฆ",rainy:"ฝนตก",snowy:"หิมะตก",stormy:"พายุ"},language:{current:"ภาษาปัจจุบัน",switch:"เปลี่ยนภาษา",traditionalChinese:"จีนแบบดั้งเดิม",english:"อังกฤษ",spanish:"สเปน",japanese:"ญี่ปุ่น",korean:"เกาหลี",thai:"ไทย",short:"TH"},aboutDetail:{techStack:{vue:"Vue 2 - Progressive JavaScript Framework",typescript:"TypeScript - JavaScript superset พร้อมระบบ type",tailwind:"TailwindCSS - Utility-first CSS framework",capacitor:"Capacitor - เครื่องมือสร้างแอพพลิเคชันข้ามแพลตฟอร์ม"},features:{smartNavigation:"ระบบนำทางอัจฉริยะ",realtimeLocation:"บริการตำแหน่งแบบเรียลไทม์",multiLanguage:"รองรับหลายภาษา",crossPlatform:"ความเข้ากันได้ข้ามแพลตฟอร์ม"},version:{current:"เวอร์ชันปัจจุบัน: v2.1.0",releaseDate:"วันที่เปิดตัว: 2024",updateFrequency:"ความถี่ในการอัพเดต: อัพเดตรายเดือน",supportedPlatforms:"แพลตฟอร์มที่รองรับ: iOS, Android, Web"},team:{frontend:"การพัฒนา Frontend: Vue.js + TypeScript",mobile:"การพัฒนา Mobile: Capacitor Cross-platform",design:"การออกแบบ UI/UX: สไตล์แก้วสมัยใหม่",data:"การสนับสนุนข้อมูล: การซิงค์แบบเรียลไทม์"}},cities:{hongkong:"ฮ่องกง",tokyo:"โตเกียว",newyork:"นิวยอร์ก",london:"ลอนดอน",paris:"ปารีส",sydney:"ซิดนีย์",beijing:"ปักกิ่ง",seoul:"โซล",dubai:"ดูไบ",losangeles:"ลอสแองเจลิส"},posterContent:{splus:{title:"สมาชิก S+ REWARDS",description:"เพิ่มเติมให้กับชีวิต สมัครเป็นสมาชิก S+ REWARDS เดี๋ยวนี้เพื่อรับความประหลาดใจและรางวัลอย่างต่อเนื่อง"},ikea:{title:"IKEA Home Ideas",description:"กิจกรรมส่งเสริมการขาย HomeSquare IKEA ส่วนลดเฟอร์นิเจอร์ที่ไม่ควรพลาด"},more:{title:"ข้อมูลการส่งเสริมการขายเพิ่มเติม",description:"ดูการส่งเสริมการขายและรายละเอียดกิจกรรมของห้างสรรพสินค้าเพิ่มเติม"}},videoContent:{sound:{on:"เปิดเสียง",off:"ปิดเสียง",notice:"คลิกปุ่มด้านล่างหรือไอคอนเสียงบนเครื่องเล่นวิดีโอเพื่อเปิดเสียง"},videos:{basketball:{title:"กิจกรรมกีฬาของโรงแรม - ไฮไลท์บาสเกตบอล",description:"ย้อนดูช่วงเวลาที่น่าตื่นเต้นของการแข่งขันบาสเกตบอลกีฬาของโรงแรม",category:"บาสเกตบอล"},swimming:{title:"ไฮไลท์การแข่งขันว่ายน้ำ",description:"การแข่งขันที่เข้มข้นและการแสดงที่น่าตื่นเต้นของการแข่งขันว่ายน้ำ",category:"ว่ายน้ำ"},tennis:{title:"รอบชิงชนะเลิศเทนนิส",description:"การดวลที่น่าตื่นเต้นของรอบชิงชนะเลิศเทนนิส",category:"เทนนิส"}}},aiSearch:{placeholder:"ค้นหาร้านค้า..."},aiChat:{welcomeTitle:"สวัสดีค่ะ ฉันชื่อ Winnie!",welcomeMessage:"ฉันเป็นผู้ช่วยบริการลูกค้าอัจฉริยะของห้างสรรพสินค้านี้ มีอะไรให้ฉันช่วยไหมคะ?",inputPlaceholder:"โปรดป้อนคำถามของคุณ...",listening:"กำลังฟัง...",sendMessage:"ส่ง",voiceInput:"ป้อนเสียง",voiceMessage:"[ข้อความเสียง]",typing:"Winnie กำลังพิมพ์...",error:"ขออภัย ตอนนี้ฉันไม่สามารถตอบข้อความของคุณได้ โปรดลองใหม่อีกครั้งในภายหลัง",newChat:"แชทใหม่",clearChat:"ล้างแชท",recordingGuide:"กำลังบันทึก... โปรดพูด..."},web:{loading:"กำลังโหลด...",error:"โหลดล้มเหลว",refresh:"รีเฟรช",back:"กลับ"}};function q(){W.Ay.setTranslations("zh-TW",F),W.Ay.setTranslations("en",H),W.Ay.setTranslations("es",K),W.Ay.setTranslations("ja",j),W.Ay.setTranslations("ko",V),W.Ay.setTranslations("th",G)}async function J(e){console.log("[i18n] 開始初始化國際化系統..."),q(),console.log("[i18n] 語言包載入完成"),await new Promise(e=>setTimeout(e,10));const t=z(e);console.log(`[i18n] 檢測到語言: ${t}`),e&&(e.$forceUpdate(),setTimeout(()=>{e.$root.$emit("language-changed",t)},100)),console.log(`[i18n] 初始化完成，當前語言: ${t}`)}function X(e){console.group("🌐 語言系統調試信息"),console.log("1. 語言包加載狀態:");const t=["zh-TW","en","es"];t.forEach(e=>{const t="common.loading",o=W.Ay.t(t);console.log(`   ${e}: ${o?"✅ 已加載":"❌ 未加載"}`)});const o=U();console.log("2. 系統語言信息:",o),console.log("3. 當前語言:",W.Ay.getCurrentLanguage()),console.log("4. 測試翻譯:");const a=["nav.shop","nav.food","common.loading"];a.forEach(e=>{console.log(`   ${e}: "${W.Ay.t(e)}"`)}),console.log("5. 重新檢測並設置語言...");const n=z(e);console.log(`   檢測結果: ${n}`),console.log("6. 設置後的翻譯測試:"),a.forEach(e=>{console.log(`   ${e}: "${W.Ay.t(e)}"`)}),console.groupEnd()}function Y(e,t){console.group(`🔄 手動切換語言到: ${e}`),W.Ay.setCurrentLanguage(e),t&&t.$root&&t.$root.$emit("language-changed",e),console.log("切換後的測試:");const o=["nav.shop","nav.food","common.loading"];o.forEach(e=>{console.log(`   ${e}: "${W.Ay.t(e)}"`)}),console.groupEnd()}"undefined"!==typeof window&&(window.__debugLanguage={info:X,switch:Y,service:W.Ay});var Z=o(417),Q=o(633);a["default"].config.productionTip=!1;const ee=()=>{console.log("开始禁用原生浏览器行为...");let e=0;document.addEventListener("touchend",t=>{const o=(new Date).getTime();o-e<=300&&t.preventDefault(),e=o},!1),document.addEventListener("dblclick",e=>{e.preventDefault()},!1),document.addEventListener("contextmenu",e=>{e.preventDefault()},!1),document.addEventListener("dragstart",e=>{e.preventDefault()},!1),document.addEventListener("selectstart",e=>{e.preventDefault()},!1),document.addEventListener("keydown",e=>{(e.ctrlKey||e.metaKey)&&"F5"!==e.key&&"F12"!==e.key&&e.preventDefault(),"F12"===e.key&&e.preventDefault()},!1),document.addEventListener("gesturestart",e=>{e.preventDefault()},!1),document.addEventListener("gesturechange",e=>{e.preventDefault()},!1),document.addEventListener("gestureend",e=>{e.preventDefault()},!1),document.addEventListener("wheel",e=>{(e.ctrlKey||e.metaKey)&&e.preventDefault()},{passive:!1}),console.log("原生浏览器行为禁用完成")},te=()=>{T.Ii.isNativePlatform()&&setTimeout(()=>{try{const e=document.querySelector('meta[name="viewport"]');e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.width="100vw",document.body.style.height="100vh",document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden";const t=document.getElementById("app");t&&(t.style.width="100vw",t.style.height="100vh",t.style.position="fixed",t.style.top="0",t.style.left="0"),console.log("强制全屏设置完成")}catch(e){console.error("强制全屏设置失败:",e)}},100)},oe=async()=>{if(T.Ii.isNativePlatform())try{await Q.S.hide(),await Z.eA.hide(),te(),console.log("Capacitor 插件初始化完成")}catch(e){console.error("Capacitor 插件初始化失败:",e),te()}},ae=async()=>{try{console.log("开始初始化应用配置..."),await B.dispatch("config/initConfig"),console.log("应用配置初始化完成")}catch(e){console.error("应用配置初始化失败:",e)}},ne=async()=>{ee(),await oe(),await ae();const e=new a["default"]({router:_,store:B,render:e=>e(m),async created(){await J(this)},async mounted(){T.Ii.isNativePlatform()&&(te(),window.addEventListener("resize",te),window.addEventListener("orientationchange",()=>{setTimeout(te,300)})),setTimeout(ee,100)}}).$mount("#app");return e};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",ee):ee(),ne()},958:(e,t,o)=>{o.d(t,{A:()=>h});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"bottom-bar"},[t("div",{staticClass:"ai-section"},[t("GlassButton",{staticClass:"ai-button",attrs:{size:"small",variant:"normal"},on:{click:e.onAIClick}},[t("div",{staticClass:"ai-content"},[t("img",{staticClass:"ai-icon",attrs:{src:"/img/icon_ai.png",alt:"AI Icon"}}),t("span",{staticClass:"ai-text"},[e._v("ai")])])])],1),t("div",{staticClass:"nav-buttons"},[t("GlassButton",{staticClass:"nav-button",attrs:{size:"small",variant:"normal"},on:{click:e.onHomeClick}},[t("svg",{staticClass:"home-icon",attrs:{fill:"currentColor",viewBox:"0 0 20 20"}},[t("path",{attrs:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}})])]),t("GlassButton",{staticClass:"nav-button",attrs:{size:"small",variant:"normal"},on:{click:()=>e.switchLanguage("en")}},[t("div",{staticClass:"lang-content"},[t("span",{staticClass:"lang-text"},[e._v("EN")])])]),t("GlassButton",{staticClass:"nav-button",attrs:{size:"small",variant:"normal"},on:{click:()=>e.switchLanguage("es")}},[t("div",{staticClass:"lang-content"},[t("span",{staticClass:"lang-text"},[e._v("ES")])])]),t("GlassButton",{staticClass:"nav-button",attrs:{size:"small",variant:"normal"},on:{click:()=>e.switchLanguage("zh-TW")}},[t("div",{staticClass:"lang-content"},[t("span",{staticClass:"lang-text"},[e._v("中")])])])],1)])},n=[],i=o(635),r=o(233),s=o(317),c=o(959);let l=class extends((0,r.Xe)(c.A)){onHomeClick(){this.$emit("home-clicked"),"/"!==this.$route.path&&this.$router.push("/")}switchLanguage(e){console.log(`[BottomBar] 開始切換語言到: ${e}`),this.$setLanguage(e),this.$emit("language-changed",e),this.showLanguageSwitchFeedback(e),setTimeout(()=>{console.log(`[BottomBar] 延遲觸發語言更新: ${e}`),this.$root.$emit("language-changed",e),this.$root.$forceUpdate()},50)}showLanguageSwitchFeedback(e){const t=this.$getLanguageConfig(e);t&&console.log(`Language switched to: ${t.nativeName}`)}onAIClick(){this.$emit("ai-clicked"),"/ai-chat"!==this.$route.path&&this.$router.push("/ai-chat")}};l=(0,i.Cg)([(0,r.uA)({components:{GlassButton:s.A}})],l);const u=l,d=u;var g=o(656),m=(0,g.A)(d,a,n,!1,null,"001050e4",null);const h=m.exports},959:(e,t,o)=>{o.d(t,{A:()=>s});var a=o(635),n=o(233),i=o(636);let r=class extends n.lD{$t(e,t){return i.Ay.t(e,t)}get $currentLanguage(){return i.Ay.getCurrentLanguage()}$setLanguage(e){i.Ay.setCurrentLanguage(e),this.$forceUpdate(),this.$root.$emit("language-changed",e)}$getLanguageConfig(e){return i.Ay.getLanguageConfig(e)}get $supportedLanguages(){return i.Ay.getSupportedLanguages()}$isCurrentLanguage(e){return this.$currentLanguage===e}get $currentLanguageNativeName(){const e=this.$getLanguageConfig();return e?.nativeName||this.$currentLanguage}mounted(){this.$root.$on("language-changed",this.onLanguageChanged)}beforeDestroy(){this.$root.$off("language-changed",this.onLanguageChanged)}onLanguageChanged(e){this.$forceUpdate()}};r=(0,a.Cg)([n.uA],r);const s=r}},t={};function o(a){var n=t[a];if(void 0!==n)return n.exports;var i=t[a]={exports:{}};return e[a].call(i.exports,i,i.exports,o),i.exports}o.m=e,(()=>{var e=[];o.O=(t,a,n,i)=>{if(!a){var r=1/0;for(u=0;u<e.length;u++){for(var[a,n,i]=e[u],s=!0,c=0;c<a.length;c++)(!1&i||r>=i)&&Object.keys(o.O).every(e=>o.O[e](a[c]))?a.splice(c--,1):(s=!1,i<r&&(r=i));if(s){e.splice(u--,1);var l=n();void 0!==l&&(t=l)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[a,n,i]}})(),(()=>{o.d=(e,t)=>{for(var a in t)o.o(t,a)&&!o.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce((t,a)=>(o.f[a](e,t),t),[]))})(),(()=>{o.u=e=>"js/"+e+"."+{13:"bf15dbb1",78:"77a85efc",103:"0128a703",126:"77f05161",253:"b32963ea",325:"d01503e8",367:"884a0851",576:"9c1f8b38",654:"4f34f9f2",695:"8f908f83",918:"22c14820",968:"ad638510"}[e]+".js"})(),(()=>{o.miniCssF=e=>"css/"+e+"."+{13:"8a6f8751",78:"7adba377",103:"96d9c095",126:"7e00c297",253:"ba582246",325:"8520ab92",367:"b5e08ad4",576:"3ae3e849",654:"2b7da1d5",695:"83e03f6e",918:"ce9722c6",968:"d00704b4"}[e]+".css"})(),(()=>{o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="emap-ai:";o.l=(a,n,i,r)=>{if(e[a])e[a].push(n);else{var s,c;if(void 0!==i)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+i){s=d;break}}s||(c=!0,s=document.createElement("script"),s.charset="utf-8",s.timeout=120,o.nc&&s.setAttribute("nonce",o.nc),s.setAttribute("data-webpack",t+i),s.src=a),e[a]=[n];var g=(t,o)=>{s.onerror=s.onload=null,clearTimeout(m);var n=e[a];if(delete e[a],s.parentNode&&s.parentNode.removeChild(s),n&&n.forEach(e=>e(o)),t)return t(o)},m=setTimeout(g.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=g.bind(null,s.onerror),s.onload=g.bind(null,s.onload),c&&document.head.appendChild(s)}}})(),(()=>{o.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{o.p=""})(),(()=>{if("undefined"!==typeof document){var e=(e,t,a,n,i)=>{var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",o.nc&&(r.nonce=o.nc);var s=o=>{if(r.onerror=r.onload=null,"load"===o.type)n();else{var a=o&&o.type,s=o&&o.target&&o.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+s+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=s,r.parentNode&&r.parentNode.removeChild(r),i(c)}};return r.onerror=r.onload=s,r.href=t,a?a.parentNode.insertBefore(r,a.nextSibling):document.head.appendChild(r),r},t=(e,t)=>{for(var o=document.getElementsByTagName("link"),a=0;a<o.length;a++){var n=o[a],i=n.getAttribute("data-href")||n.getAttribute("href");if("stylesheet"===n.rel&&(i===e||i===t))return n}var r=document.getElementsByTagName("style");for(a=0;a<r.length;a++){n=r[a],i=n.getAttribute("data-href");if(i===e||i===t)return n}},a=a=>new Promise((n,i)=>{var r=o.miniCssF(a),s=o.p+r;if(t(r,s))return n();e(a,s,null,n,i)}),n={524:0};o.f.miniCss=(e,t)=>{var o={13:1,78:1,103:1,126:1,253:1,325:1,367:1,576:1,654:1,695:1,918:1,968:1};n[e]?t.push(n[e]):0!==n[e]&&o[e]&&t.push(n[e]=a(e).then(()=>{n[e]=0},t=>{throw delete n[e],t}))}}})(),(()=>{var e={524:0};o.f.j=(t,a)=>{var n=o.o(e,t)?e[t]:void 0;if(0!==n)if(n)a.push(n[2]);else{var i=new Promise((o,a)=>n=e[t]=[o,a]);a.push(n[2]=i);var r=o.p+o.u(t),s=new Error,c=a=>{if(o.o(e,t)&&(n=e[t],0!==n&&(e[t]=void 0),n)){var i=a&&("load"===a.type?"missing":a.type),r=a&&a.target&&a.target.src;s.message="Loading chunk "+t+" failed.\n("+i+": "+r+")",s.name="ChunkLoadError",s.type=i,s.request=r,n[1](s)}};o.l(r,c,"chunk-"+t,t)}},o.O.j=t=>0===e[t];var t=(t,a)=>{var n,i,[r,s,c]=a,l=0;if(r.some(t=>0!==e[t])){for(n in s)o.o(s,n)&&(o.m[n]=s[n]);if(c)var u=c(o)}for(t&&t(a);l<r.length;l++)i=r[l],o.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return o.O(u)},a=self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=o.O(void 0,[121],()=>o(939));a=o.O(a)})();