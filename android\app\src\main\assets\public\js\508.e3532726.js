"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[508],{9616:(e,t,s)=>{s.r(t),s.d(t,{default:()=>Q});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"ai-search-page responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--ai"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.aiSearch")))])]),t("div",{staticClass:"search-input-container"},[t("div",{staticClass:"search-icon"},[t("svg",{attrs:{width:"116",height:"116",viewBox:"0 0 116 116",fill:"none"}},[t("path",{attrs:{d:"M51.6 90.2c21.3 0 38.6-17.3 38.6-38.6S72.9 13 51.6 13 13 30.3 13 51.6 30.3 90.2 51.6 90.2zM96.8 103L71.4 77.6",stroke:"white","stroke-width":"6","stroke-linecap":"round","stroke-linejoin":"round"}})])]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.searchQuery,expression:"searchQuery"}],staticClass:"search-input",attrs:{type:"text",placeholder:e.$t("aiSearch.placeholder")},domProps:{value:e.searchQuery},on:{input:[function(t){t.target.composing||(e.searchQuery=t.target.value)},e.onSearchInput]}})]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--shop"},[e.searchQuery.trim()?0===e.filteredShops.length?t("div",{staticClass:"no-results"},[t("div",{staticClass:"no-results-icon"},[e._v("😔")]),t("h2",{staticClass:"no-results-title"},[e._v(e._s(e.$t("shop.noResults")))]),t("p",{staticClass:"no-results-description"},[e._v(e._s(e.$t("shop.tryOtherKeywords")))])]):t("div",{staticClass:"shop-grid"},e._l(e.filteredShops,function(s){return t("ShopCard",{key:s.id,attrs:{"shop-name":s.name,"logo-src":s.logo},on:{click:function(t){return e.navigateToShop(s.id)}}})}),1):t("div",{staticClass:"search-prompt"},[t("div",{staticClass:"prompt-icon"},[e._v("🔍")]),t("h2",{staticClass:"prompt-title"},[e._v(e._s(e.$t("shop.startSearch")))]),t("p",{staticClass:"prompt-description"},[e._v(e._s(e.$t("shop.searchPrompt")))])])])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}}),t("BottomMarquee")],1)},o=[],r=s(1635),i=s(9603),n=s(3452),c=s(3205),l=s(4184),p=s(256),h=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("BaseCard",{attrs:{title:e.shopName,"icon-src":e.logoSrc,size:"large",variant:"shop",layout:"vertical","enable-marquee":!0,"marquee-speed":4,"marquee-delay":1.5,"marquee-type":"bounce"},on:{click:function(t){return e.$emit("click")}}})},u=[],d=s(9099);let v=class extends i.lD{shopName;logoSrc};(0,r.Cg)([(0,i.kv)({required:!0})],v.prototype,"shopName",void 0),(0,r.Cg)([(0,i.kv)({default:""})],v.prototype,"logoSrc",void 0),v=(0,r.Cg)([(0,i.uA)({components:{BaseCard:d.A}})],v);const m=v,g=m;var C=s(1656),y=(0,C.A)(g,h,u,!1,null,null,null);const _=y.exports;var f=s(5185),k=s(7959),S=s(8091);let B=class extends((0,i.Xe)(f.A,k.A)){getBackgroundColor(){return"#FF69B4"}searchQuery="";shops=[];shopsLoaded=!1;async mounted(){try{this.shops=await(0,S.aF)(),this.shopsLoaded=!0}catch(e){console.error("加载商店数据失败:",e)}}get filteredShops(){if(!this.searchQuery.trim())return[];const e=this.searchQuery.toLowerCase().trim();return this.shops.filter(t=>t.name.toLowerCase().includes(e))}onSearchInput(e){const t=e.target;this.searchQuery=t.value}navigateToShop(e){console.log("导航到商店:",e),this.$router.push(`/shop/${e}`)}};B=(0,r.Cg)([(0,i.uA)({components:{TopBar:n.A,BottomBar:c.A,BottomMarquee:l.A,BackgroundImage:p.A,ShopCard:_}})],B);const A=B,w=A;var x=(0,C.A)(w,a,o,!1,null,"7c98766a",null);const Q=x.exports}}]);