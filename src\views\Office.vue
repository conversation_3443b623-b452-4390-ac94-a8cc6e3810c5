<template>
  <div class="office-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Office主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--office">
      <h1 class="app-title">{{ $t('pageTitle.office') }}</h1>
    </div>
    
    <!-- 筛选按钮 - 与标题同高度，固定不滚动 -->
    <div class="filter-buttons-fixed">
      <button 
        class="filter-btn by-floor"
        :class="{ active: sortBy === 'floor' }"
        @click="sortBy = 'floor'"
      >
        {{ $t('office.byFloor') }}
      </button>
      <button 
        class="filter-btn by-name"
        :class="{ active: sortBy === 'name' }"
        @click="sortBy = 'name'"
      >
        {{ $t('office.byName') }}
      </button>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--office">
        <!-- 公司列表 -->
        <div class="office-list">
          <OfficeCard
            v-for="office in sortedOffices"
            :key="office.id"
            :company-name="office.name"
            :room-number="office.room"
            :logo-src="office.logo"
            class="office-item"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import OfficeCard from '@/components/OfficeCard.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

interface OfficeItem {
  id: number
  name: string
  room: string
  logo?: string
  floor: number
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    OfficeCard,
    BackgroundImage
  }
})
export default class Office extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#4A4A4A' // Office页面的灰色背景
  }

  sortBy: 'name' | 'floor' = 'name'
  
  offices: OfficeItem[] = [
    {
      id: 1,
      name: 'Tesla Inc Kong Kong Branch',
      room: '104',
      floor: 1,
      logo: '/img/offices/tesla-logo.png'
    },
    {
      id: 2,
      name: 'Merrill Lynch',
      room: '812',
      floor: 8,
      logo: '/img/offices/merrill-lynch-logo.png'
    },
    {
      id: 3,
      name: 'Apple Inc Hong Kong',
      room: '903',
      floor: 9,
      logo: '/img/offices/apple-logo.png'
    },
    {
      id: 4,
      name: 'Xiaomi',
      room: '222',
      floor: 2,
      logo: '/img/offices/xiaomi-logo.png'
    },
    {
      id: 5,
      name: 'Microsoft Hong Kong 微軟香港',
      room: '888',
      floor: 8,
      logo: '/img/offices/microsoft-logo.png'
    },
    {
      id: 6,
      name: 'Amazon',
      room: '232',
      floor: 2,
      logo: '/img/offices/amazon-logo.png'
    },
    {
      id: 7,
      name: 'Facebook',
      room: '721',
      floor: 7,
      logo: '/img/offices/facebook-logo.png'
    }
  ]
  
  get sortedOffices() {
    const sorted = [...this.offices]
    if (this.sortBy === 'name') {
      return sorted.sort((a, b) => a.name.localeCompare(b.name))
    } else {
      return sorted.sort((a, b) => a.floor - b.floor)
    }
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
/* 筛选按钮 - 根据设计稿调整位置 */
.filter-buttons-fixed {
  position: absolute;
  top: 450px; /* 与Office标题同高度 */
  right: 86px; /* 右边距 */
  z-index: 15; /* 确保在其他内容之上 */
  display: flex;
  gap: 12px;
}

/* Office页面内容区域的特定间距 - 根据设计稿调整 */
.main-content-container--office {
  padding: 0 86px 60px; /* 右边距与按钮对齐 */
  position: relative;
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

.filter-btn {
  width: 351px;
  height: 135px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  font-family: 'Inter', sans-serif;
  font-weight: 200;
  font-size: 64px;
  line-height: 1.21;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
  
  /* 内阴影效果 */
  box-shadow: 
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.filter-btn.active {
  color: #E6FF06;
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 根据设计稿调整按钮位置 */
.filter-btn.by-floor {
  margin-right: 12px;
}

.filter-btn.by-name {
  /* by Name 按钮激活时的特殊样式 */
}

.filter-btn.by-name.active {
  color: #E6FF06;
}

/* 公司列表布局 - 根据设计稿调整 */
.office-list {
  display: flex;
  flex-direction: column;
  gap: 0; /* 移除卡片间距，符合设计稿 */
  width: 100%;
  max-width: 2000px; /* 限制最大宽度 */
  margin: 0 auto;
}

.office-item {
  width: 100%;
  flex-shrink: 0;
  margin-bottom: 30px;
}
</style> 