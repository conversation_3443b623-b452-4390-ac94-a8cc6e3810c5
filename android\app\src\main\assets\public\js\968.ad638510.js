"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[968],{968:(t,s,e)=>{e.r(s),e.d(s,{default:()=>y});var i=function(){var t=this,s=t._self._c;t._self._setupProxy;return s("div",{staticClass:"poster-container responsive-page-container",style:t.containerStyle},[s("BackgroundImage"),s("TopBar"),s("div",{staticClass:"page-title-fixed page-title-fixed--poster"},[s("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.poster")))])]),s("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[s("div",{staticClass:"main-content-container main-content-container--standard"},[s("div",{staticClass:"poster-swiper-container"},[s("swiper",{ref:"posterSwiper",staticClass:"poster-swiper",attrs:{options:t.swiperOptions}},t._l(t.displayPosters,function(e,i){return s("swiper-slide",{key:e.id||i,staticClass:"poster-slide"},[s("div",{staticClass:"poster-item"},[s("img",{staticClass:"poster-image",attrs:{src:t.getPosterImageUrl(e),alt:e.title},on:{error:t.onImageError}}),s("div",{staticClass:"poster-overlay"},[s("div",{staticClass:"poster-info"},[s("h3",{staticClass:"poster-name"},[t._v(t._s(e.title))]),s("p",{staticClass:"poster-description"},[t._v(t._s(e.description))])])])])])}),1),s("div",{staticClass:"swiper-pagination"})],1),s("div",{staticClass:"poster-controls"},[s("div",{staticClass:"control-buttons"},[s("button",{staticClass:"control-btn",attrs:{disabled:!t.canGoPrevious},on:{click:t.previousPoster}},[s("span",{staticClass:"control-icon"},[t._v("◀")]),s("span",{staticClass:"control-text"},[t._v(t._s(t.$t("poster.previous")))])]),s("button",{staticClass:"control-btn",class:{active:t.isPaused},on:{click:t.pauseAutoplay}},[s("span",{staticClass:"control-icon"},[t._v(t._s(t.isPaused?"▶":"⏸"))]),s("span",{staticClass:"control-text"},[t._v(t._s(t.isPaused?t.$t("poster.play"):t.$t("poster.pause")))])]),s("button",{staticClass:"control-btn",attrs:{disabled:!t.canGoNext},on:{click:t.nextPoster}},[s("span",{staticClass:"control-icon"},[t._v("▶")]),s("span",{staticClass:"control-text"},[t._v(t._s(t.$t("poster.next")))])])]),s("div",{staticClass:"poster-counter"},[s("span",{staticClass:"counter-text"},[t._v(t._s(t.currentPosterIndex+1)+" / "+t._s(t.displayPosters.length))])])])])]),s("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},o=[],r=e(635),a=e(233),n=e(353),l=e(14),p=e(958),c=e(256),d=e(185),u=e(959),h=e(276),g=e(296);let C=class extends((0,a.Xe)(d.A,u.A)){posters;hasConfig;currentPosterIndex=0;isPaused=!1;getBackgroundColor(){return"#FF6B6B"}get defaultPosters(){return[{id:"1",title:this.$t("posterContent.splus.title"),description:this.$t("posterContent.splus.description"),url:"img/posters/poster02.jpg",type:"image"},{id:"2",title:this.$t("posterContent.ikea.title"),description:this.$t("posterContent.ikea.description"),url:"img/posters/poster01.jpg",type:"image"},{id:"3",title:this.$t("posterContent.more.title"),description:this.$t("posterContent.more.description"),url:"img/posters/poster02.jpg",type:"image"}]}get displayPosters(){return this.hasConfig&&this.posters&&this.posters.length>0?(console.log("使用远程海报配置"),this.posters.map((t,s)=>({id:String(t.id),title:t.alternativeText||`${this.$t("poster.defaultTitle")} ${s+1}`,description:t.caption||`${this.$t("poster.defaultDescription")} ${s+1}`,url:g.A.buildImageUrl(t.url),type:"image"}))):(console.log("使用本地默认海报"),this.defaultPosters)}getPosterImageUrl(t){return t.url.startsWith("http://")||t.url.startsWith("https://")||t.url.startsWith("/")?t.url:`/${t.url}`}get displayPostersWatch(){return this.displayPosters.length}swiperOptions={direction:"horizontal",slidesPerView:1,spaceBetween:0,centeredSlides:!0,loop:!0,initialSlide:0,autoplay:{delay:3e3,disableOnInteraction:!1,pauseOnMouseEnter:!0},effect:"slide",speed:800,pagination:{el:".swiper-pagination",clickable:!0,dynamicBullets:!0},touchRatio:1,touchAngle:45,simulateTouch:!0,allowTouchMove:!0,touchStartPreventDefault:!1,touchMoveStopPropagation:!1,touchReleaseOnEdges:!1,mousewheel:{invert:!1,sensitivity:1,thresholdDelta:50},keyboard:{enabled:!0,onlyInViewport:!0},on:{init:()=>{this.$nextTick(()=>{this.updateCurrentIndex()})},slideChange:()=>{this.updateCurrentIndex()}}};get canGoPrevious(){return this.currentPosterIndex>0||this.displayPosters.length>1}get canGoNext(){return this.currentPosterIndex<this.displayPosters.length-1||this.displayPosters.length>1}mounted(){console.log("Poster组件已挂载，海报数量:",this.displayPosters.length),this.$nextTick(()=>{setTimeout(()=>{this.initializeSwiper()},100)})}initializeSwiper(){const t=this.$refs.posterSwiper?.$swiper;t?(console.log("Swiper 初始化成功"),t.slideTo(0,0),this.updateCurrentIndex(),t.autoplay&&!this.isPaused&&t.autoplay.start()):console.error("Swiper 初始化失败")}updateCurrentIndex(){const t=this.$refs.posterSwiper?.$swiper;t&&(this.currentPosterIndex=t.realIndex)}previousPoster(){const t=this.$refs.posterSwiper?.$swiper;t&&t.slidePrev()}nextPoster(){const t=this.$refs.posterSwiper?.$swiper;t&&t.slideNext()}pauseAutoplay(){const t=this.$refs.posterSwiper?.$swiper;t&&(this.isPaused?(t.autoplay.start(),this.isPaused=!1):(t.autoplay.stop(),this.isPaused=!0))}goToPoster(t){const s=this.$refs.posterSwiper?.$swiper;s&&t>=0&&t<this.displayPosters.length&&s.slideTo(t)}onImageError(t){const s=t.target;s&&(s.src="/img/placeholder-poster.png",console.warn("海报图片加载失败，使用占位图片"))}};C=(0,r.Cg)([(0,a.uA)({components:{TopBar:l.A,BottomBar:p.A,BackgroundImage:c.A,Swiper:h.Swiper,SwiperSlide:h.SwiperSlide},computed:{...(0,n.L8)("config",["posters","hasConfig"])},watch:{displayPosters:{handler(t){t&&t.length>0&&(console.log("海报数据更新，重新初始化 Swiper"),this.$nextTick(()=>{setTimeout(()=>{this.initializeSwiper()},200)}))},immediate:!1}}})],C);const P=C,m=P;var v=e(656),w=(0,v.A)(m,i,o,!1,null,"57d11dfc",null);const y=w.exports}}]);