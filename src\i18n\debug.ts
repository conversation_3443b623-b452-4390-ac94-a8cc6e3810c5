// 語言系統調試工具
import i18nService, { Language } from './index'
import { detectAndSetLanguage, getSystemLanguageInfo } from './detector'

/**
 * 調試語言系統
 */
export function debugLanguageSystem(vueInstance?: any) {
  console.group('🌐 語言系統調試信息')
  
  // 1. 檢查語言包是否已加載
  console.log('1. 語言包加載狀態:')
  const languages: Language[] = ['zh-TW', 'en', 'es']
  languages.forEach(lang => {
    const testKey = 'common.loading'
    const translation = i18nService.t(testKey)
    console.log(`   ${lang}: ${translation ? '✅ 已加載' : '❌ 未加載'}`)
  })
  
  // 2. 系統語言信息
  const sysInfo = getSystemLanguageInfo()
  console.log('2. 系統語言信息:', sysInfo)
  
  // 3. 當前語言設置
  console.log('3. 當前語言:', i18nService.getCurrentLanguage())
  
  // 4. 測試翻譯
  console.log('4. 測試翻譯:')
  const testKeys = ['nav.shop', 'nav.food', 'common.loading']
  testKeys.forEach(key => {
    console.log(`   ${key}: "${i18nService.t(key)}"`)
  })
  
  // 5. 重新檢測並設置語言
  console.log('5. 重新檢測並設置語言...')
  const detectedLang = detectAndSetLanguage(vueInstance)
  console.log(`   檢測結果: ${detectedLang}`)
  
  // 6. 再次測試翻譯
  console.log('6. 設置後的翻譯測試:')
  testKeys.forEach(key => {
    console.log(`   ${key}: "${i18nService.t(key)}"`)
  })
  
  console.groupEnd()
}

/**
 * 手動切換語言並調試
 */
export function debugSwitchLanguage(language: Language, vueInstance?: any) {
  console.group(`🔄 手動切換語言到: ${language}`)
  
  // 設置語言
  i18nService.setCurrentLanguage(language)
  
  // 觸發事件
  if (vueInstance && vueInstance.$root) {
    vueInstance.$root.$emit('language-changed', language)
  }
  
  // 測試結果
  console.log('切換後的測試:')
  const testKeys = ['nav.shop', 'nav.food', 'common.loading']
  testKeys.forEach(key => {
    console.log(`   ${key}: "${i18nService.t(key)}"`)
  })
  
  console.groupEnd()
}

// 將調試函數掛載到全局，方便在控制台使用
if (typeof window !== 'undefined') {
  (window as any).__debugLanguage = {
    info: debugLanguageSystem,
    switch: debugSwitchLanguage,
    service: i18nService
  }
}
