(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[121],{23:(e,t,n)=>{"use strict";function r(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}n.d(t,{xI:()=>$e});var i=r();function s(e){i=e}var o={exec:()=>null};function a(e,t=""){let n="string"==typeof e?e:e.source,r={replace:(e,t)=>{let i="string"==typeof t?t:t.source;return i=i.replace(l.caret,"$1"),n=n.replace(e,i),r},getRegex:()=>new RegExp(n,t)};return r}var l={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},c=/^(?:[ \t]*(?:\n|$))+/,u=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,d=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,p=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,h=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,f=/(?:[*+-]|\d{1,9}[.)])/,m=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,g=a(m).replace(/bull/g,f).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),v=a(m).replace(/bull/g,f).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),y=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,b=/^[^\n]+/,w=/(?!\s*\])(?:\\.|[^\[\]\\])+/,x=a(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",w).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),E=a(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,f).getRegex(),S="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",C=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,T=a("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",C).replace("tag",S).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),k=a(y).replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex(),_=a(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",k).getRegex(),O={blockquote:_,code:u,def:x,fences:d,heading:h,hr:p,html:T,lheading:g,list:E,newline:c,paragraph:k,table:o,text:b},$=a("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex(),R={...O,lheading:v,table:$,paragraph:a(y).replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",$).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex()},P={...O,html:a("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",C).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:o,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:a(y).replace("hr",p).replace("heading"," *#{1,6} *[^\n]").replace("lheading",g).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},A=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,I=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,L=/^( {2,}|\\)\n(?!\s*$)/,M=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,N=/[\p{P}\p{S}]/u,j=/[\s\p{P}\p{S}]/u,D=/[^\s\p{P}\p{S}]/u,z=a(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,j).getRegex(),F=/(?!~)[\p{P}\p{S}]/u,B=/(?!~)[\s\p{P}\p{S}]/u,U=/(?:[^\s\p{P}\p{S}]|~)/u,q=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,H=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,G=a(H,"u").replace(/punct/g,N).getRegex(),V=a(H,"u").replace(/punct/g,F).getRegex(),W="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Y=a(W,"gu").replace(/notPunctSpace/g,D).replace(/punctSpace/g,j).replace(/punct/g,N).getRegex(),X=a(W,"gu").replace(/notPunctSpace/g,U).replace(/punctSpace/g,B).replace(/punct/g,F).getRegex(),K=a("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,D).replace(/punctSpace/g,j).replace(/punct/g,N).getRegex(),J=a(/\\(punct)/,"gu").replace(/punct/g,N).getRegex(),Z=a(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Q=a(C).replace("(?:--\x3e|$)","--\x3e").getRegex(),ee=a("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Q).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),te=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,ne=a(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",te).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),re=a(/^!?\[(label)\]\[(ref)\]/).replace("label",te).replace("ref",w).getRegex(),ie=a(/^!?\[(ref)\](?:\[\])?/).replace("ref",w).getRegex(),se=a("reflink|nolink(?!\\()","g").replace("reflink",re).replace("nolink",ie).getRegex(),oe={_backpedal:o,anyPunctuation:J,autolink:Z,blockSkip:q,br:L,code:I,del:o,emStrongLDelim:G,emStrongRDelimAst:Y,emStrongRDelimUnd:K,escape:A,link:ne,nolink:ie,punctuation:z,reflink:re,reflinkSearch:se,tag:ee,text:M,url:o},ae={...oe,link:a(/^!?\[(label)\]\((.*?)\)/).replace("label",te).getRegex(),reflink:a(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",te).getRegex()},le={...oe,emStrongRDelimAst:X,emStrongLDelim:V,url:a(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ce={...le,br:a(L).replace("{2,}","*").getRegex(),text:a(le.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ue={normal:O,gfm:R,pedantic:P},de={normal:oe,gfm:le,breaks:ce,pedantic:ae},pe={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},he=e=>pe[e];function fe(e,t){if(t){if(l.escapeTest.test(e))return e.replace(l.escapeReplace,he)}else if(l.escapeTestNoEncode.test(e))return e.replace(l.escapeReplaceNoEncode,he);return e}function me(e){try{e=encodeURI(e).replace(l.percentDecode,"%")}catch{return null}return e}function ge(e,t){let n=e.replace(l.findPipe,(e,t,n)=>{let r=!1,i=t;for(;--i>=0&&"\\"===n[i];)r=!r;return r?"|":" |"}),r=n.split(l.splitPipe),i=0;if(r[0].trim()||r.shift(),r.length>0&&!r.at(-1)?.trim()&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(l.slashPipe,"|");return r}function ve(e,t,n){let r=e.length;if(0===r)return"";let i=0;for(;i<r;){let s=e.charAt(r-i-1);if(s!==t||n){if(s===t||!n)break;i++}else i++}return e.slice(0,r-i)}function ye(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return n>0?-2:-1}function be(e,t,n,r,i){let s=t.href,o=t.title||null,a=e[1].replace(i.other.outputLinkReplace,"$1");r.state.inLink=!0;let l={type:"!"===e[0].charAt(0)?"image":"link",raw:n,href:s,title:o,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}function we(e,t,n){let r=e.match(n.other.indentCodeCompensation);if(null===r)return t;let i=r[1];return t.split("\n").map(e=>{let t=e.match(n.other.beginningSpace);if(null===t)return e;let[r]=t;return r.length>=i.length?e.slice(i.length):e}).join("\n")}var xe=class{options;rules;lexer;constructor(e){this.options=e||i}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:ve(e,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let e=t[0],n=we(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){let t=ve(e,"#");(this.options.pedantic||!t||this.rules.other.endingSpaceChar.test(t))&&(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ve(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let e=ve(t[0],"\n").split("\n"),n="",r="",i=[];for(;e.length>0;){let t,s=!1,o=[];for(t=0;t<e.length;t++)if(this.rules.other.blockquoteStart.test(e[t]))o.push(e[t]),s=!0;else{if(s)break;o.push(e[t])}e=e.slice(t);let a=o.join("\n"),l=a.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}\n${a}`:a,r=r?`${r}\n${l}`:l;let c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(l,i,!0),this.lexer.state.top=c,0===e.length)break;let u=i.at(-1);if("code"===u?.type)break;if("blockquote"===u?.type){let t=u,s=t.raw+"\n"+e.join("\n"),o=this.blockquote(s);i[i.length-1]=o,n=n.substring(0,n.length-t.raw.length)+o.raw,r=r.substring(0,r.length-t.text.length)+o.text;break}if("list"===u?.type){let t=u,s=t.raw+"\n"+e.join("\n"),o=this.list(s);i[i.length-1]=o,n=n.substring(0,n.length-u.raw.length)+o.raw,r=r.substring(0,r.length-t.raw.length)+o.raw,e=s.substring(i.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:n,tokens:i,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,i={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let s=this.rules.other.listItemRegex(n),o=!1;for(;e;){let n=!1,r="",a="";if(!(t=s.exec(e))||this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let l=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,e=>" ".repeat(3*e.length)),c=e.split("\n",1)[0],u=!l.trim(),d=0;if(this.options.pedantic?(d=2,a=l.trimStart()):u?d=t[1].length+1:(d=t[2].search(this.rules.other.nonSpaceChar),d=d>4?1:d,a=l.slice(d),d+=t[1].length),u&&this.rules.other.blankLine.test(c)&&(r+=c+"\n",e=e.substring(c.length+1),n=!0),!n){let t=this.rules.other.nextBulletRegex(d),n=this.rules.other.hrRegex(d),i=this.rules.other.fencesBeginRegex(d),s=this.rules.other.headingBeginRegex(d),o=this.rules.other.htmlBeginRegex(d);for(;e;){let p,h=e.split("\n",1)[0];if(c=h,this.options.pedantic?(c=c.replace(this.rules.other.listReplaceNesting,"  "),p=c):p=c.replace(this.rules.other.tabCharGlobal,"    "),i.test(c)||s.test(c)||o.test(c)||t.test(c)||n.test(c))break;if(p.search(this.rules.other.nonSpaceChar)>=d||!c.trim())a+="\n"+p.slice(d);else{if(u||l.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||i.test(l)||s.test(l)||n.test(l))break;a+="\n"+c}!u&&!c.trim()&&(u=!0),r+=h+"\n",e=e.substring(h.length+1),l=p.slice(d)}}i.loose||(o?i.loose=!0:this.rules.other.doubleBlankLine.test(r)&&(o=!0));let p,h=null;this.options.gfm&&(h=this.rules.other.listIsTask.exec(a),h&&(p="[ ] "!==h[0],a=a.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:r,task:!!h,checked:p,loose:!1,text:a,tokens:[]}),i.raw+=r}let a=i.items.at(-1);if(!a)return;a.raw=a.raw.trimEnd(),a.text=a.text.trimEnd(),i.raw=i.raw.trimEnd();for(let e=0;e<i.items.length;e++)if(this.lexer.state.top=!1,i.items[e].tokens=this.lexer.blockTokens(i.items[e].text,[]),!i.loose){let t=i.items[e].tokens.filter(e=>"space"===e.type),n=t.length>0&&t.some(e=>this.rules.other.anyLine.test(e.raw));i.loose=n}if(i.loose)for(let e=0;e<i.items.length;e++)i.items[e].loose=!0;return i}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=ge(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],s={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let e of r)this.rules.other.tableAlignRight.test(e)?s.align.push("right"):this.rules.other.tableAlignCenter.test(e)?s.align.push("center"):this.rules.other.tableAlignLeft.test(e)?s.align.push("left"):s.align.push(null);for(let e=0;e<n.length;e++)s.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:s.align[e]});for(let e of i)s.rows.push(ge(e,s.header.length).map((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:s.align[t]})));return s}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;let t=ve(e.slice(0,-1),"\\");if((e.length-t.length)%2===0)return}else{let e=ye(t[2],"()");if(-2===e)return;if(e>-1){let n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){let e=this.rules.other.pedanticHrefTitle.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(n=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?n.slice(1):n.slice(1,-1)),be(t,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),r=t[e.toLowerCase()];if(!r){let e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return be(n,r,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){let n,i,s=[...r[0]].length-1,o=s,a=0,l="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,t=t.slice(-1*e.length+s);null!=(r=l.exec(t));){if(n=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!n)continue;if(i=[...n].length,r[3]||r[4]){o+=i;continue}if((r[5]||r[6])&&s%3&&!((s+i)%3)){a+=i;continue}if(o-=i,o>0)continue;i=Math.min(i,i+o+a);let t=[...r[0]][0].length,l=e.slice(0,s+r.index+t+i);if(Math.min(s,i)%2){let e=l.slice(1,-1);return{type:"em",raw:l,text:e,tokens:this.lexer.inlineTokens(e)}}let c=l.slice(2,-2);return{type:"strong",raw:l,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," "),n=this.rules.other.nonSpaceChar.test(e),r=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return n&&r&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=t[1],n="mailto:"+e):(e=t[1],n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=t[0],n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=t[0],n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}},Ee=class e{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||i,this.options.tokenizer=this.options.tokenizer||new xe,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:l,block:ue.normal,inline:de.normal};this.options.pedantic?(t.block=ue.pedantic,t.inline=de.pedantic):this.options.gfm&&(t.block=ue.gfm,this.options.breaks?t.inline=de.breaks:t.inline=de.gfm),this.tokenizer.rules=t}static get rules(){return{block:ue,inline:de}}static lex(t,n){return new e(n).lex(t)}static lexInline(t,n){return new e(n).inlineTokens(t)}lex(e){e=e.replace(l.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let e=this.inlineQueue[t];this.inlineTokens(e.src,e.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(l.tabCharGlobal,"    ").replace(l.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let n=t.at(-1);1===r.raw.length&&void 0!==n?n.raw+="\n":t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.raw,this.inlineQueue.at(-1).src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let t,n=1/0,r=e.slice(1);this.options.extensions.startBlock.forEach(e=>{t=e.call({lexer:this},r),"number"==typeof t&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(i=e.substring(0,n+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let s=t.at(-1);n&&"paragraph"===s?.type?(s.raw+="\n"+r.raw,s.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){let e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(n));)e.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.anyPunctuation.exec(n));)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(n));)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,s="";for(;e;){let r;if(i||(s=""),i=!1,this.options.extensions?.inline?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.escape(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.tag(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.link(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===r.type&&"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(r=this.tokenizer.emStrong(e,n,s)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.codespan(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.br(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.del(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.autolink(e)){e=e.substring(r.raw.length),t.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}let o=e;if(this.options.extensions?.startInline){let t,n=1/0,r=e.slice(1);this.options.extensions.startInline.forEach(e=>{t=e.call({lexer:this},r),"number"==typeof t&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(o=e.substring(0,n+1))}if(r=this.tokenizer.inlineText(o)){e=e.substring(r.raw.length),"_"!==r.raw.slice(-1)&&(s=r.raw.slice(-1)),i=!0;let n=t.at(-1);"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return t}},Se=class{options;parser;constructor(e){this.options=e||i}space(e){return""}code({text:e,lang:t,escaped:n}){let r=(t||"").match(l.notSpaceStart)?.[0],i=e.replace(l.endingNewline,"")+"\n";return r?'<pre><code class="language-'+fe(r)+'">'+(n?i:fe(i,!0))+"</code></pre>\n":"<pre><code>"+(n?i:fe(i,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,n=e.start,r="";for(let o=0;o<e.items.length;o++){let t=e.items[o];r+=this.listitem(t)}let i=t?"ol":"ul",s=t&&1!==n?' start="'+n+'"':"";return"<"+i+s+">\n"+r+"</"+i+">\n"}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?"paragraph"===e.tokens[0]?.type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+fe(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let i=0;i<e.header.length;i++)n+=this.tablecell(e.header[i]);t+=this.tablerow({text:n});let r="";for(let i=0;i<e.rows.length;i++){let t=e.rows[i];n="";for(let e=0;e<t.length;e++)n+=this.tablecell(t[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${fe(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),i=me(e);if(null===i)return r;e=i;let s='<a href="'+e+'"';return t&&(s+=' title="'+fe(t)+'"'),s+=">"+r+"</a>",s}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));let i=me(e);if(null===i)return fe(n);e=i;let s=`<img src="${e}" alt="${n}"`;return t&&(s+=` title="${fe(t)}"`),s+=">",s}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:fe(e.text)}},Ce=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},Te=class e{options;renderer;textRenderer;constructor(e){this.options=e||i,this.options.renderer=this.options.renderer||new Se,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Ce}static parse(t,n){return new e(n).parse(t)}static parseInline(t,n){return new e(n).parseInline(t)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=i,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}let s=i;switch(s.type){case"space":n+=this.renderer.space(s);continue;case"hr":n+=this.renderer.hr(s);continue;case"heading":n+=this.renderer.heading(s);continue;case"code":n+=this.renderer.code(s);continue;case"table":n+=this.renderer.table(s);continue;case"blockquote":n+=this.renderer.blockquote(s);continue;case"list":n+=this.renderer.list(s);continue;case"html":n+=this.renderer.html(s);continue;case"paragraph":n+=this.renderer.paragraph(s);continue;case"text":{let i=s,o=this.renderer.text(i);for(;r+1<e.length&&"text"===e[r+1].type;)i=e[++r],o+="\n"+this.renderer.text(i);n+=t?this.renderer.paragraph({type:"paragraph",raw:o,text:o,tokens:[{type:"text",raw:o,text:o,escaped:!0}]}):o;continue}default:{let e='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e,t=this.renderer){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=e||"";continue}}let s=i;switch(s.type){case"escape":n+=t.text(s);break;case"html":n+=t.html(s);break;case"link":n+=t.link(s);break;case"image":n+=t.image(s);break;case"strong":n+=t.strong(s);break;case"em":n+=t.em(s);break;case"codespan":n+=t.codespan(s);break;case"br":n+=t.br(s);break;case"del":n+=t.del(s);break;case"text":n+=t.text(s);break;default:{let e='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}},ke=class{options;block;constructor(e){this.options=e||i}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?Ee.lex:Ee.lexInline}provideParser(){return this.block?Te.parse:Te.parseInline}},_e=class{defaults=r();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=Te;Renderer=Se;TextRenderer=Ce;Lexer=Ee;Tokenizer=xe;Hooks=ke;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{let e=r;for(let r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(let r of e.rows)for(let e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{let e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{let e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach(r=>{let i=e[r].flat(1/0);n=n.concat(this.walkTokens(i,t))}):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(e=>{let n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(e=>{if(!e.name)throw new Error("extension name required");if("renderer"in e){let n=t.renderers[e.name];t.renderers[e.name]=n?function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");let n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)}),n.extensions=t),e.renderer){let t=this.defaults.renderer||new Se(this.defaults);for(let n in e.renderer){if(!(n in t))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;let r=n,i=e.renderer[r],s=t[r];t[r]=(...e)=>{let n=i.apply(t,e);return!1===n&&(n=s.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){let t=this.defaults.tokenizer||new xe(this.defaults);for(let n in e.tokenizer){if(!(n in t))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;let r=n,i=e.tokenizer[r],s=t[r];t[r]=(...e)=>{let n=i.apply(t,e);return!1===n&&(n=s.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){let t=this.defaults.hooks||new ke;for(let n in e.hooks){if(!(n in t))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;let r=n,i=e.hooks[r],s=t[r];ke.passThroughHooks.has(n)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(i.call(t,e)).then(e=>s.call(t,e));let n=i.call(t,e);return s.call(t,n)}:t[r]=(...e)=>{let n=i.apply(t,e);return!1===n&&(n=s.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){let t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return Ee.lex(e,t??this.defaults)}parser(e,t){return Te.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{let r={...n},i={...this.defaults,...r},s=this.onError(!!i.silent,!!i.async);if(!0===this.defaults.async&&!1===r.async)return s(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||null===t)return s(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=e);let o=i.hooks?i.hooks.provideLexer():e?Ee.lex:Ee.lexInline,a=i.hooks?i.hooks.provideParser():e?Te.parse:Te.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(e=>o(e,i)).then(e=>i.hooks?i.hooks.processAllTokens(e):e).then(e=>i.walkTokens?Promise.all(this.walkTokens(e,i.walkTokens)).then(()=>e):e).then(e=>a(e,i)).then(e=>i.hooks?i.hooks.postprocess(e):e).catch(s);try{i.hooks&&(t=i.hooks.preprocess(t));let e=o(t,i);i.hooks&&(e=i.hooks.processAllTokens(e)),i.walkTokens&&this.walkTokens(e,i.walkTokens);let n=a(e,i);return i.hooks&&(n=i.hooks.postprocess(n)),n}catch(l){return s(l)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let e="<p>An error occurred:</p><pre>"+fe(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}},Oe=new _e;function $e(e,t){return Oe.parse(e,t)}$e.options=$e.setOptions=function(e){return Oe.setOptions(e),$e.defaults=Oe.defaults,s($e.defaults),$e},$e.getDefaults=r,$e.defaults=i,$e.use=function(...e){return Oe.use(...e),$e.defaults=Oe.defaults,s($e.defaults),$e},$e.walkTokens=function(e,t){return Oe.walkTokens(e,t)},$e.parseInline=Oe.parseInline,$e.Parser=Te,$e.parser=Te.parse,$e.Renderer=Se,$e.TextRenderer=Ce,$e.Lexer=Ee,$e.lexer=Ee.lex,$e.Tokenizer=xe,$e.Hooks=ke,$e.parse=$e;$e.options,$e.setOptions,$e.use,$e.walkTokens,$e.parseInline,Te.parse,Ee.lex},173:(e,t,n)=>{"use strict";function r(e,t){for(var n in t)e[n]=t[n];return e}n.d(t,{Ay:()=>Et});var i=/[!'()*]/g,s=function(e){return"%"+e.charCodeAt(0).toString(16)},o=/%2C/g,a=function(e){return encodeURIComponent(e).replace(i,s).replace(o,",")};function l(e){try{return decodeURIComponent(e)}catch(t){0}return e}function c(e,t,n){void 0===t&&(t={});var r,i=n||d;try{r=i(e||"")}catch(a){r={}}for(var s in t){var o=t[s];r[s]=Array.isArray(o)?o.map(u):u(o)}return r}var u=function(e){return null==e||"object"===typeof e?e:String(e)};function d(e){var t={};return e=e.trim().replace(/^(\?|#|&)/,""),e?(e.split("&").forEach(function(e){var n=e.replace(/\+/g," ").split("="),r=l(n.shift()),i=n.length>0?l(n.join("=")):null;void 0===t[r]?t[r]=i:Array.isArray(t[r])?t[r].push(i):t[r]=[t[r],i]}),t):t}function p(e){var t=e?Object.keys(e).map(function(t){var n=e[t];if(void 0===n)return"";if(null===n)return a(t);if(Array.isArray(n)){var r=[];return n.forEach(function(e){void 0!==e&&(null===e?r.push(a(t)):r.push(a(t)+"="+a(e)))}),r.join("&")}return a(t)+"="+a(n)}).filter(function(e){return e.length>0}).join("&"):null;return t?"?"+t:""}var h=/\/?$/;function f(e,t,n,r){var i=r&&r.options.stringifyQuery,s=t.query||{};try{s=m(s)}catch(a){}var o={name:t.name||e&&e.name,meta:e&&e.meta||{},path:t.path||"/",hash:t.hash||"",query:s,params:t.params||{},fullPath:y(t,i),matched:e?v(e):[]};return n&&(o.redirectedFrom=y(n,i)),Object.freeze(o)}function m(e){if(Array.isArray(e))return e.map(m);if(e&&"object"===typeof e){var t={};for(var n in e)t[n]=m(e[n]);return t}return e}var g=f(null,{path:"/"});function v(e){var t=[];while(e)t.unshift(e),e=e.parent;return t}function y(e,t){var n=e.path,r=e.query;void 0===r&&(r={});var i=e.hash;void 0===i&&(i="");var s=t||p;return(n||"/")+s(r)+i}function b(e,t,n){return t===g?e===t:!!t&&(e.path&&t.path?e.path.replace(h,"")===t.path.replace(h,"")&&(n||e.hash===t.hash&&w(e.query,t.query)):!(!e.name||!t.name)&&(e.name===t.name&&(n||e.hash===t.hash&&w(e.query,t.query)&&w(e.params,t.params))))}function w(e,t){if(void 0===e&&(e={}),void 0===t&&(t={}),!e||!t)return e===t;var n=Object.keys(e).sort(),r=Object.keys(t).sort();return n.length===r.length&&n.every(function(n,i){var s=e[n],o=r[i];if(o!==n)return!1;var a=t[n];return null==s||null==a?s===a:"object"===typeof s&&"object"===typeof a?w(s,a):String(s)===String(a)})}function x(e,t){return 0===e.path.replace(h,"/").indexOf(t.path.replace(h,"/"))&&(!t.hash||e.hash===t.hash)&&E(e.query,t.query)}function E(e,t){for(var n in t)if(!(n in e))return!1;return!0}function S(e){for(var t=0;t<e.matched.length;t++){var n=e.matched[t];for(var r in n.instances){var i=n.instances[r],s=n.enteredCbs[r];if(i&&s){delete n.enteredCbs[r];for(var o=0;o<s.length;o++)i._isBeingDestroyed||s[o](i)}}}}var C={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render:function(e,t){var n=t.props,i=t.children,s=t.parent,o=t.data;o.routerView=!0;var a=s.$createElement,l=n.name,c=s.$route,u=s._routerViewCache||(s._routerViewCache={}),d=0,p=!1;while(s&&s._routerRoot!==s){var h=s.$vnode?s.$vnode.data:{};h.routerView&&d++,h.keepAlive&&s._directInactive&&s._inactive&&(p=!0),s=s.$parent}if(o.routerViewDepth=d,p){var f=u[l],m=f&&f.component;return m?(f.configProps&&T(m,o,f.route,f.configProps),a(m,o,i)):a()}var g=c.matched[d],v=g&&g.components[l];if(!g||!v)return u[l]=null,a();u[l]={component:v},o.registerRouteInstance=function(e,t){var n=g.instances[l];(t&&n!==e||!t&&n===e)&&(g.instances[l]=t)},(o.hook||(o.hook={})).prepatch=function(e,t){g.instances[l]=t.componentInstance},o.hook.init=function(e){e.data.keepAlive&&e.componentInstance&&e.componentInstance!==g.instances[l]&&(g.instances[l]=e.componentInstance),S(c)};var y=g.props&&g.props[l];return y&&(r(u[l],{route:c,configProps:y}),T(v,o,c,y)),a(v,o,i)}};function T(e,t,n,i){var s=t.props=k(n,i);if(s){s=t.props=r({},s);var o=t.attrs=t.attrs||{};for(var a in s)e.props&&a in e.props||(o[a]=s[a],delete s[a])}}function k(e,t){switch(typeof t){case"undefined":return;case"object":return t;case"function":return t(e);case"boolean":return t?e.params:void 0;default:0}}function _(e,t,n){var r=e.charAt(0);if("/"===r)return e;if("?"===r||"#"===r)return t+e;var i=t.split("/");n&&i[i.length-1]||i.pop();for(var s=e.replace(/^\//,"").split("/"),o=0;o<s.length;o++){var a=s[o];".."===a?i.pop():"."!==a&&i.push(a)}return""!==i[0]&&i.unshift(""),i.join("/")}function O(e){var t="",n="",r=e.indexOf("#");r>=0&&(t=e.slice(r),e=e.slice(0,r));var i=e.indexOf("?");return i>=0&&(n=e.slice(i+1),e=e.slice(0,i)),{path:e,query:n,hash:t}}function $(e){return e.replace(/\/(?:\s*\/)+/g,"/")}var R=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)},P=K,A=j,I=D,L=B,M=X,N=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function j(e,t){var n,r=[],i=0,s=0,o="",a=t&&t.delimiter||"/";while(null!=(n=N.exec(e))){var l=n[0],c=n[1],u=n.index;if(o+=e.slice(s,u),s=u+l.length,c)o+=c[1];else{var d=e[s],p=n[2],h=n[3],f=n[4],m=n[5],g=n[6],v=n[7];o&&(r.push(o),o="");var y=null!=p&&null!=d&&d!==p,b="+"===g||"*"===g,w="?"===g||"*"===g,x=n[2]||a,E=f||m;r.push({name:h||i++,prefix:p||"",delimiter:x,optional:w,repeat:b,partial:y,asterisk:!!v,pattern:E?q(E):v?".*":"[^"+U(x)+"]+?"})}}return s<e.length&&(o+=e.substr(s)),o&&r.push(o),r}function D(e,t){return B(j(e,t),t)}function z(e){return encodeURI(e).replace(/[\/?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function F(e){return encodeURI(e).replace(/[?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function B(e,t){for(var n=new Array(e.length),r=0;r<e.length;r++)"object"===typeof e[r]&&(n[r]=new RegExp("^(?:"+e[r].pattern+")$",G(t)));return function(t,r){for(var i="",s=t||{},o=r||{},a=o.pretty?z:encodeURIComponent,l=0;l<e.length;l++){var c=e[l];if("string"!==typeof c){var u,d=s[c.name];if(null==d){if(c.optional){c.partial&&(i+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(R(d)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(d)+"`");if(0===d.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var p=0;p<d.length;p++){if(u=a(d[p]),!n[l].test(u))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(u)+"`");i+=(0===p?c.prefix:c.delimiter)+u}}else{if(u=c.asterisk?F(d):a(d),!n[l].test(u))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+u+'"');i+=c.prefix+u}}else i+=c}return i}}function U(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function q(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function H(e,t){return e.keys=t,e}function G(e){return e&&e.sensitive?"":"i"}function V(e,t){var n=e.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)t.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return H(e,t)}function W(e,t,n){for(var r=[],i=0;i<e.length;i++)r.push(K(e[i],t,n).source);var s=new RegExp("(?:"+r.join("|")+")",G(n));return H(s,t)}function Y(e,t,n){return X(j(e,n),t,n)}function X(e,t,n){R(t)||(n=t||n,t=[]),n=n||{};for(var r=n.strict,i=!1!==n.end,s="",o=0;o<e.length;o++){var a=e[o];if("string"===typeof a)s+=U(a);else{var l=U(a.prefix),c="(?:"+a.pattern+")";t.push(a),a.repeat&&(c+="(?:"+l+c+")*"),c=a.optional?a.partial?l+"("+c+")?":"(?:"+l+"("+c+"))?":l+"("+c+")",s+=c}}var u=U(n.delimiter||"/"),d=s.slice(-u.length)===u;return r||(s=(d?s.slice(0,-u.length):s)+"(?:"+u+"(?=$))?"),s+=i?"$":r&&d?"":"(?="+u+"|$)",H(new RegExp("^"+s,G(n)),t)}function K(e,t,n){return R(t)||(n=t||n,t=[]),n=n||{},e instanceof RegExp?V(e,t):R(e)?W(e,t,n):Y(e,t,n)}P.parse=A,P.compile=I,P.tokensToFunction=L,P.tokensToRegExp=M;var J=Object.create(null);function Z(e,t,n){t=t||{};try{var r=J[e]||(J[e]=P.compile(e));return"string"===typeof t.pathMatch&&(t[0]=t.pathMatch),r(t,{pretty:!0})}catch(i){return""}finally{delete t[0]}}function Q(e,t,n,i){var s="string"===typeof e?{path:e}:e;if(s._normalized)return s;if(s.name){s=r({},e);var o=s.params;return o&&"object"===typeof o&&(s.params=r({},o)),s}if(!s.path&&s.params&&t){s=r({},s),s._normalized=!0;var a=r(r({},t.params),s.params);if(t.name)s.name=t.name,s.params=a;else if(t.matched.length){var l=t.matched[t.matched.length-1].path;s.path=Z(l,a,"path "+t.path)}else 0;return s}var u=O(s.path||""),d=t&&t.path||"/",p=u.path?_(u.path,d,n||s.append):d,h=c(u.query,s.query,i&&i.options.parseQuery),f=s.hash||u.hash;return f&&"#"!==f.charAt(0)&&(f="#"+f),{_normalized:!0,path:p,query:h,hash:f}}var ee,te=[String,Object],ne=[String,Array],re=function(){},ie={name:"RouterLink",props:{to:{type:te,required:!0},tag:{type:String,default:"a"},custom:Boolean,exact:Boolean,exactPath:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,ariaCurrentValue:{type:String,default:"page"},event:{type:ne,default:"click"}},render:function(e){var t=this,n=this.$router,i=this.$route,s=n.resolve(this.to,i,this.append),o=s.location,a=s.route,l=s.href,c={},u=n.options.linkActiveClass,d=n.options.linkExactActiveClass,p=null==u?"router-link-active":u,h=null==d?"router-link-exact-active":d,m=null==this.activeClass?p:this.activeClass,g=null==this.exactActiveClass?h:this.exactActiveClass,v=a.redirectedFrom?f(null,Q(a.redirectedFrom),null,n):a;c[g]=b(i,v,this.exactPath),c[m]=this.exact||this.exactPath?c[g]:x(i,v);var y=c[g]?this.ariaCurrentValue:null,w=function(e){se(e)&&(t.replace?n.replace(o,re):n.push(o,re))},E={click:se};Array.isArray(this.event)?this.event.forEach(function(e){E[e]=w}):E[this.event]=w;var S={class:c},C=!this.$scopedSlots.$hasNormal&&this.$scopedSlots.default&&this.$scopedSlots.default({href:l,route:a,navigate:w,isActive:c[m],isExactActive:c[g]});if(C){if(1===C.length)return C[0];if(C.length>1||!C.length)return 0===C.length?e():e("span",{},C)}if("a"===this.tag)S.on=E,S.attrs={href:l,"aria-current":y};else{var T=oe(this.$slots.default);if(T){T.isStatic=!1;var k=T.data=r({},T.data);for(var _ in k.on=k.on||{},k.on){var O=k.on[_];_ in E&&(k.on[_]=Array.isArray(O)?O:[O])}for(var $ in E)$ in k.on?k.on[$].push(E[$]):k.on[$]=w;var R=T.data.attrs=r({},T.data.attrs);R.href=l,R["aria-current"]=y}else S.on=E}return e(this.tag,S,this.$slots.default)}};function se(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){var t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function oe(e){if(e)for(var t,n=0;n<e.length;n++){if(t=e[n],"a"===t.tag)return t;if(t.children&&(t=oe(t.children)))return t}}function ae(e){if(!ae.installed||ee!==e){ae.installed=!0,ee=e;var t=function(e){return void 0!==e},n=function(e,n){var r=e.$options._parentVnode;t(r)&&t(r=r.data)&&t(r=r.registerRouteInstance)&&r(e,n)};e.mixin({beforeCreate:function(){t(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,n(this,this)},destroyed:function(){n(this)}}),Object.defineProperty(e.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get:function(){return this._routerRoot._route}}),e.component("RouterView",C),e.component("RouterLink",ie);var r=e.config.optionMergeStrategies;r.beforeRouteEnter=r.beforeRouteLeave=r.beforeRouteUpdate=r.created}}var le="undefined"!==typeof window;function ce(e,t,n,r,i){var s=t||[],o=n||Object.create(null),a=r||Object.create(null);e.forEach(function(e){ue(s,o,a,e,i)});for(var l=0,c=s.length;l<c;l++)"*"===s[l]&&(s.push(s.splice(l,1)[0]),c--,l--);return{pathList:s,pathMap:o,nameMap:a}}function ue(e,t,n,r,i,s){var o=r.path,a=r.name;var l=r.pathToRegexpOptions||{},c=pe(o,i,l.strict);"boolean"===typeof r.caseSensitive&&(l.sensitive=r.caseSensitive);var u={path:c,regex:de(c,l),components:r.components||{default:r.component},alias:r.alias?"string"===typeof r.alias?[r.alias]:r.alias:[],instances:{},enteredCbs:{},name:a,parent:i,matchAs:s,redirect:r.redirect,beforeEnter:r.beforeEnter,meta:r.meta||{},props:null==r.props?{}:r.components?r.props:{default:r.props}};if(r.children&&r.children.forEach(function(r){var i=s?$(s+"/"+r.path):void 0;ue(e,t,n,r,u,i)}),t[u.path]||(e.push(u.path),t[u.path]=u),void 0!==r.alias)for(var d=Array.isArray(r.alias)?r.alias:[r.alias],p=0;p<d.length;++p){var h=d[p];0;var f={path:h,children:r.children};ue(e,t,n,f,i,u.path||"/")}a&&(n[a]||(n[a]=u))}function de(e,t){var n=P(e,[],t);return n}function pe(e,t,n){return n||(e=e.replace(/\/$/,"")),"/"===e[0]||null==t?e:$(t.path+"/"+e)}function he(e,t){var n=ce(e),r=n.pathList,i=n.pathMap,s=n.nameMap;function o(e){ce(e,r,i,s)}function a(e,t){var n="object"!==typeof e?s[e]:void 0;ce([t||e],r,i,s,n),n&&n.alias.length&&ce(n.alias.map(function(e){return{path:e,children:[t]}}),r,i,s,n)}function l(){return r.map(function(e){return i[e]})}function c(e,n,o){var a=Q(e,n,!1,t),l=a.name;if(l){var c=s[l];if(!c)return p(null,a);var u=c.regex.keys.filter(function(e){return!e.optional}).map(function(e){return e.name});if("object"!==typeof a.params&&(a.params={}),n&&"object"===typeof n.params)for(var d in n.params)!(d in a.params)&&u.indexOf(d)>-1&&(a.params[d]=n.params[d]);return a.path=Z(c.path,a.params,'named route "'+l+'"'),p(c,a,o)}if(a.path){a.params={};for(var h=0;h<r.length;h++){var f=r[h],m=i[f];if(fe(m.regex,a.path,a.params))return p(m,a,o)}}return p(null,a)}function u(e,n){var r=e.redirect,i="function"===typeof r?r(f(e,n,null,t)):r;if("string"===typeof i&&(i={path:i}),!i||"object"!==typeof i)return p(null,n);var o=i,a=o.name,l=o.path,u=n.query,d=n.hash,h=n.params;if(u=o.hasOwnProperty("query")?o.query:u,d=o.hasOwnProperty("hash")?o.hash:d,h=o.hasOwnProperty("params")?o.params:h,a){s[a];return c({_normalized:!0,name:a,query:u,hash:d,params:h},void 0,n)}if(l){var m=me(l,e),g=Z(m,h,'redirect route with path "'+m+'"');return c({_normalized:!0,path:g,query:u,hash:d},void 0,n)}return p(null,n)}function d(e,t,n){var r=Z(n,t.params,'aliased route with path "'+n+'"'),i=c({_normalized:!0,path:r});if(i){var s=i.matched,o=s[s.length-1];return t.params=i.params,p(o,t)}return p(null,t)}function p(e,n,r){return e&&e.redirect?u(e,r||n):e&&e.matchAs?d(e,n,e.matchAs):f(e,n,r,t)}return{match:c,addRoute:a,getRoutes:l,addRoutes:o}}function fe(e,t,n){var r=t.match(e);if(!r)return!1;if(!n)return!0;for(var i=1,s=r.length;i<s;++i){var o=e.keys[i-1];o&&(n[o.name||"pathMatch"]="string"===typeof r[i]?l(r[i]):r[i])}return!0}function me(e,t){return _(e,t.parent?t.parent.path:"/",!0)}var ge=le&&window.performance&&window.performance.now?window.performance:Date;function ve(){return ge.now().toFixed(3)}var ye=ve();function be(){return ye}function we(e){return ye=e}var xe=Object.create(null);function Ee(){"scrollRestoration"in window.history&&(window.history.scrollRestoration="manual");var e=window.location.protocol+"//"+window.location.host,t=window.location.href.replace(e,""),n=r({},window.history.state);return n.key=be(),window.history.replaceState(n,"",t),window.addEventListener("popstate",Te),function(){window.removeEventListener("popstate",Te)}}function Se(e,t,n,r){if(e.app){var i=e.options.scrollBehavior;i&&e.app.$nextTick(function(){var s=ke(),o=i.call(e,t,n,r?s:null);o&&("function"===typeof o.then?o.then(function(e){Ie(e,s)}).catch(function(e){0}):Ie(o,s))})}}function Ce(){var e=be();e&&(xe[e]={x:window.pageXOffset,y:window.pageYOffset})}function Te(e){Ce(),e.state&&e.state.key&&we(e.state.key)}function ke(){var e=be();if(e)return xe[e]}function _e(e,t){var n=document.documentElement,r=n.getBoundingClientRect(),i=e.getBoundingClientRect();return{x:i.left-r.left-t.x,y:i.top-r.top-t.y}}function Oe(e){return Pe(e.x)||Pe(e.y)}function $e(e){return{x:Pe(e.x)?e.x:window.pageXOffset,y:Pe(e.y)?e.y:window.pageYOffset}}function Re(e){return{x:Pe(e.x)?e.x:0,y:Pe(e.y)?e.y:0}}function Pe(e){return"number"===typeof e}var Ae=/^#\d/;function Ie(e,t){var n="object"===typeof e;if(n&&"string"===typeof e.selector){var r=Ae.test(e.selector)?document.getElementById(e.selector.slice(1)):document.querySelector(e.selector);if(r){var i=e.offset&&"object"===typeof e.offset?e.offset:{};i=Re(i),t=_e(r,i)}else Oe(e)&&(t=$e(e))}else n&&Oe(e)&&(t=$e(e));t&&("scrollBehavior"in document.documentElement.style?window.scrollTo({left:t.x,top:t.y,behavior:e.behavior}):window.scrollTo(t.x,t.y))}var Le=le&&function(){var e=window.navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone"))&&(window.history&&"function"===typeof window.history.pushState)}();function Me(e,t){Ce();var n=window.history;try{if(t){var i=r({},n.state);i.key=be(),n.replaceState(i,"",e)}else n.pushState({key:we(ve())},"",e)}catch(s){window.location[t?"replace":"assign"](e)}}function Ne(e){Me(e,!0)}var je={redirected:2,aborted:4,cancelled:8,duplicated:16};function De(e,t){return Ue(e,t,je.redirected,'Redirected when going from "'+e.fullPath+'" to "'+He(t)+'" via a navigation guard.')}function ze(e,t){var n=Ue(e,t,je.duplicated,'Avoided redundant navigation to current location: "'+e.fullPath+'".');return n.name="NavigationDuplicated",n}function Fe(e,t){return Ue(e,t,je.cancelled,'Navigation cancelled from "'+e.fullPath+'" to "'+t.fullPath+'" with a new navigation.')}function Be(e,t){return Ue(e,t,je.aborted,'Navigation aborted from "'+e.fullPath+'" to "'+t.fullPath+'" via a navigation guard.')}function Ue(e,t,n,r){var i=new Error(r);return i._isRouter=!0,i.from=e,i.to=t,i.type=n,i}var qe=["params","query","hash"];function He(e){if("string"===typeof e)return e;if("path"in e)return e.path;var t={};return qe.forEach(function(n){n in e&&(t[n]=e[n])}),JSON.stringify(t,null,2)}function Ge(e){return Object.prototype.toString.call(e).indexOf("Error")>-1}function Ve(e,t){return Ge(e)&&e._isRouter&&(null==t||e.type===t)}function We(e,t,n){var r=function(i){i>=e.length?n():e[i]?t(e[i],function(){r(i+1)}):r(i+1)};r(0)}function Ye(e){return function(t,n,r){var i=!1,s=0,o=null;Xe(e,function(e,t,n,a){if("function"===typeof e&&void 0===e.cid){i=!0,s++;var l,c=Qe(function(t){Ze(t)&&(t=t.default),e.resolved="function"===typeof t?t:ee.extend(t),n.components[a]=t,s--,s<=0&&r()}),u=Qe(function(e){var t="Failed to resolve async component "+a+": "+e;o||(o=Ge(e)?e:new Error(t),r(o))});try{l=e(c,u)}catch(p){u(p)}if(l)if("function"===typeof l.then)l.then(c,u);else{var d=l.component;d&&"function"===typeof d.then&&d.then(c,u)}}}),i||r()}}function Xe(e,t){return Ke(e.map(function(e){return Object.keys(e.components).map(function(n){return t(e.components[n],e.instances[n],e,n)})}))}function Ke(e){return Array.prototype.concat.apply([],e)}var Je="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag;function Ze(e){return e.__esModule||Je&&"Module"===e[Symbol.toStringTag]}function Qe(e){var t=!1;return function(){var n=[],r=arguments.length;while(r--)n[r]=arguments[r];if(!t)return t=!0,e.apply(this,n)}}var et=function(e,t){this.router=e,this.base=tt(t),this.current=g,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[],this.listeners=[]};function tt(e){if(!e)if(le){var t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^https?:\/\/[^\/]+/,"")}else e="/";return"/"!==e.charAt(0)&&(e="/"+e),e.replace(/\/$/,"")}function nt(e,t){var n,r=Math.max(e.length,t.length);for(n=0;n<r;n++)if(e[n]!==t[n])break;return{updated:t.slice(0,n),activated:t.slice(n),deactivated:e.slice(n)}}function rt(e,t,n,r){var i=Xe(e,function(e,r,i,s){var o=it(e,t);if(o)return Array.isArray(o)?o.map(function(e){return n(e,r,i,s)}):n(o,r,i,s)});return Ke(r?i.reverse():i)}function it(e,t){return"function"!==typeof e&&(e=ee.extend(e)),e.options[t]}function st(e){return rt(e,"beforeRouteLeave",at,!0)}function ot(e){return rt(e,"beforeRouteUpdate",at)}function at(e,t){if(t)return function(){return e.apply(t,arguments)}}function lt(e){return rt(e,"beforeRouteEnter",function(e,t,n,r){return ct(e,n,r)})}function ct(e,t,n){return function(r,i,s){return e(r,i,function(e){"function"===typeof e&&(t.enteredCbs[n]||(t.enteredCbs[n]=[]),t.enteredCbs[n].push(e)),s(e)})}}et.prototype.listen=function(e){this.cb=e},et.prototype.onReady=function(e,t){this.ready?e():(this.readyCbs.push(e),t&&this.readyErrorCbs.push(t))},et.prototype.onError=function(e){this.errorCbs.push(e)},et.prototype.transitionTo=function(e,t,n){var r,i=this;try{r=this.router.match(e,this.current)}catch(o){throw this.errorCbs.forEach(function(e){e(o)}),o}var s=this.current;this.confirmTransition(r,function(){i.updateRoute(r),t&&t(r),i.ensureURL(),i.router.afterHooks.forEach(function(e){e&&e(r,s)}),i.ready||(i.ready=!0,i.readyCbs.forEach(function(e){e(r)}))},function(e){n&&n(e),e&&!i.ready&&(Ve(e,je.redirected)&&s===g||(i.ready=!0,i.readyErrorCbs.forEach(function(t){t(e)})))})},et.prototype.confirmTransition=function(e,t,n){var r=this,i=this.current;this.pending=e;var s=function(e){!Ve(e)&&Ge(e)&&(r.errorCbs.length?r.errorCbs.forEach(function(t){t(e)}):console.error(e)),n&&n(e)},o=e.matched.length-1,a=i.matched.length-1;if(b(e,i)&&o===a&&e.matched[o]===i.matched[a])return this.ensureURL(),e.hash&&Se(this.router,i,e,!1),s(ze(i,e));var l=nt(this.current.matched,e.matched),c=l.updated,u=l.deactivated,d=l.activated,p=[].concat(st(u),this.router.beforeHooks,ot(c),d.map(function(e){return e.beforeEnter}),Ye(d)),h=function(t,n){if(r.pending!==e)return s(Fe(i,e));try{t(e,i,function(t){!1===t?(r.ensureURL(!0),s(Be(i,e))):Ge(t)?(r.ensureURL(!0),s(t)):"string"===typeof t||"object"===typeof t&&("string"===typeof t.path||"string"===typeof t.name)?(s(De(i,e)),"object"===typeof t&&t.replace?r.replace(t):r.push(t)):n(t)})}catch(o){s(o)}};We(p,h,function(){var n=lt(d),o=n.concat(r.router.resolveHooks);We(o,h,function(){if(r.pending!==e)return s(Fe(i,e));r.pending=null,t(e),r.router.app&&r.router.app.$nextTick(function(){S(e)})})})},et.prototype.updateRoute=function(e){this.current=e,this.cb&&this.cb(e)},et.prototype.setupListeners=function(){},et.prototype.teardown=function(){this.listeners.forEach(function(e){e()}),this.listeners=[],this.current=g,this.pending=null};var ut=function(e){function t(t,n){e.call(this,t,n),this._startLocation=dt(this.base)}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.setupListeners=function(){var e=this;if(!(this.listeners.length>0)){var t=this.router,n=t.options.scrollBehavior,r=Le&&n;r&&this.listeners.push(Ee());var i=function(){var n=e.current,i=dt(e.base);e.current===g&&i===e._startLocation||e.transitionTo(i,function(e){r&&Se(t,e,n,!0)})};window.addEventListener("popstate",i),this.listeners.push(function(){window.removeEventListener("popstate",i)})}},t.prototype.go=function(e){window.history.go(e)},t.prototype.push=function(e,t,n){var r=this,i=this,s=i.current;this.transitionTo(e,function(e){Me($(r.base+e.fullPath)),Se(r.router,e,s,!1),t&&t(e)},n)},t.prototype.replace=function(e,t,n){var r=this,i=this,s=i.current;this.transitionTo(e,function(e){Ne($(r.base+e.fullPath)),Se(r.router,e,s,!1),t&&t(e)},n)},t.prototype.ensureURL=function(e){if(dt(this.base)!==this.current.fullPath){var t=$(this.base+this.current.fullPath);e?Me(t):Ne(t)}},t.prototype.getCurrentLocation=function(){return dt(this.base)},t}(et);function dt(e){var t=window.location.pathname,n=t.toLowerCase(),r=e.toLowerCase();return!e||n!==r&&0!==n.indexOf($(r+"/"))||(t=t.slice(e.length)),(t||"/")+window.location.search+window.location.hash}var pt=function(e){function t(t,n,r){e.call(this,t,n),r&&ht(this.base)||ft()}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.setupListeners=function(){var e=this;if(!(this.listeners.length>0)){var t=this.router,n=t.options.scrollBehavior,r=Le&&n;r&&this.listeners.push(Ee());var i=function(){var t=e.current;ft()&&e.transitionTo(mt(),function(n){r&&Se(e.router,n,t,!0),Le||yt(n.fullPath)})},s=Le?"popstate":"hashchange";window.addEventListener(s,i),this.listeners.push(function(){window.removeEventListener(s,i)})}},t.prototype.push=function(e,t,n){var r=this,i=this,s=i.current;this.transitionTo(e,function(e){vt(e.fullPath),Se(r.router,e,s,!1),t&&t(e)},n)},t.prototype.replace=function(e,t,n){var r=this,i=this,s=i.current;this.transitionTo(e,function(e){yt(e.fullPath),Se(r.router,e,s,!1),t&&t(e)},n)},t.prototype.go=function(e){window.history.go(e)},t.prototype.ensureURL=function(e){var t=this.current.fullPath;mt()!==t&&(e?vt(t):yt(t))},t.prototype.getCurrentLocation=function(){return mt()},t}(et);function ht(e){var t=dt(e);if(!/^\/#/.test(t))return window.location.replace($(e+"/#"+t)),!0}function ft(){var e=mt();return"/"===e.charAt(0)||(yt("/"+e),!1)}function mt(){var e=window.location.href,t=e.indexOf("#");return t<0?"":(e=e.slice(t+1),e)}function gt(e){var t=window.location.href,n=t.indexOf("#"),r=n>=0?t.slice(0,n):t;return r+"#"+e}function vt(e){Le?Me(gt(e)):window.location.hash=e}function yt(e){Le?Ne(gt(e)):window.location.replace(gt(e))}var bt=function(e){function t(t,n){e.call(this,t,n),this.stack=[],this.index=-1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.push=function(e,t,n){var r=this;this.transitionTo(e,function(e){r.stack=r.stack.slice(0,r.index+1).concat(e),r.index++,t&&t(e)},n)},t.prototype.replace=function(e,t,n){var r=this;this.transitionTo(e,function(e){r.stack=r.stack.slice(0,r.index).concat(e),t&&t(e)},n)},t.prototype.go=function(e){var t=this,n=this.index+e;if(!(n<0||n>=this.stack.length)){var r=this.stack[n];this.confirmTransition(r,function(){var e=t.current;t.index=n,t.updateRoute(r),t.router.afterHooks.forEach(function(t){t&&t(r,e)})},function(e){Ve(e,je.duplicated)&&(t.index=n)})}},t.prototype.getCurrentLocation=function(){var e=this.stack[this.stack.length-1];return e?e.fullPath:"/"},t.prototype.ensureURL=function(){},t}(et),wt=function(e){void 0===e&&(e={}),this.app=null,this.apps=[],this.options=e,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=he(e.routes||[],this);var t=e.mode||"hash";switch(this.fallback="history"===t&&!Le&&!1!==e.fallback,this.fallback&&(t="hash"),le||(t="abstract"),this.mode=t,t){case"history":this.history=new ut(this,e.base);break;case"hash":this.history=new pt(this,e.base,this.fallback);break;case"abstract":this.history=new bt(this,e.base);break;default:0}},xt={currentRoute:{configurable:!0}};wt.prototype.match=function(e,t,n){return this.matcher.match(e,t,n)},xt.currentRoute.get=function(){return this.history&&this.history.current},wt.prototype.init=function(e){var t=this;if(this.apps.push(e),e.$once("hook:destroyed",function(){var n=t.apps.indexOf(e);n>-1&&t.apps.splice(n,1),t.app===e&&(t.app=t.apps[0]||null),t.app||t.history.teardown()}),!this.app){this.app=e;var n=this.history;if(n instanceof ut||n instanceof pt){var r=function(e){var r=n.current,i=t.options.scrollBehavior,s=Le&&i;s&&"fullPath"in e&&Se(t,e,r,!1)},i=function(e){n.setupListeners(),r(e)};n.transitionTo(n.getCurrentLocation(),i,i)}n.listen(function(e){t.apps.forEach(function(t){t._route=e})})}},wt.prototype.beforeEach=function(e){return St(this.beforeHooks,e)},wt.prototype.beforeResolve=function(e){return St(this.resolveHooks,e)},wt.prototype.afterEach=function(e){return St(this.afterHooks,e)},wt.prototype.onReady=function(e,t){this.history.onReady(e,t)},wt.prototype.onError=function(e){this.history.onError(e)},wt.prototype.push=function(e,t,n){var r=this;if(!t&&!n&&"undefined"!==typeof Promise)return new Promise(function(t,n){r.history.push(e,t,n)});this.history.push(e,t,n)},wt.prototype.replace=function(e,t,n){var r=this;if(!t&&!n&&"undefined"!==typeof Promise)return new Promise(function(t,n){r.history.replace(e,t,n)});this.history.replace(e,t,n)},wt.prototype.go=function(e){this.history.go(e)},wt.prototype.back=function(){this.go(-1)},wt.prototype.forward=function(){this.go(1)},wt.prototype.getMatchedComponents=function(e){var t=e?e.matched?e:this.resolve(e).route:this.currentRoute;return t?[].concat.apply([],t.matched.map(function(e){return Object.keys(e.components).map(function(t){return e.components[t]})})):[]},wt.prototype.resolve=function(e,t,n){t=t||this.history.current;var r=Q(e,t,n,this),i=this.match(r,t),s=i.redirectedFrom||i.fullPath,o=this.history.base,a=Ct(o,s,this.mode);return{location:r,route:i,href:a,normalizedTo:r,resolved:i}},wt.prototype.getRoutes=function(){return this.matcher.getRoutes()},wt.prototype.addRoute=function(e,t){this.matcher.addRoute(e,t),this.history.current!==g&&this.history.transitionTo(this.history.getCurrentLocation())},wt.prototype.addRoutes=function(e){this.matcher.addRoutes(e),this.history.current!==g&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(wt.prototype,xt);var Et=wt;function St(e,t){return e.push(t),function(){var n=e.indexOf(t);n>-1&&e.splice(n,1)}}function Ct(e,t,n){var r="hash"===n?"#"+t:t;return e?$(e+"/"+r):r}wt.install=ae,wt.version="3.6.5",wt.isNavigationFailure=Ve,wt.NavigationFailureType=je,wt.START_LOCATION=g,le&&window.Vue&&window.Vue.use(wt)},1635:(e,t,n)=>{"use strict";n.d(t,{Cg:()=>r});function r(e,t,n,r){var i,s=arguments.length,o=s<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(o=(s<3?i(o):s>3?i(t,n,o):i(t,n))||o);return s>3&&o&&Object.defineProperty(t,n,o),o}Object.create;Object.create;"function"===typeof SuppressedError&&SuppressedError},1656:(e,t,n)=>{"use strict";function r(e,t,n,r,i,s,o,a){var l,c="function"===typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),s&&(c._scopeId="data-v-"+s),o?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=l):i&&(l=a?function(){i.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n.d(t,{A:()=>r})},2207:(e,t,n)=>{e.exports=n(7452)},3633:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(6546);const i=(0,r.F3)("SplashScreen",{web:()=>n.e(121).then(n.bind(n,7411)).then(e=>new e.SplashScreenWeb)})},4214:(e,t,n)=>{"use strict";n.d(t,{R:()=>i});var r=n(6546);n(5992);const i=(0,r.F3)("VoiceRecorder",{web:()=>n.e(121).then(n.bind(n,9396)).then(e=>new e.VoiceRecorderWeb)})},4276:function(e,t,n){
/*!
 * vue-awesome-swiper v4.1.1
 * Copyright (c) Surmon. All rights reserved.
 * Released under the MIT License.
 * Surmon <https://github.com/surmon-china>
 */
(function(e,r){r(t,n(4654),n(5471))})(0,function(e,t,n){"use strict";var r;t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t,n=n&&Object.prototype.hasOwnProperty.call(n,"default")?n["default"]:n,function(e){e["SwiperComponent"]="Swiper",e["SwiperSlideComponent"]="SwiperSlide",e["SwiperDirective"]="swiper",e["SwiperInstance"]="$swiper"}(r||(r={}));var i,s,o=Object.freeze({containerClass:"swiper-container",wrapperClass:"swiper-wrapper",slideClass:"swiper-slide"});(function(e){e["Ready"]="ready",e["ClickSlide"]="clickSlide"})(i||(i={})),function(e){e["AutoUpdate"]="autoUpdate",e["AutoDestroy"]="autoDestroy",e["DeleteInstanceOnDestroy"]="deleteInstanceOnDestroy",e["CleanupStylesOnDestroy"]="cleanupStylesOnDestroy"}(s||(s={}));var a=["init","beforeDestroy","slideChange","slideChangeTransitionStart","slideChangeTransitionEnd","slideNextTransitionStart","slideNextTransitionEnd","slidePrevTransitionStart","slidePrevTransitionEnd","transitionStart","transitionEnd","touchStart","touchMove","touchMoveOpposite","sliderMove","touchEnd","click","tap","doubleTap","imagesReady","progress","reachBeginning","reachEnd","fromEdge","setTranslate","setTransition","resize","observerUpdate","beforeLoopFix","loopFix"];
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */function l(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var s=arguments[t],o=0,a=s.length;o<a;o++,i++)r[i]=s[o];return r}var c,u=function(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase()},d=function(e,t,n){var r,s,o;if(e&&!e.destroyed){var a=(null===(r=t.composedPath)||void 0===r?void 0:r.call(t))||t.path;if((null===t||void 0===t?void 0:t.target)&&a){var l=Array.from(e.slides),c=Array.from(a);if(l.includes(t.target)||c.some(function(e){return l.includes(e)})){var d=e.clickedIndex,p=Number(null===(o=null===(s=e.clickedSlide)||void 0===s?void 0:s.dataset)||void 0===o?void 0:o.swiperSlideIndex),h=Number.isInteger(p)?p:null;n(i.ClickSlide,d,h),n(u(i.ClickSlide),d,h)}}}},p=function(e,t){a.forEach(function(n){e.on(n,function(){for(var e=arguments,r=[],i=0;i<arguments.length;i++)r[i]=e[i];t.apply(void 0,l([n],r));var s=u(n);s!==n&&t.apply(void 0,l([s],r))})})},h="instanceName";function f(e,t){var n=function(e,t){var n,r,i,s,o=null===(r=null===(n=e.data)||void 0===n?void 0:n.attrs)||void 0===r?void 0:r[t];return void 0!==o?o:null===(s=null===(i=e.data)||void 0===i?void 0:i.attrs)||void 0===s?void 0:s[u(t)]},a=function(e,t,i){return t.arg||n(i,h)||e.id||r.SwiperInstance},l=function(e,t,n){var r=a(e,t,n);return n.context[r]||null},c=function(e){return e.value||t},f=function(e){return[!0,void 0,null,""].includes(e)},m=function(e){var t,n,r=(null===(t=e.data)||void 0===t?void 0:t.on)||(null===(n=e.componentOptions)||void 0===n?void 0:n.listeners);return function(e){for(var t,n=arguments,i=[],s=1;s<arguments.length;s++)i[s-1]=n[s];var o=null===(t=r)||void 0===t?void 0:t[e];o&&o.fns.apply(o,i)}};return{bind:function(e,t,n){-1===e.className.indexOf(o.containerClass)&&(e.className+=(e.className?" ":"")+o.containerClass),e.addEventListener("click",function(r){var i=m(n),s=l(e,t,n);d(s,r,i)})},inserted:function(t,n,r){var s=r.context,o=c(n),l=a(t,n,r),u=m(r),d=s,h=null===d||void 0===d?void 0:d[l];h&&!h.destroyed||(h=new e(t,o),d[l]=h,p(h,u),u(i.Ready,h))},componentUpdated:function(e,t,r){var i,o,a,u,d,p,h,m,g,v,y,b,w=n(r,s.AutoUpdate);if(f(w)){var x=l(e,t,r);if(x){var E=c(t),S=E.loop;S&&(null===(o=null===(i=x)||void 0===i?void 0:i.loopDestroy)||void 0===o||o.call(i)),null===(a=null===x||void 0===x?void 0:x.update)||void 0===a||a.call(x),null===(d=null===(u=x.navigation)||void 0===u?void 0:u.update)||void 0===d||d.call(u),null===(h=null===(p=x.pagination)||void 0===p?void 0:p.render)||void 0===h||h.call(p),null===(g=null===(m=x.pagination)||void 0===m?void 0:m.update)||void 0===g||g.call(m),S&&(null===(y=null===(v=x)||void 0===v?void 0:v.loopCreate)||void 0===y||y.call(v),null===(b=null===x||void 0===x?void 0:x.update)||void 0===b||b.call(x))}}},unbind:function(e,t,r){var i,o=n(r,s.AutoDestroy);if(f(o)){var a=l(e,t,r);a&&a.initialized&&(null===(i=null===a||void 0===a?void 0:a.destroy)||void 0===i||i.call(a,f(n(r,s.DeleteInstanceOnDestroy)),f(n(r,s.CleanupStylesOnDestroy))))}}}}function m(e){var t;return n.extend({name:r.SwiperComponent,props:(t={defaultOptions:{type:Object,required:!1,default:function(){return{}}},options:{type:Object,required:!1}},t[s.AutoUpdate]={type:Boolean,default:!0},t[s.AutoDestroy]={type:Boolean,default:!0},t[s.DeleteInstanceOnDestroy]={type:Boolean,required:!1,default:!0},t[s.CleanupStylesOnDestroy]={type:Boolean,required:!1,default:!0},t),data:function(){var e;return e={},e[r.SwiperInstance]=null,e},computed:{swiperInstance:{cache:!1,set:function(e){this[r.SwiperInstance]=e},get:function(){return this[r.SwiperInstance]}},swiperOptions:function(){return this.options||this.defaultOptions},wrapperClass:function(){return this.swiperOptions.wrapperClass||o.wrapperClass}},methods:{handleSwiperClick:function(e){d(this.swiperInstance,e,this.$emit.bind(this))},autoReLoopSwiper:function(){var e,t;if(this.swiperInstance&&this.swiperOptions.loop){var n=this.swiperInstance;null===(e=null===n||void 0===n?void 0:n.loopDestroy)||void 0===e||e.call(n),null===(t=null===n||void 0===n?void 0:n.loopCreate)||void 0===t||t.call(n)}},updateSwiper:function(){var e,t,n,r,i,o,a,l;this[s.AutoUpdate]&&this.swiperInstance&&(this.autoReLoopSwiper(),null===(t=null===(e=this.swiperInstance)||void 0===e?void 0:e.update)||void 0===t||t.call(e),null===(r=null===(n=this.swiperInstance.navigation)||void 0===n?void 0:n.update)||void 0===r||r.call(n),null===(o=null===(i=this.swiperInstance.pagination)||void 0===i?void 0:i.render)||void 0===o||o.call(i),null===(l=null===(a=this.swiperInstance.pagination)||void 0===a?void 0:a.update)||void 0===l||l.call(a))},destroySwiper:function(){var e,t;this[s.AutoDestroy]&&this.swiperInstance&&this.swiperInstance.initialized&&(null===(t=null===(e=this.swiperInstance)||void 0===e?void 0:e.destroy)||void 0===t||t.call(e,this[s.DeleteInstanceOnDestroy],this[s.CleanupStylesOnDestroy]))},initSwiper:function(){this.swiperInstance=new e(this.$el,this.swiperOptions),p(this.swiperInstance,this.$emit.bind(this)),this.$emit(i.Ready,this.swiperInstance)}},mounted:function(){this.swiperInstance||this.initSwiper()},activated:function(){this.updateSwiper()},updated:function(){this.updateSwiper()},beforeDestroy:function(){this.$nextTick(this.destroySwiper)},render:function(e){return e("div",{staticClass:o.containerClass,on:{click:this.handleSwiperClick}},[this.$slots[c.ParallaxBg],e("div",{class:this.wrapperClass},this.$slots.default),this.$slots[c.Pagination],this.$slots[c.PrevButton],this.$slots[c.NextButton],this.$slots[c.Scrollbar]])}})}(function(e){e["ParallaxBg"]="parallax-bg",e["Pagination"]="pagination",e["Scrollbar"]="scrollbar",e["PrevButton"]="button-prev",e["NextButton"]="button-next"})(c||(c={}));var g=n.extend({name:r.SwiperSlideComponent,computed:{slideClass:function(){var e,t;return(null===(t=null===(e=this.$parent)||void 0===e?void 0:e.swiperOptions)||void 0===t?void 0:t.slideClass)||o.slideClass}},methods:{update:function(){var e,t=this.$parent;t[s.AutoUpdate]&&(null===(e=null===t||void 0===t?void 0:t.swiperInstance)||void 0===e||e.update())}},mounted:function(){this.update()},updated:function(){this.update()},render:function(e){return e("div",{class:this.slideClass},this.$slots.default)}}),v=function(e){var t=function(n,i){if(!t.installed){var s=m(e);i&&(s.options.props.defaultOptions.default=function(){return i}),n.component(r.SwiperComponent,s),n.component(r.SwiperSlideComponent,g),n.directive(r.SwiperDirective,f(e,i)),t.installed=!0}};return t};function y(e){var t;return t={version:"4.1.1",install:v(e),directive:f(e)},t[r.SwiperComponent]=m(e),t[r.SwiperSlideComponent]=g,t}var b=y(t),w=b.version,x=b.install,E=b.directive,S=b.Swiper,C=b.SwiperSlide;e.Swiper=S,e.SwiperSlide=C,e.default=b,e.directive=E,e.install=x,e.version=w,Object.defineProperty(e,"__esModule",{value:!0})})},4335:(e,t,n)=>{"use strict";n.d(t,{A:()=>bn});var r={};function i(e,t){return function(){return e.apply(t,arguments)}}n.r(r),n.d(r,{hasBrowserEnv:()=>De,hasStandardBrowserEnv:()=>Fe,hasStandardBrowserWebWorkerEnv:()=>Be,navigator:()=>ze,origin:()=>Ue});const{toString:s}=Object.prototype,{getPrototypeOf:o}=Object,{iterator:a,toStringTag:l}=Symbol,c=(e=>t=>{const n=s.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),u=e=>(e=e.toLowerCase(),t=>c(t)===e),d=e=>t=>typeof t===e,{isArray:p}=Array,h=d("undefined");function f(e){return null!==e&&!h(e)&&null!==e.constructor&&!h(e.constructor)&&y(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const m=u("ArrayBuffer");function g(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&m(e.buffer),t}const v=d("string"),y=d("function"),b=d("number"),w=e=>null!==e&&"object"===typeof e,x=e=>!0===e||!1===e,E=e=>{if("object"!==c(e))return!1;const t=o(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(l in e)&&!(a in e)},S=e=>{if(!w(e)||f(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(t){return!1}},C=u("Date"),T=u("File"),k=u("Blob"),_=u("FileList"),O=e=>w(e)&&y(e.pipe),$=e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||y(e.append)&&("formdata"===(t=c(e))||"object"===t&&y(e.toString)&&"[object FormData]"===e.toString()))},R=u("URLSearchParams"),[P,A,I,L]=["ReadableStream","Request","Response","Headers"].map(u),M=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function N(e,t,{allOwnKeys:n=!1}={}){if(null===e||"undefined"===typeof e)return;let r,i;if("object"!==typeof e&&(e=[e]),p(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{if(f(e))return;const i=n?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;let o;for(r=0;r<s;r++)o=i[r],t.call(null,e[o],o,e)}}function j(e,t){if(f(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;while(i-- >0)if(r=n[i],t===r.toLowerCase())return r;return null}const D=(()=>"undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global)(),z=e=>!h(e)&&e!==D;function F(){const{caseless:e}=z(this)&&this||{},t={},n=(n,r)=>{const i=e&&j(t,r)||r;E(t[i])&&E(n)?t[i]=F(t[i],n):E(n)?t[i]=F({},n):p(n)?t[i]=n.slice():t[i]=n};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&N(arguments[r],n);return t}const B=(e,t,n,{allOwnKeys:r}={})=>(N(t,(t,r)=>{n&&y(t)?e[r]=i(t,n):e[r]=t},{allOwnKeys:r}),e),U=e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),q=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},H=(e,t,n,r)=>{let i,s,a;const l={};if(t=t||{},null==e)return t;do{i=Object.getOwnPropertyNames(e),s=i.length;while(s-- >0)a=i[s],r&&!r(a,e,t)||l[a]||(t[a]=e[a],l[a]=!0);e=!1!==n&&o(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},G=(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},V=e=>{if(!e)return null;if(p(e))return e;let t=e.length;if(!b(t))return null;const n=new Array(t);while(t-- >0)n[t]=e[t];return n},W=(e=>t=>e&&t instanceof e)("undefined"!==typeof Uint8Array&&o(Uint8Array)),Y=(e,t)=>{const n=e&&e[a],r=n.call(e);let i;while((i=r.next())&&!i.done){const n=i.value;t.call(e,n[0],n[1])}},X=(e,t)=>{let n;const r=[];while(null!==(n=e.exec(t)))r.push(n);return r},K=u("HTMLFormElement"),J=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),Z=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Q=u("RegExp"),ee=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};N(n,(n,i)=>{let s;!1!==(s=t(n,i,e))&&(r[i]=s||n)}),Object.defineProperties(e,r)},te=e=>{ee(e,(t,n)=>{if(y(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];y(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},ne=(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return p(e)?r(e):r(String(e).split(t)),n},re=()=>{},ie=(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t;function se(e){return!!(e&&y(e.append)&&"FormData"===e[l]&&e[a])}const oe=e=>{const t=new Array(10),n=(e,r)=>{if(w(e)){if(t.indexOf(e)>=0)return;if(f(e))return e;if(!("toJSON"in e)){t[r]=e;const i=p(e)?[]:{};return N(e,(e,t)=>{const s=n(e,r+1);!h(s)&&(i[t]=s)}),t[r]=void 0,i}}return e};return n(e,0)},ae=u("AsyncFunction"),le=e=>e&&(w(e)||y(e))&&y(e.then)&&y(e.catch),ce=((e,t)=>e?setImmediate:t?((e,t)=>(D.addEventListener("message",({source:n,data:r})=>{n===D&&r===e&&t.length&&t.shift()()},!1),n=>{t.push(n),D.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e))("function"===typeof setImmediate,y(D.postMessage)),ue="undefined"!==typeof queueMicrotask?queueMicrotask.bind(D):"undefined"!==typeof process&&process.nextTick||ce,de=e=>null!=e&&y(e[a]),pe={isArray:p,isArrayBuffer:m,isBuffer:f,isFormData:$,isArrayBufferView:g,isString:v,isNumber:b,isBoolean:x,isObject:w,isPlainObject:E,isEmptyObject:S,isReadableStream:P,isRequest:A,isResponse:I,isHeaders:L,isUndefined:h,isDate:C,isFile:T,isBlob:k,isRegExp:Q,isFunction:y,isStream:O,isURLSearchParams:R,isTypedArray:W,isFileList:_,forEach:N,merge:F,extend:B,trim:M,stripBOM:U,inherits:q,toFlatObject:H,kindOf:c,kindOfTest:u,endsWith:G,toArray:V,forEachEntry:Y,matchAll:X,isHTMLForm:K,hasOwnProperty:Z,hasOwnProp:Z,reduceDescriptors:ee,freezeMethods:te,toObjectSet:ne,toCamelCase:J,noop:re,toFiniteNumber:ie,findKey:j,global:D,isContextDefined:z,isSpecCompliantForm:se,toJSONObject:oe,isAsyncFn:ae,isThenable:le,setImmediate:ce,asap:ue,isIterable:de};function he(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}pe.inherits(he,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:pe.toJSONObject(this.config),code:this.code,status:this.status}}});const fe=he.prototype,me={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{me[e]={value:e}}),Object.defineProperties(he,me),Object.defineProperty(fe,"isAxiosError",{value:!0}),he.from=(e,t,n,r,i,s)=>{const o=Object.create(fe);return pe.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),he.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};const ge=he,ve=null;function ye(e){return pe.isPlainObject(e)||pe.isArray(e)}function be(e){return pe.endsWith(e,"[]")?e.slice(0,-2):e}function we(e,t,n){return e?e.concat(t).map(function(e,t){return e=be(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}function xe(e){return pe.isArray(e)&&!e.some(ye)}const Ee=pe.toFlatObject(pe,{},null,function(e){return/^is[A-Z]/.test(e)});function Se(e,t,n){if(!pe.isObject(e))throw new TypeError("target must be an object");t=t||new(ve||FormData),n=pe.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!pe.isUndefined(t[e])});const r=n.metaTokens,i=n.visitor||u,s=n.dots,o=n.indexes,a=n.Blob||"undefined"!==typeof Blob&&Blob,l=a&&pe.isSpecCompliantForm(t);if(!pe.isFunction(i))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(pe.isDate(e))return e.toISOString();if(pe.isBoolean(e))return e.toString();if(!l&&pe.isBlob(e))throw new ge("Blob is not supported. Use a Buffer instead.");return pe.isArrayBuffer(e)||pe.isTypedArray(e)?l&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,i){let a=e;if(e&&!i&&"object"===typeof e)if(pe.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(pe.isArray(e)&&xe(e)||(pe.isFileList(e)||pe.endsWith(n,"[]"))&&(a=pe.toArray(e)))return n=be(n),a.forEach(function(e,r){!pe.isUndefined(e)&&null!==e&&t.append(!0===o?we([n],r,s):null===o?n:n+"[]",c(e))}),!1;return!!ye(e)||(t.append(we(i,n,s),c(e)),!1)}const d=[],p=Object.assign(Ee,{defaultVisitor:u,convertValue:c,isVisitable:ye});function h(e,n){if(!pe.isUndefined(e)){if(-1!==d.indexOf(e))throw Error("Circular reference detected in "+n.join("."));d.push(e),pe.forEach(e,function(e,r){const s=!(pe.isUndefined(e)||null===e)&&i.call(t,e,pe.isString(r)?r.trim():r,n,p);!0===s&&h(e,n?n.concat(r):[r])}),d.pop()}}if(!pe.isObject(e))throw new TypeError("data must be an object");return h(e),t}const Ce=Se;function Te(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ke(e,t){this._pairs=[],e&&Ce(e,this,t)}const _e=ke.prototype;_e.append=function(e,t){this._pairs.push([e,t])},_e.toString=function(e){const t=e?function(t){return e.call(this,t,Te)}:Te;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const Oe=ke;function $e(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Re(e,t,n){if(!t)return e;const r=n&&n.encode||$e;pe.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let s;if(s=i?i(t,n):pe.isURLSearchParams(t)?t.toString():new Oe(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}class Pe{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){pe.forEach(this.handlers,function(t){null!==t&&e(t)})}}const Ae=Pe,Ie={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Le="undefined"!==typeof URLSearchParams?URLSearchParams:Oe,Me="undefined"!==typeof FormData?FormData:null,Ne="undefined"!==typeof Blob?Blob:null,je={isBrowser:!0,classes:{URLSearchParams:Le,FormData:Me,Blob:Ne},protocols:["http","https","file","blob","url","data"]},De="undefined"!==typeof window&&"undefined"!==typeof document,ze="object"===typeof navigator&&navigator||void 0,Fe=De&&(!ze||["ReactNative","NativeScript","NS"].indexOf(ze.product)<0),Be=(()=>"undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts)(),Ue=De&&window.location.href||"http://localhost",qe={...r,...je};function He(e,t){return Ce(e,new qe.classes.URLSearchParams,{visitor:function(e,t,n,r){return qe.isNode&&pe.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)},...t})}function Ge(e){return pe.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}function Ve(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}function We(e){function t(e,n,r,i){let s=e[i++];if("__proto__"===s)return!0;const o=Number.isFinite(+s),a=i>=e.length;if(s=!s&&pe.isArray(r)?r.length:s,a)return pe.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!o;r[s]&&pe.isObject(r[s])||(r[s]=[]);const l=t(e,n,r[s],i);return l&&pe.isArray(r[s])&&(r[s]=Ve(r[s])),!o}if(pe.isFormData(e)&&pe.isFunction(e.entries)){const n={};return pe.forEachEntry(e,(e,r)=>{t(Ge(e),r,n,0)}),n}return null}const Ye=We;function Xe(e,t,n){if(pe.isString(e))try{return(t||JSON.parse)(e),pe.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}const Ke={transitional:Ie,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=pe.isObject(e);i&&pe.isHTMLForm(e)&&(e=new FormData(e));const s=pe.isFormData(e);if(s)return r?JSON.stringify(Ye(e)):e;if(pe.isArrayBuffer(e)||pe.isBuffer(e)||pe.isStream(e)||pe.isFile(e)||pe.isBlob(e)||pe.isReadableStream(e))return e;if(pe.isArrayBufferView(e))return e.buffer;if(pe.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return He(e,this.formSerializer).toString();if((o=pe.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Ce(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),Xe(e)):e}],transformResponse:[function(e){const t=this.transitional||Ke.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(pe.isResponse(e)||pe.isReadableStream(e))return e;if(e&&pe.isString(e)&&(n&&!this.responseType||r)){const n=t&&t.silentJSONParsing,s=!n&&r;try{return JSON.parse(e)}catch(i){if(s){if("SyntaxError"===i.name)throw ge.from(i,ge.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:qe.classes.FormData,Blob:qe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};pe.forEach(["delete","get","head","post","put","patch"],e=>{Ke.headers[e]={}});const Je=Ke,Ze=pe.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Qe=e=>{const t={};let n,r,i;return e&&e.split("\n").forEach(function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&Ze[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},et=Symbol("internals");function tt(e){return e&&String(e).trim().toLowerCase()}function nt(e){return!1===e||null==e?e:pe.isArray(e)?e.map(nt):String(e)}function rt(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;while(r=n.exec(e))t[r[1]]=r[2];return t}const it=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function st(e,t,n,r,i){return pe.isFunction(r)?r.call(this,t,n):(i&&(t=n),pe.isString(t)?pe.isString(r)?-1!==t.indexOf(r):pe.isRegExp(r)?r.test(t):void 0:void 0)}function ot(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function at(e,t){const n=pe.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})})}class lt{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=tt(t);if(!i)throw new Error("header name must be a non-empty string");const s=pe.findKey(r,i);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=nt(e))}const s=(e,t)=>pe.forEach(e,(e,n)=>i(e,n,t));if(pe.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(pe.isString(e)&&(e=e.trim())&&!it(e))s(Qe(e),t);else if(pe.isObject(e)&&pe.isIterable(e)){let n,r,i={};for(const t of e){if(!pe.isArray(t))throw TypeError("Object iterator must return a key-value pair");i[r=t[0]]=(n=i[r])?pe.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(i,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=tt(e),e){const n=pe.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return rt(e);if(pe.isFunction(t))return t.call(this,e,n);if(pe.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=tt(e),e){const n=pe.findKey(this,e);return!(!n||void 0===this[n]||t&&!st(this,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=tt(e),e){const i=pe.findKey(n,e);!i||t&&!st(n,n[i],i,t)||(delete n[i],r=!0)}}return pe.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;while(n--){const i=t[n];e&&!st(this,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return pe.forEach(this,(r,i)=>{const s=pe.findKey(n,i);if(s)return t[s]=nt(r),void delete t[i];const o=e?ot(i):String(i).trim();o!==i&&delete t[i],t[o]=nt(r),n[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return pe.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&pe.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){const t=this[et]=this[et]={accessors:{}},n=t.accessors,r=this.prototype;function i(e){const t=tt(e);n[t]||(at(r,e),n[t]=!0)}return pe.isArray(e)?e.forEach(i):i(e),this}}lt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),pe.reduceDescriptors(lt.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),pe.freezeMethods(lt);const ct=lt;function ut(e,t){const n=this||Je,r=t||n,i=ct.from(r.headers);let s=r.data;return pe.forEach(e,function(e){s=e.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function dt(e){return!(!e||!e.__CANCEL__)}function pt(e,t,n){ge.call(this,null==e?"canceled":e,ge.ERR_CANCELED,t,n),this.name="CanceledError"}pe.inherits(pt,ge,{__CANCEL__:!0});const ht=pt;function ft(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new ge("Request failed with status code "+n.status,[ge.ERR_BAD_REQUEST,ge.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}function mt(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function gt(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,s=0,o=0;return t=void 0!==t?t:1e3,function(a){const l=Date.now(),c=r[o];i||(i=l),n[s]=a,r[s]=l;let u=o,d=0;while(u!==s)d+=n[u++],u%=e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const p=c&&l-c;return p?Math.round(1e3*d/p):void 0}}const vt=gt;function yt(e,t){let n,r,i=0,s=1e3/t;const o=(t,s=Date.now())=>{i=s,n=null,r&&(clearTimeout(r),r=null),e(...t)},a=(...e)=>{const t=Date.now(),a=t-i;a>=s?o(e,t):(n=e,r||(r=setTimeout(()=>{r=null,o(n)},s-a)))},l=()=>n&&o(n);return[a,l]}const bt=yt,wt=(e,t,n=3)=>{let r=0;const i=vt(50,250);return bt(n=>{const s=n.loaded,o=n.lengthComputable?n.total:void 0,a=s-r,l=i(a),c=s<=o;r=s;const u={loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:l||void 0,estimated:l&&o&&c?(o-s)/l:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0};e(u)},n)},xt=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Et=e=>(...t)=>pe.asap(()=>e(...t)),St=qe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,qe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(qe.origin),qe.navigator&&/(msie|trident)/i.test(qe.navigator.userAgent)):()=>!0,Ct=qe.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const o=[e+"="+encodeURIComponent(t)];pe.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),pe.isString(r)&&o.push("path="+r),pe.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Tt(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function kt(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function _t(e,t,n){let r=!Tt(t);return e&&(r||0==n)?kt(e,t):t}const Ot=e=>e instanceof ct?{...e}:e;function $t(e,t){t=t||{};const n={};function r(e,t,n,r){return pe.isPlainObject(e)&&pe.isPlainObject(t)?pe.merge.call({caseless:r},e,t):pe.isPlainObject(t)?pe.merge({},t):pe.isArray(t)?t.slice():t}function i(e,t,n,i){return pe.isUndefined(t)?pe.isUndefined(e)?void 0:r(void 0,e,n,i):r(e,t,n,i)}function s(e,t){if(!pe.isUndefined(t))return r(void 0,t)}function o(e,t){return pe.isUndefined(t)?pe.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,i,s){return s in t?r(n,i):s in e?r(void 0,n):void 0}const l={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(e,t,n)=>i(Ot(e),Ot(t),n,!0)};return pe.forEach(Object.keys({...e,...t}),function(r){const s=l[r]||i,o=s(e[r],t[r],r);pe.isUndefined(o)&&s!==a||(n[r]=o)}),n}const Rt=e=>{const t=$t({},e);let n,{data:r,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:l}=t;if(t.headers=a=ct.from(a),t.url=Re(_t(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),pe.isFormData(r))if(qe.hasStandardBrowserEnv||qe.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(qe.hasStandardBrowserEnv&&(i&&pe.isFunction(i)&&(i=i(t)),i||!1!==i&&St(t.url))){const e=s&&o&&Ct.read(o);e&&a.set(s,e)}return t},Pt="undefined"!==typeof XMLHttpRequest,At=Pt&&function(e){return new Promise(function(t,n){const r=Rt(e);let i=r.data;const s=ct.from(r.headers).normalize();let o,a,l,c,u,{responseType:d,onUploadProgress:p,onDownloadProgress:h}=r;function f(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=ct.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),i=d&&"text"!==d&&"json"!==d?m.response:m.responseText,s={data:i,status:m.status,statusText:m.statusText,headers:r,config:e,request:m};ft(function(e){t(e),f()},function(e){n(e),f()},s),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new ge("Request aborted",ge.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new ge("Network Error",ge.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||Ie;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new ge(t,i.clarifyTimeoutError?ge.ETIMEDOUT:ge.ECONNABORTED,e,m)),m=null},void 0===i&&s.setContentType(null),"setRequestHeader"in m&&pe.forEach(s.toJSON(),function(e,t){m.setRequestHeader(t,e)}),pe.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),h&&([l,u]=wt(h,!0),m.addEventListener("progress",l)),p&&m.upload&&([a,c]=wt(p),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(o=t=>{m&&(n(!t||t.type?new ht(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const v=mt(r.url);v&&-1===qe.protocols.indexOf(v)?n(new ge("Unsupported protocol "+v+":",ge.ERR_BAD_REQUEST,e)):m.send(i||null)})},It=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof ge?t:new ht(t instanceof Error?t.message:t))}};let s=t&&setTimeout(()=>{s=null,i(new ge(`timeout ${t} of ms exceeded`,ge.ETIMEDOUT))},t);const o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));const{signal:a}=r;return a.unsubscribe=()=>pe.asap(o),a}},Lt=It,Mt=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,i=0;while(i<n)r=i+t,yield e.slice(i,r),i=r},Nt=async function*(e,t){for await(const n of jt(e))yield*Mt(n,t)},jt=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Dt=(e,t,n,r)=>{const i=Nt(e,t);let s,o=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=o+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel(e){return a(e),i.return()}},{highWaterMark:2})},zt="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Ft=zt&&"function"===typeof ReadableStream,Bt=zt&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ut=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},qt=Ft&&Ut(()=>{let e=!1;const t=new Request(qe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ht=65536,Gt=Ft&&Ut(()=>pe.isReadableStream(new Response("").body)),Vt={stream:Gt&&(e=>e.body)};zt&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Vt[t]&&(Vt[t]=pe.isFunction(e[t])?e=>e[t]():(e,n)=>{throw new ge(`Response type '${t}' is not supported`,ge.ERR_NOT_SUPPORT,n)})})})(new Response);const Wt=async e=>{if(null==e)return 0;if(pe.isBlob(e))return e.size;if(pe.isSpecCompliantForm(e)){const t=new Request(qe.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return pe.isArrayBufferView(e)||pe.isArrayBuffer(e)?e.byteLength:(pe.isURLSearchParams(e)&&(e+=""),pe.isString(e)?(await Bt(e)).byteLength:void 0)},Yt=async(e,t)=>{const n=pe.toFiniteNumber(e.getContentLength());return null==n?Wt(t):n},Xt=zt&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:o,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:p}=Rt(e);c=c?(c+"").toLowerCase():"text";let h,f=Lt([i,s&&s.toAbortSignal()],o);const m=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let g;try{if(l&&qt&&"get"!==n&&"head"!==n&&0!==(g=await Yt(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(pe.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=xt(g,wt(Et(l)));r=Dt(n.body,Ht,e,t)}}pe.isString(d)||(d=d?"include":"omit");const i="credentials"in Request.prototype;h=new Request(t,{...p,signal:f,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:i?d:void 0});let s=await fetch(h,p);const o=Gt&&("stream"===c||"response"===c);if(Gt&&(a||o&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});const t=pe.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&xt(t,wt(Et(a),!0))||[];s=new Response(Dt(s.body,Ht,n,()=>{r&&r(),m&&m()}),e)}c=c||"text";let v=await Vt[pe.findKey(Vt,c)||"text"](s,e);return!o&&m&&m(),await new Promise((t,n)=>{ft(t,n,{data:v,headers:ct.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:h})})}catch(v){if(m&&m(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new ge("Network Error",ge.ERR_NETWORK,e,h),{cause:v.cause||v});throw ge.from(v,v&&v.code,e,h)}}),Kt={http:ve,xhr:At,fetch:Xt};pe.forEach(Kt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const Jt=e=>`- ${e}`,Zt=e=>pe.isFunction(e)||null===e||!1===e,Qt={getAdapter:e=>{e=pe.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!Zt(n)&&(r=Kt[(t=String(n)).toLowerCase()],void 0===r))throw new ge(`Unknown adapter '${t}'`);if(r)break;i[t||"#"+s]=r}if(!r){const e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));let n=t?e.length>1?"since :\n"+e.map(Jt).join("\n"):" "+Jt(e[0]):"as no adapter specified";throw new ge("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r},adapters:Kt};function en(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ht(null,e)}function tn(e){en(e),e.headers=ct.from(e.headers),e.data=ut.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);const t=Qt.getAdapter(e.adapter||Je.adapter);return t(e).then(function(t){return en(e),t.data=ut.call(e,e.transformResponse,t),t.headers=ct.from(t.headers),t},function(t){return dt(t)||(en(e),t&&t.response&&(t.response.data=ut.call(e,e.transformResponse,t.response),t.response.headers=ct.from(t.response.headers))),Promise.reject(t)})}const nn="1.11.0",rn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{rn[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const sn={};function on(e,t,n){if("object"!==typeof e)throw new ge("options must be an object",ge.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;while(i-- >0){const s=r[i],o=t[s];if(o){const t=e[s],n=void 0===t||o(t,s,e);if(!0!==n)throw new ge("option "+s+" must be "+n,ge.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new ge("Unknown option "+s,ge.ERR_BAD_OPTION)}}rn.transitional=function(e,t,n){function r(e,t){return"[Axios v"+nn+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,s)=>{if(!1===e)throw new ge(r(i," has been removed"+(t?" in "+t:"")),ge.ERR_DEPRECATED);return t&&!sn[i]&&(sn[i]=!0,console.warn(r(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,s)}},rn.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const an={assertOptions:on,validators:rn},ln=an.validators;class cn{constructor(e){this.defaults=e||{},this.interceptors={request:new Ae,response:new Ae}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{},t.url=e):t=e||{},t=$t(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&an.assertOptions(n,{silentJSONParsing:ln.transitional(ln.boolean),forcedJSONParsing:ln.transitional(ln.boolean),clarifyTimeoutError:ln.transitional(ln.boolean)},!1),null!=r&&(pe.isFunction(r)?t.paramsSerializer={serialize:r}:an.assertOptions(r,{encode:ln.function,serialize:ln.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),an.assertOptions(t,{baseUrl:ln.spelling("baseURL"),withXsrfToken:ln.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=i&&pe.merge(i.common,i[t.method]);i&&pe.forEach(["delete","get","head","post","put","patch","common"],e=>{delete i[e]}),t.headers=ct.concat(s,i);const o=[];let a=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,o.unshift(e.fulfilled,e.rejected))});const l=[];let c;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u,d=0;if(!a){const e=[tn.bind(this),void 0];e.unshift(...o),e.push(...l),u=e.length,c=Promise.resolve(t);while(d<u)c=c.then(e[d++],e[d++]);return c}u=o.length;let p=t;d=0;while(d<u){const e=o[d++],t=o[d++];try{p=e(p)}catch(h){t.call(this,h);break}}try{c=tn.call(this,p)}catch(h){return Promise.reject(h)}d=0,u=l.length;while(d<u)c=c.then(l[d++],l[d++]);return c}getUri(e){e=$t(this.defaults,e);const t=_t(e.baseURL,e.url,e.allowAbsoluteUrls);return Re(t,e.params,e.paramsSerializer)}}pe.forEach(["delete","get","head","options"],function(e){cn.prototype[e]=function(t,n){return this.request($t(n||{},{method:e,url:t,data:(n||{}).data}))}}),pe.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,i){return this.request($t(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}cn.prototype[e]=t(),cn.prototype[e+"Form"]=t(!0)});const un=cn;class dn{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;while(t-- >0)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,i){n.reason||(n.reason=new ht(e,r,i),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new dn(function(t){e=t});return{token:t,cancel:e}}}const pn=dn;function hn(e){return function(t){return e.apply(null,t)}}function fn(e){return pe.isObject(e)&&!0===e.isAxiosError}const mn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(mn).forEach(([e,t])=>{mn[t]=e});const gn=mn;function vn(e){const t=new un(e),n=i(un.prototype.request,t);return pe.extend(n,un.prototype,t,{allOwnKeys:!0}),pe.extend(n,t,null,{allOwnKeys:!0}),n.create=function(t){return vn($t(e,t))},n}const yn=vn(Je);yn.Axios=un,yn.CanceledError=ht,yn.CancelToken=pn,yn.isCancel=dt,yn.VERSION=nn,yn.toFormData=Ce,yn.AxiosError=ge,yn.Cancel=yn.CanceledError,yn.all=function(e){return Promise.all(e)},yn.spread=hn,yn.isAxiosError=fn,yn.mergeConfig=$t,yn.AxiosHeaders=ct,yn.formToJSON=e=>Ye(pe.isHTMLForm(e)?new FormData(e):e),yn.getAdapter=Qt.getAdapter,yn.HttpStatusCode=gn,yn.default=yn;const bn=yn},4654:(e,t,n)=>{"use strict";function r(e){return null!==e&&"object"===typeof e&&"constructor"in e&&e.constructor===Object}function i(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach(function(n){"undefined"===typeof e[n]?e[n]=t[n]:r(t[n])&&r(e[n])&&Object.keys(t[n]).length>0&&i(e[n],t[n])})}n.r(t),n.d(t,{default:()=>ln});var s="undefined"!==typeof document?document:{},o={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};i(s,o);var a="undefined"!==typeof window?window:{},l={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}}};i(a,l);class c{constructor(e){const t=this;for(let n=0;n<e.length;n+=1)t[n]=e[n];return t.length=e.length,this}}function u(e,t){const n=[];let r=0;if(e&&!t&&e instanceof c)return e;if(e)if("string"===typeof e){let i,o;const a=e.trim();if(a.indexOf("<")>=0&&a.indexOf(">")>=0){let e="div";for(0===a.indexOf("<li")&&(e="ul"),0===a.indexOf("<tr")&&(e="tbody"),0!==a.indexOf("<td")&&0!==a.indexOf("<th")||(e="tr"),0===a.indexOf("<tbody")&&(e="table"),0===a.indexOf("<option")&&(e="select"),o=s.createElement(e),o.innerHTML=a,r=0;r<o.childNodes.length;r+=1)n.push(o.childNodes[r])}else for(i=t||"#"!==e[0]||e.match(/[ .<>:~]/)?(t||s).querySelectorAll(e.trim()):[s.getElementById(e.trim().split("#")[1])],r=0;r<i.length;r+=1)i[r]&&n.push(i[r])}else if(e.nodeType||e===a||e===s)n.push(e);else if(e.length>0&&e[0].nodeType)for(r=0;r<e.length;r+=1)n.push(e[r]);return new c(n)}function d(e){const t=[];for(let n=0;n<e.length;n+=1)-1===t.indexOf(e[n])&&t.push(e[n]);return t}function p(e){if("undefined"===typeof e)return this;const t=e.split(" ");for(let n=0;n<t.length;n+=1)for(let e=0;e<this.length;e+=1)"undefined"!==typeof this[e]&&"undefined"!==typeof this[e].classList&&this[e].classList.add(t[n]);return this}function h(e){const t=e.split(" ");for(let n=0;n<t.length;n+=1)for(let e=0;e<this.length;e+=1)"undefined"!==typeof this[e]&&"undefined"!==typeof this[e].classList&&this[e].classList.remove(t[n]);return this}function f(e){return!!this[0]&&this[0].classList.contains(e)}function m(e){const t=e.split(" ");for(let n=0;n<t.length;n+=1)for(let e=0;e<this.length;e+=1)"undefined"!==typeof this[e]&&"undefined"!==typeof this[e].classList&&this[e].classList.toggle(t[n]);return this}function g(e,t){if(1===arguments.length&&"string"===typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let n=0;n<this.length;n+=1)if(2===arguments.length)this[n].setAttribute(e,t);else for(const t in e)this[n][t]=e[t],this[n].setAttribute(t,e[t]);return this}function v(e){for(let t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this}function y(e,t){let n;if("undefined"!==typeof t){for(let r=0;r<this.length;r+=1)n=this[r],n.dom7ElementDataStorage||(n.dom7ElementDataStorage={}),n.dom7ElementDataStorage[e]=t;return this}if(n=this[0],n){if(n.dom7ElementDataStorage&&e in n.dom7ElementDataStorage)return n.dom7ElementDataStorage[e];const t=n.getAttribute(`data-${e}`);return t||void 0}}function b(e){for(let t=0;t<this.length;t+=1){const n=this[t].style;n.webkitTransform=e,n.transform=e}return this}function w(e){"string"!==typeof e&&(e=`${e}ms`);for(let t=0;t<this.length;t+=1){const n=this[t].style;n.webkitTransitionDuration=e,n.transitionDuration=e}return this}function x(...e){let[t,n,r,i]=e;function s(e){const t=e.target;if(!t)return;const i=e.target.dom7EventData||[];if(i.indexOf(e)<0&&i.unshift(e),u(t).is(n))r.apply(t,i);else{const e=u(t).parents();for(let t=0;t<e.length;t+=1)u(e[t]).is(n)&&r.apply(e[t],i)}}function o(e){const t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),r.apply(this,t)}"function"===typeof e[1]&&([t,r,i]=e,n=void 0),i||(i=!1);const a=t.split(" ");let l;for(let c=0;c<this.length;c+=1){const e=this[c];if(n)for(l=0;l<a.length;l+=1){const t=a[l];e.dom7LiveListeners||(e.dom7LiveListeners={}),e.dom7LiveListeners[t]||(e.dom7LiveListeners[t]=[]),e.dom7LiveListeners[t].push({listener:r,proxyListener:s}),e.addEventListener(t,s,i)}else for(l=0;l<a.length;l+=1){const t=a[l];e.dom7Listeners||(e.dom7Listeners={}),e.dom7Listeners[t]||(e.dom7Listeners[t]=[]),e.dom7Listeners[t].push({listener:r,proxyListener:o}),e.addEventListener(t,o,i)}}return this}function E(...e){let[t,n,r,i]=e;"function"===typeof e[1]&&([t,r,i]=e,n=void 0),i||(i=!1);const s=t.split(" ");for(let o=0;o<s.length;o+=1){const e=s[o];for(let t=0;t<this.length;t+=1){const s=this[t];let o;if(!n&&s.dom7Listeners?o=s.dom7Listeners[e]:n&&s.dom7LiveListeners&&(o=s.dom7LiveListeners[e]),o&&o.length)for(let t=o.length-1;t>=0;t-=1){const n=o[t];r&&n.listener===r||r&&n.listener&&n.listener.dom7proxy&&n.listener.dom7proxy===r?(s.removeEventListener(e,n.proxyListener,i),o.splice(t,1)):r||(s.removeEventListener(e,n.proxyListener,i),o.splice(t,1))}}}return this}function S(...e){const t=e[0].split(" "),n=e[1];for(let i=0;i<t.length;i+=1){const o=t[i];for(let t=0;t<this.length;t+=1){const i=this[t];let l;try{l=new a.CustomEvent(o,{detail:n,bubbles:!0,cancelable:!0})}catch(r){l=s.createEvent("Event"),l.initEvent(o,!0,!0),l.detail=n}i.dom7EventData=e.filter((e,t)=>t>0),i.dispatchEvent(l),i.dom7EventData=[],delete i.dom7EventData}}return this}function C(e){const t=["webkitTransitionEnd","transitionend"],n=this;let r;function i(s){if(s.target===this)for(e.call(this,s),r=0;r<t.length;r+=1)n.off(t[r],i)}if(e)for(r=0;r<t.length;r+=1)n.on(t[r],i);return this}function T(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null}function k(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null}function _(){if(this.length>0){const e=this[0],t=e.getBoundingClientRect(),n=s.body,r=e.clientTop||n.clientTop||0,i=e.clientLeft||n.clientLeft||0,o=e===a?a.scrollY:e.scrollTop,l=e===a?a.scrollX:e.scrollLeft;return{top:t.top+o-r,left:t.left+l-i}}return null}function O(){return this[0]?a.getComputedStyle(this[0],null):{}}function $(e,t){let n;if(1===arguments.length){if("string"!==typeof e){for(n=0;n<this.length;n+=1)for(let t in e)this[n].style[t]=e[t];return this}if(this[0])return a.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"===typeof e){for(n=0;n<this.length;n+=1)this[n].style[e]=t;return this}return this}function R(e){if(!e)return this;for(let t=0;t<this.length;t+=1)if(!1===e.call(this[t],t,this[t]))return this;return this}function P(e){const t=[],n=this;for(let r=0;r<n.length;r+=1)e.call(n[r],r,n[r])&&t.push(n[r]);return new c(t)}function A(e){if("undefined"===typeof e)return this[0]?this[0].innerHTML:void 0;for(let t=0;t<this.length;t+=1)this[t].innerHTML=e;return this}function I(e){if("undefined"===typeof e)return this[0]?this[0].textContent.trim():null;for(let t=0;t<this.length;t+=1)this[t].textContent=e;return this}function L(e){const t=this[0];let n,r;if(!t||"undefined"===typeof e)return!1;if("string"===typeof e){if(t.matches)return t.matches(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);for(n=u(e),r=0;r<n.length;r+=1)if(n[r]===t)return!0;return!1}if(e===s)return t===s;if(e===a)return t===a;if(e.nodeType||e instanceof c){for(n=e.nodeType?[e]:e,r=0;r<n.length;r+=1)if(n[r]===t)return!0;return!1}return!1}function M(){let e,t=this[0];if(t){e=0;while(null!==(t=t.previousSibling))1===t.nodeType&&(e+=1);return e}}function N(e){if("undefined"===typeof e)return this;const t=this.length;let n;return e>t-1?new c([]):e<0?(n=t+e,new c(n<0?[]:[this[n]])):new c([this[e]])}function j(...e){let t;for(let n=0;n<e.length;n+=1){t=e[n];for(let e=0;e<this.length;e+=1)if("string"===typeof t){const n=s.createElement("div");n.innerHTML=t;while(n.firstChild)this[e].appendChild(n.firstChild)}else if(t instanceof c)for(let n=0;n<t.length;n+=1)this[e].appendChild(t[n]);else this[e].appendChild(t)}return this}function D(e){let t,n;for(t=0;t<this.length;t+=1)if("string"===typeof e){const r=s.createElement("div");for(r.innerHTML=e,n=r.childNodes.length-1;n>=0;n-=1)this[t].insertBefore(r.childNodes[n],this[t].childNodes[0])}else if(e instanceof c)for(n=0;n<e.length;n+=1)this[t].insertBefore(e[n],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this}function z(e){return this.length>0?e?this[0].nextElementSibling&&u(this[0].nextElementSibling).is(e)?new c([this[0].nextElementSibling]):new c([]):this[0].nextElementSibling?new c([this[0].nextElementSibling]):new c([]):new c([])}function F(e){const t=[];let n=this[0];if(!n)return new c([]);while(n.nextElementSibling){const r=n.nextElementSibling;e?u(r).is(e)&&t.push(r):t.push(r),n=r}return new c(t)}function B(e){if(this.length>0){const t=this[0];return e?t.previousElementSibling&&u(t.previousElementSibling).is(e)?new c([t.previousElementSibling]):new c([]):t.previousElementSibling?new c([t.previousElementSibling]):new c([])}return new c([])}function U(e){const t=[];let n=this[0];if(!n)return new c([]);while(n.previousElementSibling){const r=n.previousElementSibling;e?u(r).is(e)&&t.push(r):t.push(r),n=r}return new c(t)}function q(e){const t=[];for(let n=0;n<this.length;n+=1)null!==this[n].parentNode&&(e?u(this[n].parentNode).is(e)&&t.push(this[n].parentNode):t.push(this[n].parentNode));return u(d(t))}function H(e){const t=[];for(let n=0;n<this.length;n+=1){let r=this[n].parentNode;while(r)e?u(r).is(e)&&t.push(r):t.push(r),r=r.parentNode}return u(d(t))}function G(e){let t=this;return"undefined"===typeof e?new c([]):(t.is(e)||(t=t.parents(e).eq(0)),t)}function V(e){const t=[];for(let n=0;n<this.length;n+=1){const r=this[n].querySelectorAll(e);for(let e=0;e<r.length;e+=1)t.push(r[e])}return new c(t)}function W(e){const t=[];for(let n=0;n<this.length;n+=1){const r=this[n].childNodes;for(let n=0;n<r.length;n+=1)e?1===r[n].nodeType&&u(r[n]).is(e)&&t.push(r[n]):1===r[n].nodeType&&t.push(r[n])}return new c(d(t))}function Y(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}function X(...e){const t=this;let n,r;for(n=0;n<e.length;n+=1){const i=u(e[n]);for(r=0;r<i.length;r+=1)t[t.length]=i[r],t.length+=1}return t}u.fn=c.prototype,u.Class=c,u.Dom7=c;"resize scroll".split(" ");const K={addClass:p,removeClass:h,hasClass:f,toggleClass:m,attr:g,removeAttr:v,data:y,transform:b,transition:w,on:x,off:E,trigger:S,transitionEnd:C,outerWidth:T,outerHeight:k,offset:_,css:$,each:R,html:A,text:I,is:L,index:M,eq:N,append:j,prepend:D,next:z,nextAll:F,prev:B,prevAll:U,parent:q,parents:H,closest:G,find:V,children:W,filter:P,remove:Y,add:X,styles:O};Object.keys(K).forEach(e=>{u.fn[e]=u.fn[e]||K[e]});const J={deleteProps(e){const t=e;Object.keys(t).forEach(e=>{try{t[e]=null}catch(n){}try{delete t[e]}catch(n){}})},nextTick(e,t=0){return setTimeout(e,t)},now(){return Date.now()},getTranslate(e,t="x"){let n,r,i;const s=a.getComputedStyle(e,null);return a.WebKitCSSMatrix?(r=s.transform||s.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),i=new a.WebKitCSSMatrix("none"===r?"":r)):(i=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),n=i.toString().split(",")),"x"===t&&(r=a.WebKitCSSMatrix?i.m41:16===n.length?parseFloat(n[12]):parseFloat(n[4])),"y"===t&&(r=a.WebKitCSSMatrix?i.m42:16===n.length?parseFloat(n[13]):parseFloat(n[5])),r||0},parseUrlQuery(e){const t={};let n,r,i,s,o=e||a.location.href;if("string"===typeof o&&o.length)for(o=o.indexOf("?")>-1?o.replace(/\S*\?/,""):"",r=o.split("&").filter(e=>""!==e),s=r.length,n=0;n<s;n+=1)i=r[n].replace(/#\S+/g,"").split("="),t[decodeURIComponent(i[0])]="undefined"===typeof i[1]?void 0:decodeURIComponent(i[1])||"";return t},isObject(e){return"object"===typeof e&&null!==e&&e.constructor&&e.constructor===Object},extend(...e){const t=Object(e[0]);for(let n=1;n<e.length;n+=1){const r=e[n];if(void 0!==r&&null!==r){const e=Object.keys(Object(r));for(let n=0,i=e.length;n<i;n+=1){const i=e[n],s=Object.getOwnPropertyDescriptor(r,i);void 0!==s&&s.enumerable&&(J.isObject(t[i])&&J.isObject(r[i])?J.extend(t[i],r[i]):!J.isObject(t[i])&&J.isObject(r[i])?(t[i]={},J.extend(t[i],r[i])):t[i]=r[i])}}}return t}},Z=function(){return{touch:!!("ontouchstart"in a||a.DocumentTouch&&s instanceof a.DocumentTouch),pointerEvents:!!a.PointerEvent&&"maxTouchPoints"in a.navigator&&a.navigator.maxTouchPoints>=0,observer:function(){return"MutationObserver"in a||"WebkitMutationObserver"in a}(),passiveListener:function(){let e=!1;try{const t=Object.defineProperty({},"passive",{get(){e=!0}});a.addEventListener("testPassiveListener",null,t)}catch(t){}return e}(),gestures:function(){return"ongesturestart"in a}()}}();class Q{constructor(e={}){const t=this;t.params=e,t.eventsListeners={},t.params&&t.params.on&&Object.keys(t.params.on).forEach(e=>{t.on(e,t.params.on[e])})}on(e,t,n){const r=this;if("function"!==typeof t)return r;const i=n?"unshift":"push";return e.split(" ").forEach(e=>{r.eventsListeners[e]||(r.eventsListeners[e]=[]),r.eventsListeners[e][i](t)}),r}once(e,t,n){const r=this;if("function"!==typeof t)return r;function i(...n){r.off(e,i),i.f7proxy&&delete i.f7proxy,t.apply(r,n)}return i.f7proxy=t,r.on(e,i,n)}off(e,t){const n=this;return n.eventsListeners?(e.split(" ").forEach(e=>{"undefined"===typeof t?n.eventsListeners[e]=[]:n.eventsListeners[e]&&n.eventsListeners[e].length&&n.eventsListeners[e].forEach((r,i)=>{(r===t||r.f7proxy&&r.f7proxy===t)&&n.eventsListeners[e].splice(i,1)})}),n):n}emit(...e){const t=this;if(!t.eventsListeners)return t;let n,r,i;"string"===typeof e[0]||Array.isArray(e[0])?(n=e[0],r=e.slice(1,e.length),i=t):(n=e[0].events,r=e[0].data,i=e[0].context||t);const s=Array.isArray(n)?n:n.split(" ");return s.forEach(e=>{if(t.eventsListeners&&t.eventsListeners[e]){const n=[];t.eventsListeners[e].forEach(e=>{n.push(e)}),n.forEach(e=>{e.apply(i,r)})}}),t}useModulesParams(e){const t=this;t.modules&&Object.keys(t.modules).forEach(n=>{const r=t.modules[n];r.params&&J.extend(e,r.params)})}useModules(e={}){const t=this;t.modules&&Object.keys(t.modules).forEach(n=>{const r=t.modules[n],i=e[n]||{};r.instance&&Object.keys(r.instance).forEach(e=>{const n=r.instance[e];t[e]="function"===typeof n?n.bind(t):n}),r.on&&t.on&&Object.keys(r.on).forEach(e=>{t.on(e,r.on[e])}),r.create&&r.create.bind(t)(i)})}static set components(e){const t=this;t.use&&t.use(e)}static installModule(e,...t){const n=this;n.prototype.modules||(n.prototype.modules={});const r=e.name||`${Object.keys(n.prototype.modules).length}_${J.now()}`;return n.prototype.modules[r]=e,e.proto&&Object.keys(e.proto).forEach(t=>{n.prototype[t]=e.proto[t]}),e.static&&Object.keys(e.static).forEach(t=>{n[t]=e.static[t]}),e.install&&e.install.apply(n,t),n}static use(e,...t){const n=this;return Array.isArray(e)?(e.forEach(e=>n.installModule(e)),n):n.installModule(e,...t)}}function ee(){const e=this;let t,n;const r=e.$el;t="undefined"!==typeof e.params.width?e.params.width:r[0].clientWidth,n="undefined"!==typeof e.params.height?e.params.height:r[0].clientHeight,0===t&&e.isHorizontal()||0===n&&e.isVertical()||(t=t-parseInt(r.css("padding-left"),10)-parseInt(r.css("padding-right"),10),n=n-parseInt(r.css("padding-top"),10)-parseInt(r.css("padding-bottom"),10),J.extend(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function te(){const e=this,t=e.params,{$wrapperEl:n,size:r,rtlTranslate:i,wrongRTL:s}=e,o=e.virtual&&t.virtual.enabled,l=o?e.virtual.slides.length:e.slides.length,c=n.children(`.${e.params.slideClass}`),u=o?e.virtual.slides.length:c.length;let d=[];const p=[],h=[];function f(e){return!t.cssMode||e!==c.length-1}let m=t.slidesOffsetBefore;"function"===typeof m&&(m=t.slidesOffsetBefore.call(e));let g=t.slidesOffsetAfter;"function"===typeof g&&(g=t.slidesOffsetAfter.call(e));const v=e.snapGrid.length,y=e.snapGrid.length;let b,w,x=t.spaceBetween,E=-m,S=0,C=0;if("undefined"===typeof r)return;"string"===typeof x&&x.indexOf("%")>=0&&(x=parseFloat(x.replace("%",""))/100*r),e.virtualSize=-x,i?c.css({marginLeft:"",marginTop:""}):c.css({marginRight:"",marginBottom:""}),t.slidesPerColumn>1&&(b=Math.floor(u/t.slidesPerColumn)===u/e.params.slidesPerColumn?u:Math.ceil(u/t.slidesPerColumn)*t.slidesPerColumn,"auto"!==t.slidesPerView&&"row"===t.slidesPerColumnFill&&(b=Math.max(b,t.slidesPerView*t.slidesPerColumn)));const T=t.slidesPerColumn,k=b/T,_=Math.floor(u/t.slidesPerColumn);for(let $=0;$<u;$+=1){w=0;const n=c.eq($);if(t.slidesPerColumn>1){let r,i,s;if("row"===t.slidesPerColumnFill&&t.slidesPerGroup>1){const e=Math.floor($/(t.slidesPerGroup*t.slidesPerColumn)),o=$-t.slidesPerColumn*t.slidesPerGroup*e,a=0===e?t.slidesPerGroup:Math.min(Math.ceil((u-e*T*t.slidesPerGroup)/T),t.slidesPerGroup);s=Math.floor(o/a),i=o-s*a+e*t.slidesPerGroup,r=i+s*b/T,n.css({"-webkit-box-ordinal-group":r,"-moz-box-ordinal-group":r,"-ms-flex-order":r,"-webkit-order":r,order:r})}else"column"===t.slidesPerColumnFill?(i=Math.floor($/T),s=$-i*T,(i>_||i===_&&s===T-1)&&(s+=1,s>=T&&(s=0,i+=1))):(s=Math.floor($/k),i=$-s*k);n.css("margin-"+(e.isHorizontal()?"top":"left"),0!==s&&t.spaceBetween&&`${t.spaceBetween}px`)}if("none"!==n.css("display")){if("auto"===t.slidesPerView){const r=a.getComputedStyle(n[0],null),i=n[0].style.transform,s=n[0].style.webkitTransform;if(i&&(n[0].style.transform="none"),s&&(n[0].style.webkitTransform="none"),t.roundLengths)w=e.isHorizontal()?n.outerWidth(!0):n.outerHeight(!0);else if(e.isHorizontal()){const e=parseFloat(r.getPropertyValue("width")),t=parseFloat(r.getPropertyValue("padding-left")),n=parseFloat(r.getPropertyValue("padding-right")),i=parseFloat(r.getPropertyValue("margin-left")),s=parseFloat(r.getPropertyValue("margin-right")),o=r.getPropertyValue("box-sizing");w=o&&"border-box"===o?e+i+s:e+t+n+i+s}else{const e=parseFloat(r.getPropertyValue("height")),t=parseFloat(r.getPropertyValue("padding-top")),n=parseFloat(r.getPropertyValue("padding-bottom")),i=parseFloat(r.getPropertyValue("margin-top")),s=parseFloat(r.getPropertyValue("margin-bottom")),o=r.getPropertyValue("box-sizing");w=o&&"border-box"===o?e+i+s:e+t+n+i+s}i&&(n[0].style.transform=i),s&&(n[0].style.webkitTransform=s),t.roundLengths&&(w=Math.floor(w))}else w=(r-(t.slidesPerView-1)*x)/t.slidesPerView,t.roundLengths&&(w=Math.floor(w)),c[$]&&(e.isHorizontal()?c[$].style.width=`${w}px`:c[$].style.height=`${w}px`);c[$]&&(c[$].swiperSlideSize=w),h.push(w),t.centeredSlides?(E=E+w/2+S/2+x,0===S&&0!==$&&(E=E-r/2-x),0===$&&(E=E-r/2-x),Math.abs(E)<.001&&(E=0),t.roundLengths&&(E=Math.floor(E)),C%t.slidesPerGroup===0&&d.push(E),p.push(E)):(t.roundLengths&&(E=Math.floor(E)),(C-Math.min(e.params.slidesPerGroupSkip,C))%e.params.slidesPerGroup===0&&d.push(E),p.push(E),E=E+w+x),e.virtualSize+=w+x,S=w,C+=1}}let O;if(e.virtualSize=Math.max(e.virtualSize,r)+g,i&&s&&("slide"===t.effect||"coverflow"===t.effect)&&n.css({width:`${e.virtualSize+t.spaceBetween}px`}),t.setWrapperSize&&(e.isHorizontal()?n.css({width:`${e.virtualSize+t.spaceBetween}px`}):n.css({height:`${e.virtualSize+t.spaceBetween}px`})),t.slidesPerColumn>1&&(e.virtualSize=(w+t.spaceBetween)*b,e.virtualSize=Math.ceil(e.virtualSize/t.slidesPerColumn)-t.spaceBetween,e.isHorizontal()?n.css({width:`${e.virtualSize+t.spaceBetween}px`}):n.css({height:`${e.virtualSize+t.spaceBetween}px`}),t.centeredSlides)){O=[];for(let n=0;n<d.length;n+=1){let r=d[n];t.roundLengths&&(r=Math.floor(r)),d[n]<e.virtualSize+d[0]&&O.push(r)}d=O}if(!t.centeredSlides){O=[];for(let n=0;n<d.length;n+=1){let i=d[n];t.roundLengths&&(i=Math.floor(i)),d[n]<=e.virtualSize-r&&O.push(i)}d=O,Math.floor(e.virtualSize-r)-Math.floor(d[d.length-1])>1&&d.push(e.virtualSize-r)}if(0===d.length&&(d=[0]),0!==t.spaceBetween&&(e.isHorizontal()?i?c.filter(f).css({marginLeft:`${x}px`}):c.filter(f).css({marginRight:`${x}px`}):c.filter(f).css({marginBottom:`${x}px`})),t.centeredSlides&&t.centeredSlidesBounds){let e=0;h.forEach(n=>{e+=n+(t.spaceBetween?t.spaceBetween:0)}),e-=t.spaceBetween;const n=e-r;d=d.map(e=>e<0?-m:e>n?n+g:e)}if(t.centerInsufficientSlides){let e=0;if(h.forEach(n=>{e+=n+(t.spaceBetween?t.spaceBetween:0)}),e-=t.spaceBetween,e<r){const t=(r-e)/2;d.forEach((e,n)=>{d[n]=e-t}),p.forEach((e,n)=>{p[n]=e+t})}}J.extend(e,{slides:c,snapGrid:d,slidesGrid:p,slidesSizesGrid:h}),u!==l&&e.emit("slidesLengthChange"),d.length!==v&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),p.length!==y&&e.emit("slidesGridLengthChange"),(t.watchSlidesProgress||t.watchSlidesVisibility)&&e.updateSlidesOffset()}function ne(e){const t=this,n=[];let r,i=0;if("number"===typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed),"auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)t.visibleSlides.each((e,t)=>{n.push(t)});else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length)break;n.push(t.slides.eq(e)[0])}else n.push(t.slides.eq(t.activeIndex)[0]);for(r=0;r<n.length;r+=1)if("undefined"!==typeof n[r]){const e=n[r].offsetHeight;i=e>i?e:i}i&&t.$wrapperEl.css("height",`${i}px`)}function re(){const e=this,t=e.slides;for(let n=0;n<t.length;n+=1)t[n].swiperSlideOffset=e.isHorizontal()?t[n].offsetLeft:t[n].offsetTop}function ie(e=this&&this.translate||0){const t=this,n=t.params,{slides:r,rtlTranslate:i}=t;if(0===r.length)return;"undefined"===typeof r[0].swiperSlideOffset&&t.updateSlidesOffset();let s=-e;i&&(s=e),r.removeClass(n.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let o=0;o<r.length;o+=1){const e=r[o],a=(s+(n.centeredSlides?t.minTranslate():0)-e.swiperSlideOffset)/(e.swiperSlideSize+n.spaceBetween);if(n.watchSlidesVisibility||n.centeredSlides&&n.autoHeight){const i=-(s-e.swiperSlideOffset),a=i+t.slidesSizesGrid[o],l=i>=0&&i<t.size-1||a>1&&a<=t.size||i<=0&&a>=t.size;l&&(t.visibleSlides.push(e),t.visibleSlidesIndexes.push(o),r.eq(o).addClass(n.slideVisibleClass))}e.progress=i?-a:a}t.visibleSlides=u(t.visibleSlides)}function se(e){const t=this;if("undefined"===typeof e){const n=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*n||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:s,isEnd:o}=t;const a=s,l=o;0===r?(i=0,s=!0,o=!0):(i=(e-t.minTranslate())/r,s=i<=0,o=i>=1),J.extend(t,{progress:i,isBeginning:s,isEnd:o}),(n.watchSlidesProgress||n.watchSlidesVisibility||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),s&&!a&&t.emit("reachBeginning toEdge"),o&&!l&&t.emit("reachEnd toEdge"),(a&&!s||l&&!o)&&t.emit("fromEdge"),t.emit("progress",i)}function oe(){const e=this,{slides:t,params:n,$wrapperEl:r,activeIndex:i,realIndex:s}=e,o=e.virtual&&n.virtual.enabled;let a;t.removeClass(`${n.slideActiveClass} ${n.slideNextClass} ${n.slidePrevClass} ${n.slideDuplicateActiveClass} ${n.slideDuplicateNextClass} ${n.slideDuplicatePrevClass}`),a=o?e.$wrapperEl.find(`.${n.slideClass}[data-swiper-slide-index="${i}"]`):t.eq(i),a.addClass(n.slideActiveClass),n.loop&&(a.hasClass(n.slideDuplicateClass)?r.children(`.${n.slideClass}:not(.${n.slideDuplicateClass})[data-swiper-slide-index="${s}"]`).addClass(n.slideDuplicateActiveClass):r.children(`.${n.slideClass}.${n.slideDuplicateClass}[data-swiper-slide-index="${s}"]`).addClass(n.slideDuplicateActiveClass));let l=a.nextAll(`.${n.slideClass}`).eq(0).addClass(n.slideNextClass);n.loop&&0===l.length&&(l=t.eq(0),l.addClass(n.slideNextClass));let c=a.prevAll(`.${n.slideClass}`).eq(0).addClass(n.slidePrevClass);n.loop&&0===c.length&&(c=t.eq(-1),c.addClass(n.slidePrevClass)),n.loop&&(l.hasClass(n.slideDuplicateClass)?r.children(`.${n.slideClass}:not(.${n.slideDuplicateClass})[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(n.slideDuplicateNextClass):r.children(`.${n.slideClass}.${n.slideDuplicateClass}[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(n.slideDuplicateNextClass),c.hasClass(n.slideDuplicateClass)?r.children(`.${n.slideClass}:not(.${n.slideDuplicateClass})[data-swiper-slide-index="${c.attr("data-swiper-slide-index")}"]`).addClass(n.slideDuplicatePrevClass):r.children(`.${n.slideClass}.${n.slideDuplicateClass}[data-swiper-slide-index="${c.attr("data-swiper-slide-index")}"]`).addClass(n.slideDuplicatePrevClass))}function ae(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:r,snapGrid:i,params:s,activeIndex:o,realIndex:a,snapIndex:l}=t;let c,u=e;if("undefined"===typeof u){for(let e=0;e<r.length;e+=1)"undefined"!==typeof r[e+1]?n>=r[e]&&n<r[e+1]-(r[e+1]-r[e])/2?u=e:n>=r[e]&&n<r[e+1]&&(u=e+1):n>=r[e]&&(u=e);s.normalizeSlideIndex&&(u<0||"undefined"===typeof u)&&(u=0)}if(i.indexOf(n)>=0)c=i.indexOf(n);else{const e=Math.min(s.slidesPerGroupSkip,u);c=e+Math.floor((u-e)/s.slidesPerGroup)}if(c>=i.length&&(c=i.length-1),u===o)return void(c!==l&&(t.snapIndex=c,t.emit("snapIndexChange")));const d=parseInt(t.slides.eq(u).attr("data-swiper-slide-index")||u,10);J.extend(t,{snapIndex:c,realIndex:d,previousIndex:o,activeIndex:u}),t.emit("activeIndexChange"),t.emit("snapIndexChange"),a!==d&&t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&t.emit("slideChange")}function le(e){const t=this,n=t.params,r=u(e.target).closest(`.${n.slideClass}`)[0];let i=!1;if(r)for(let s=0;s<t.slides.length;s+=1)t.slides[s]===r&&(i=!0);if(!r||!i)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(u(r).attr("data-swiper-slide-index"),10):t.clickedIndex=u(r).index(),n.slideToClickedSlide&&void 0!==t.clickedIndex&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var ce={updateSize:ee,updateSlides:te,updateAutoHeight:ne,updateSlidesOffset:re,updateSlidesProgress:ie,updateProgress:se,updateSlidesClasses:oe,updateActiveIndex:ae,updateClickedSlide:le};function ue(e=(this.isHorizontal()?"x":"y")){const t=this,{params:n,rtlTranslate:r,translate:i,$wrapperEl:s}=t;if(n.virtualTranslate)return r?-i:i;if(n.cssMode)return i;let o=J.getTranslate(s[0],e);return r&&(o=-o),o||0}function de(e,t){const n=this,{rtlTranslate:r,params:i,$wrapperEl:s,wrapperEl:o,progress:a}=n;let l=0,c=0;const u=0;let d;n.isHorizontal()?l=r?-e:e:c=e,i.roundLengths&&(l=Math.floor(l),c=Math.floor(c)),i.cssMode?o[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-l:-c:i.virtualTranslate||s.transform(`translate3d(${l}px, ${c}px, ${u}px)`),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?l:c;const p=n.maxTranslate()-n.minTranslate();d=0===p?0:(e-n.minTranslate())/p,d!==a&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function pe(){return-this.snapGrid[0]}function he(){return-this.snapGrid[this.snapGrid.length-1]}function fe(e=0,t=this.params.speed,n=!0,r=!0,i){const s=this,{params:o,wrapperEl:a}=s;if(s.animating&&o.preventInteractionOnTransition)return!1;const l=s.minTranslate(),c=s.maxTranslate();let u;if(u=r&&e>l?l:r&&e<c?c:e,s.updateProgress(u),o.cssMode){const e=s.isHorizontal();return 0===t?a[e?"scrollLeft":"scrollTop"]=-u:a.scrollTo?a.scrollTo({[e?"left":"top"]:-u,behavior:"smooth"}):a[e?"scrollLeft":"scrollTop"]=-u,!0}return 0===t?(s.setTransition(0),s.setTranslate(u),n&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionEnd"))):(s.setTransition(t),s.setTranslate(u),n&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(e){s&&!s.destroyed&&e.target===this&&(s.$wrapperEl[0].removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.$wrapperEl[0].removeEventListener("webkitTransitionEnd",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,n&&s.emit("transitionEnd"))}),s.$wrapperEl[0].addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.$wrapperEl[0].addEventListener("webkitTransitionEnd",s.onTranslateToWrapperTransitionEnd))),!0}var me={getTranslate:ue,setTranslate:de,minTranslate:pe,maxTranslate:he,translateTo:fe};function ge(e,t){const n=this;n.params.cssMode||n.$wrapperEl.transition(e),n.emit("setTransition",e,t)}function ve(e=!0,t){const n=this,{activeIndex:r,params:i,previousIndex:s}=n;if(i.cssMode)return;i.autoHeight&&n.updateAutoHeight();let o=t;if(o||(o=r>s?"next":r<s?"prev":"reset"),n.emit("transitionStart"),e&&r!==s){if("reset"===o)return void n.emit("slideResetTransitionStart");n.emit("slideChangeTransitionStart"),"next"===o?n.emit("slideNextTransitionStart"):n.emit("slidePrevTransitionStart")}}function ye(e=!0,t){const n=this,{activeIndex:r,previousIndex:i,params:s}=n;if(n.animating=!1,s.cssMode)return;n.setTransition(0);let o=t;if(o||(o=r>i?"next":r<i?"prev":"reset"),n.emit("transitionEnd"),e&&r!==i){if("reset"===o)return void n.emit("slideResetTransitionEnd");n.emit("slideChangeTransitionEnd"),"next"===o?n.emit("slideNextTransitionEnd"):n.emit("slidePrevTransitionEnd")}}var be={setTransition:ge,transitionStart:ve,transitionEnd:ye};function we(e=0,t=this.params.speed,n=!0,r){const i=this;let s=e;s<0&&(s=0);const{params:o,snapGrid:a,slidesGrid:l,previousIndex:c,activeIndex:u,rtlTranslate:d,wrapperEl:p}=i;if(i.animating&&o.preventInteractionOnTransition)return!1;const h=Math.min(i.params.slidesPerGroupSkip,s);let f=h+Math.floor((s-h)/i.params.slidesPerGroup);f>=a.length&&(f=a.length-1),(u||o.initialSlide||0)===(c||0)&&n&&i.emit("beforeSlideChangeStart");const m=-a[f];if(i.updateProgress(m),o.normalizeSlideIndex)for(let v=0;v<l.length;v+=1)-Math.floor(100*m)>=Math.floor(100*l[v])&&(s=v);if(i.initialized&&s!==u){if(!i.allowSlideNext&&m<i.translate&&m<i.minTranslate())return!1;if(!i.allowSlidePrev&&m>i.translate&&m>i.maxTranslate()&&(u||0)!==s)return!1}let g;if(g=s>u?"next":s<u?"prev":"reset",d&&-m===i.translate||!d&&m===i.translate)return i.updateActiveIndex(s),o.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),"slide"!==o.effect&&i.setTranslate(m),"reset"!==g&&(i.transitionStart(n,g),i.transitionEnd(n,g)),!1;if(o.cssMode){const e=i.isHorizontal();let n=-m;return d&&(n=p.scrollWidth-p.offsetWidth-n),0===t?p[e?"scrollLeft":"scrollTop"]=n:p.scrollTo?p.scrollTo({[e?"left":"top"]:n,behavior:"smooth"}):p[e?"scrollLeft":"scrollTop"]=n,!0}return 0===t?(i.setTransition(0),i.setTranslate(m),i.updateActiveIndex(s),i.updateSlidesClasses(),i.emit("beforeTransitionStart",t,r),i.transitionStart(n,g),i.transitionEnd(n,g)):(i.setTransition(t),i.setTranslate(m),i.updateActiveIndex(s),i.updateSlidesClasses(),i.emit("beforeTransitionStart",t,r),i.transitionStart(n,g),i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(e){i&&!i.destroyed&&e.target===this&&(i.$wrapperEl[0].removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.$wrapperEl[0].removeEventListener("webkitTransitionEnd",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(n,g))}),i.$wrapperEl[0].addEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.$wrapperEl[0].addEventListener("webkitTransitionEnd",i.onSlideToWrapperTransitionEnd))),!0}function xe(e=0,t=this.params.speed,n=!0,r){const i=this;let s=e;return i.params.loop&&(s+=i.loopedSlides),i.slideTo(s,t,n,r)}function Ee(e=this.params.speed,t=!0,n){const r=this,{params:i,animating:s}=r,o=r.activeIndex<i.slidesPerGroupSkip?1:i.slidesPerGroup;if(i.loop){if(s)return!1;r.loopFix(),r._clientLeft=r.$wrapperEl[0].clientLeft}return r.slideTo(r.activeIndex+o,e,t,n)}function Se(e=this.params.speed,t=!0,n){const r=this,{params:i,animating:s,snapGrid:o,slidesGrid:a,rtlTranslate:l}=r;if(i.loop){if(s)return!1;r.loopFix(),r._clientLeft=r.$wrapperEl[0].clientLeft}const c=l?r.translate:-r.translate;function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const d=u(c),p=o.map(e=>u(e));a.map(e=>u(e)),o[p.indexOf(d)];let h,f=o[p.indexOf(d)-1];return"undefined"===typeof f&&i.cssMode&&o.forEach(e=>{!f&&d>=e&&(f=e)}),"undefined"!==typeof f&&(h=a.indexOf(f),h<0&&(h=r.activeIndex-1)),r.slideTo(h,e,t,n)}function Ce(e=this.params.speed,t=!0,n){const r=this;return r.slideTo(r.activeIndex,e,t,n)}function Te(e=this.params.speed,t=!0,n,r=.5){const i=this;let s=i.activeIndex;const o=Math.min(i.params.slidesPerGroupSkip,s),a=o+Math.floor((s-o)/i.params.slidesPerGroup),l=i.rtlTranslate?i.translate:-i.translate;if(l>=i.snapGrid[a]){const e=i.snapGrid[a],t=i.snapGrid[a+1];l-e>(t-e)*r&&(s+=i.params.slidesPerGroup)}else{const e=i.snapGrid[a-1],t=i.snapGrid[a];l-e<=(t-e)*r&&(s-=i.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,i.slidesGrid.length-1),i.slideTo(s,e,t,n)}function ke(){const e=this,{params:t,$wrapperEl:n}=e,r="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,s=e.clickedIndex;if(t.loop){if(e.animating)return;i=parseInt(u(e.clickedSlide).attr("data-swiper-slide-index"),10),t.centeredSlides?s<e.loopedSlides-r/2||s>e.slides.length-e.loopedSlides+r/2?(e.loopFix(),s=n.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),J.nextTick(()=>{e.slideTo(s)})):e.slideTo(s):s>e.slides.length-r?(e.loopFix(),s=n.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),J.nextTick(()=>{e.slideTo(s)})):e.slideTo(s)}else e.slideTo(s)}var _e={slideTo:we,slideToLoop:xe,slideNext:Ee,slidePrev:Se,slideReset:Ce,slideToClosest:Te,slideToClickedSlide:ke};function Oe(){const e=this,{params:t,$wrapperEl:n}=e;n.children(`.${t.slideClass}.${t.slideDuplicateClass}`).remove();let r=n.children(`.${t.slideClass}`);if(t.loopFillGroupWithBlank){const e=t.slidesPerGroup-r.length%t.slidesPerGroup;if(e!==t.slidesPerGroup){for(let r=0;r<e;r+=1){const e=u(s.createElement("div")).addClass(`${t.slideClass} ${t.slideBlankClass}`);n.append(e)}r=n.children(`.${t.slideClass}`)}}"auto"!==t.slidesPerView||t.loopedSlides||(t.loopedSlides=r.length),e.loopedSlides=Math.ceil(parseFloat(t.loopedSlides||t.slidesPerView,10)),e.loopedSlides+=t.loopAdditionalSlides,e.loopedSlides>r.length&&(e.loopedSlides=r.length);const i=[],o=[];r.each((t,n)=>{const s=u(n);t<e.loopedSlides&&o.push(n),t<r.length&&t>=r.length-e.loopedSlides&&i.push(n),s.attr("data-swiper-slide-index",t)});for(let s=0;s<o.length;s+=1)n.append(u(o[s].cloneNode(!0)).addClass(t.slideDuplicateClass));for(let s=i.length-1;s>=0;s-=1)n.prepend(u(i[s].cloneNode(!0)).addClass(t.slideDuplicateClass))}function $e(){const e=this;e.emit("beforeLoopFix");const{activeIndex:t,slides:n,loopedSlides:r,allowSlidePrev:i,allowSlideNext:s,snapGrid:o,rtlTranslate:a}=e;let l;e.allowSlidePrev=!0,e.allowSlideNext=!0;const c=-o[t],u=c-e.getTranslate();if(t<r){l=n.length-3*r+t,l+=r;const i=e.slideTo(l,0,!1,!0);i&&0!==u&&e.setTranslate((a?-e.translate:e.translate)-u)}else if(t>=n.length-r){l=-n.length+t+r,l+=r;const i=e.slideTo(l,0,!1,!0);i&&0!==u&&e.setTranslate((a?-e.translate:e.translate)-u)}e.allowSlidePrev=i,e.allowSlideNext=s,e.emit("loopFix")}function Re(){const e=this,{$wrapperEl:t,params:n,slides:r}=e;t.children(`.${n.slideClass}.${n.slideDuplicateClass},.${n.slideClass}.${n.slideBlankClass}`).remove(),r.removeAttr("data-swiper-slide-index")}var Pe={loopCreate:Oe,loopFix:$e,loopDestroy:Re};function Ae(e){const t=this;if(Z.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.el;n.style.cursor="move",n.style.cursor=e?"-webkit-grabbing":"-webkit-grab",n.style.cursor=e?"-moz-grabbin":"-moz-grab",n.style.cursor=e?"grabbing":"grab"}function Ie(){const e=this;Z.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.el.style.cursor="")}var Le={setGrabCursor:Ae,unsetGrabCursor:Ie};function Me(e){const t=this,{$wrapperEl:n,params:r}=t;if(r.loop&&t.loopDestroy(),"object"===typeof e&&"length"in e)for(let i=0;i<e.length;i+=1)e[i]&&n.append(e[i]);else n.append(e);r.loop&&t.loopCreate(),r.observer&&Z.observer||t.update()}function Ne(e){const t=this,{params:n,$wrapperEl:r,activeIndex:i}=t;n.loop&&t.loopDestroy();let s=i+1;if("object"===typeof e&&"length"in e){for(let t=0;t<e.length;t+=1)e[t]&&r.prepend(e[t]);s=i+e.length}else r.prepend(e);n.loop&&t.loopCreate(),n.observer&&Z.observer||t.update(),t.slideTo(s,0,!1)}function je(e,t){const n=this,{$wrapperEl:r,params:i,activeIndex:s}=n;let o=s;i.loop&&(o-=n.loopedSlides,n.loopDestroy(),n.slides=r.children(`.${i.slideClass}`));const a=n.slides.length;if(e<=0)return void n.prependSlide(t);if(e>=a)return void n.appendSlide(t);let l=o>e?o+1:o;const c=[];for(let u=a-1;u>=e;u-=1){const e=n.slides.eq(u);e.remove(),c.unshift(e)}if("object"===typeof t&&"length"in t){for(let e=0;e<t.length;e+=1)t[e]&&r.append(t[e]);l=o>e?o+t.length:o}else r.append(t);for(let u=0;u<c.length;u+=1)r.append(c[u]);i.loop&&n.loopCreate(),i.observer&&Z.observer||n.update(),i.loop?n.slideTo(l+n.loopedSlides,0,!1):n.slideTo(l,0,!1)}function De(e){const t=this,{params:n,$wrapperEl:r,activeIndex:i}=t;let s=i;n.loop&&(s-=t.loopedSlides,t.loopDestroy(),t.slides=r.children(`.${n.slideClass}`));let o,a=s;if("object"===typeof e&&"length"in e){for(let n=0;n<e.length;n+=1)o=e[n],t.slides[o]&&t.slides.eq(o).remove(),o<a&&(a-=1);a=Math.max(a,0)}else o=e,t.slides[o]&&t.slides.eq(o).remove(),o<a&&(a-=1),a=Math.max(a,0);n.loop&&t.loopCreate(),n.observer&&Z.observer||t.update(),n.loop?t.slideTo(a+t.loopedSlides,0,!1):t.slideTo(a,0,!1)}function ze(){const e=this,t=[];for(let n=0;n<e.slides.length;n+=1)t.push(n);e.removeSlide(t)}var Fe={appendSlide:Me,prependSlide:Ne,addSlide:je,removeSlide:De,removeAllSlides:ze};const Be=function(){const e=a.navigator.platform,t=a.navigator.userAgent,n={ios:!1,android:!1,androidChrome:!1,desktop:!1,iphone:!1,ipod:!1,ipad:!1,edge:!1,ie:!1,firefox:!1,macos:!1,windows:!1,cordova:!(!a.cordova&&!a.phonegap),phonegap:!(!a.cordova&&!a.phonegap),electron:!1},r=a.screen.width,i=a.screen.height,s=t.match(/(Android);?[\s\/]+([\d.]+)?/);let o=t.match(/(iPad).*OS\s([\d_]+)/);const l=t.match(/(iPod)(.*OS\s([\d_]+))?/),c=!o&&t.match(/(iPhone\sOS|iOS)\s([\d_]+)/),u=t.indexOf("MSIE ")>=0||t.indexOf("Trident/")>=0,d=t.indexOf("Edge/")>=0,p=t.indexOf("Gecko/")>=0&&t.indexOf("Firefox/")>=0,h="Win32"===e,f=t.toLowerCase().indexOf("electron")>=0;let m="MacIntel"===e;return!o&&m&&Z.touch&&(1024===r&&1366===i||834===r&&1194===i||834===r&&1112===i||768===r&&1024===i)&&(o=t.match(/(Version)\/([\d.]+)/),m=!1),n.ie=u,n.edge=d,n.firefox=p,s&&!h&&(n.os="android",n.osVersion=s[2],n.android=!0,n.androidChrome=t.toLowerCase().indexOf("chrome")>=0),(o||c||l)&&(n.os="ios",n.ios=!0),c&&!l&&(n.osVersion=c[2].replace(/_/g,"."),n.iphone=!0),o&&(n.osVersion=o[2].replace(/_/g,"."),n.ipad=!0),l&&(n.osVersion=l[3]?l[3].replace(/_/g,"."):null,n.ipod=!0),n.ios&&n.osVersion&&t.indexOf("Version/")>=0&&"10"===n.osVersion.split(".")[0]&&(n.osVersion=t.toLowerCase().split("version/")[1].split(" ")[0]),n.webView=!(!(c||o||l)||!t.match(/.*AppleWebKit(?!.*Safari)/i)&&!a.navigator.standalone)||a.matchMedia&&a.matchMedia("(display-mode: standalone)").matches,n.webview=n.webView,n.standalone=n.webView,n.desktop=!(n.ios||n.android)||f,n.desktop&&(n.electron=f,n.macos=m,n.windows=h,n.macos&&(n.os="macos"),n.windows&&(n.os="windows")),n.pixelRatio=a.devicePixelRatio||1,n}();function Ue(e){const t=this,n=t.touchEventsData,{params:r,touches:i}=t;if(t.animating&&r.preventInteractionOnTransition)return;let o=e;o.originalEvent&&(o=o.originalEvent);const l=u(o.target);if("wrapper"===r.touchEventsTarget&&!l.closest(t.wrapperEl).length)return;if(n.isTouchEvent="touchstart"===o.type,!n.isTouchEvent&&"which"in o&&3===o.which)return;if(!n.isTouchEvent&&"button"in o&&o.button>0)return;if(n.isTouched&&n.isMoved)return;if(r.noSwiping&&l.closest(r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`)[0])return void(t.allowClick=!0);if(r.swipeHandler&&!l.closest(r.swipeHandler)[0])return;i.currentX="touchstart"===o.type?o.targetTouches[0].pageX:o.pageX,i.currentY="touchstart"===o.type?o.targetTouches[0].pageY:o.pageY;const c=i.currentX,d=i.currentY,p=r.edgeSwipeDetection||r.iOSEdgeSwipeDetection,h=r.edgeSwipeThreshold||r.iOSEdgeSwipeThreshold;if(!p||!(c<=h||c>=a.screen.width-h)){if(J.extend(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),i.startX=c,i.startY=d,n.touchStartTime=J.now(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1),"touchstart"!==o.type){let e=!0;l.is(n.formElements)&&(e=!1),s.activeElement&&u(s.activeElement).is(n.formElements)&&s.activeElement!==l[0]&&s.activeElement.blur();const i=e&&t.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||i)&&o.preventDefault()}t.emit("touchStart",o)}}function qe(e){const t=this,n=t.touchEventsData,{params:r,touches:i,rtlTranslate:o}=t;let a=e;if(a.originalEvent&&(a=a.originalEvent),!n.isTouched)return void(n.startMoving&&n.isScrolling&&t.emit("touchMoveOpposite",a));if(n.isTouchEvent&&"touchmove"!==a.type)return;const l="touchmove"===a.type&&a.targetTouches&&(a.targetTouches[0]||a.changedTouches[0]),c="touchmove"===a.type?l.pageX:a.pageX,d="touchmove"===a.type?l.pageY:a.pageY;if(a.preventedByNestedSwiper)return i.startX=c,void(i.startY=d);if(!t.allowTouchMove)return t.allowClick=!1,void(n.isTouched&&(J.extend(i,{startX:c,startY:d,currentX:c,currentY:d}),n.touchStartTime=J.now()));if(n.isTouchEvent&&r.touchReleaseOnEdges&&!r.loop)if(t.isVertical()){if(d<i.startY&&t.translate<=t.maxTranslate()||d>i.startY&&t.translate>=t.minTranslate())return n.isTouched=!1,void(n.isMoved=!1)}else if(c<i.startX&&t.translate<=t.maxTranslate()||c>i.startX&&t.translate>=t.minTranslate())return;if(n.isTouchEvent&&s.activeElement&&a.target===s.activeElement&&u(a.target).is(n.formElements))return n.isMoved=!0,void(t.allowClick=!1);if(n.allowTouchCallbacks&&t.emit("touchMove",a),a.targetTouches&&a.targetTouches.length>1)return;i.currentX=c,i.currentY=d;const p=i.currentX-i.startX,h=i.currentY-i.startY;if(t.params.threshold&&Math.sqrt(p**2+h**2)<t.params.threshold)return;if("undefined"===typeof n.isScrolling){let e;t.isHorizontal()&&i.currentY===i.startY||t.isVertical()&&i.currentX===i.startX?n.isScrolling=!1:p*p+h*h>=25&&(e=180*Math.atan2(Math.abs(h),Math.abs(p))/Math.PI,n.isScrolling=t.isHorizontal()?e>r.touchAngle:90-e>r.touchAngle)}if(n.isScrolling&&t.emit("touchMoveOpposite",a),"undefined"===typeof n.startMoving&&(i.currentX===i.startX&&i.currentY===i.startY||(n.startMoving=!0)),n.isScrolling)return void(n.isTouched=!1);if(!n.startMoving)return;t.allowClick=!1,!r.cssMode&&a.cancelable&&a.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&a.stopPropagation(),n.isMoved||(r.loop&&t.loopFix(),n.startTranslate=t.getTranslate(),t.setTransition(0),t.animating&&t.$wrapperEl.trigger("webkitTransitionEnd transitionend"),n.allowMomentumBounce=!1,!r.grabCursor||!0!==t.allowSlideNext&&!0!==t.allowSlidePrev||t.setGrabCursor(!0),t.emit("sliderFirstMove",a)),t.emit("sliderMove",a),n.isMoved=!0;let f=t.isHorizontal()?p:h;i.diff=f,f*=r.touchRatio,o&&(f=-f),t.swipeDirection=f>0?"prev":"next",n.currentTranslate=f+n.startTranslate;let m=!0,g=r.resistanceRatio;if(r.touchReleaseOnEdges&&(g=0),f>0&&n.currentTranslate>t.minTranslate()?(m=!1,r.resistance&&(n.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+n.startTranslate+f)**g)):f<0&&n.currentTranslate<t.maxTranslate()&&(m=!1,r.resistance&&(n.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-n.startTranslate-f)**g)),m&&(a.preventedByNestedSwiper=!0),!t.allowSlideNext&&"next"===t.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!t.allowSlidePrev&&"prev"===t.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),r.threshold>0){if(!(Math.abs(f)>r.threshold||n.allowThresholdMove))return void(n.currentTranslate=n.startTranslate);if(!n.allowThresholdMove)return n.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,n.currentTranslate=n.startTranslate,void(i.diff=t.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY)}r.followFinger&&!r.cssMode&&((r.freeMode||r.watchSlidesProgress||r.watchSlidesVisibility)&&(t.updateActiveIndex(),t.updateSlidesClasses()),r.freeMode&&(0===n.velocities.length&&n.velocities.push({position:i[t.isHorizontal()?"startX":"startY"],time:n.touchStartTime}),n.velocities.push({position:i[t.isHorizontal()?"currentX":"currentY"],time:J.now()})),t.updateProgress(n.currentTranslate),t.setTranslate(n.currentTranslate))}function He(e){const t=this,n=t.touchEventsData,{params:r,touches:i,rtlTranslate:s,$wrapperEl:o,slidesGrid:a,snapGrid:l}=t;let c=e;if(c.originalEvent&&(c=c.originalEvent),n.allowTouchCallbacks&&t.emit("touchEnd",c),n.allowTouchCallbacks=!1,!n.isTouched)return n.isMoved&&r.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,void(n.startMoving=!1);r.grabCursor&&n.isMoved&&n.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const u=J.now(),d=u-n.touchStartTime;if(t.allowClick&&(t.updateClickedSlide(c),t.emit("tap click",c),d<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",c)),n.lastClickTime=J.now(),J.nextTick(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||0===i.diff||n.currentTranslate===n.startTranslate)return n.isTouched=!1,n.isMoved=!1,void(n.startMoving=!1);let p;if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,p=r.followFinger?s?t.translate:-t.translate:-n.currentTranslate,r.cssMode)return;if(r.freeMode){if(p<-t.minTranslate())return void t.slideTo(t.activeIndex);if(p>-t.maxTranslate())return void(t.slides.length<l.length?t.slideTo(l.length-1):t.slideTo(t.slides.length-1));if(r.freeModeMomentum){if(n.velocities.length>1){const e=n.velocities.pop(),i=n.velocities.pop(),s=e.position-i.position,o=e.time-i.time;t.velocity=s/o,t.velocity/=2,Math.abs(t.velocity)<r.freeModeMinimumVelocity&&(t.velocity=0),(o>150||J.now()-e.time>300)&&(t.velocity=0)}else t.velocity=0;t.velocity*=r.freeModeMomentumVelocityRatio,n.velocities.length=0;let e=1e3*r.freeModeMomentumRatio;const i=t.velocity*e;let a=t.translate+i;s&&(a=-a);let c,u=!1;const d=20*Math.abs(t.velocity)*r.freeModeMomentumBounceRatio;let p;if(a<t.maxTranslate())r.freeModeMomentumBounce?(a+t.maxTranslate()<-d&&(a=t.maxTranslate()-d),c=t.maxTranslate(),u=!0,n.allowMomentumBounce=!0):a=t.maxTranslate(),r.loop&&r.centeredSlides&&(p=!0);else if(a>t.minTranslate())r.freeModeMomentumBounce?(a-t.minTranslate()>d&&(a=t.minTranslate()+d),c=t.minTranslate(),u=!0,n.allowMomentumBounce=!0):a=t.minTranslate(),r.loop&&r.centeredSlides&&(p=!0);else if(r.freeModeSticky){let e;for(let t=0;t<l.length;t+=1)if(l[t]>-a){e=t;break}a=Math.abs(l[e]-a)<Math.abs(l[e-1]-a)||"next"===t.swipeDirection?l[e]:l[e-1],a=-a}if(p&&t.once("transitionEnd",()=>{t.loopFix()}),0!==t.velocity){if(e=s?Math.abs((-a-t.translate)/t.velocity):Math.abs((a-t.translate)/t.velocity),r.freeModeSticky){const n=Math.abs((s?-a:a)-t.translate),i=t.slidesSizesGrid[t.activeIndex];e=n<i?r.speed:n<2*i?1.5*r.speed:2.5*r.speed}}else if(r.freeModeSticky)return void t.slideToClosest();r.freeModeMomentumBounce&&u?(t.updateProgress(c),t.setTransition(e),t.setTranslate(a),t.transitionStart(!0,t.swipeDirection),t.animating=!0,o.transitionEnd(()=>{t&&!t.destroyed&&n.allowMomentumBounce&&(t.emit("momentumBounce"),t.setTransition(r.speed),setTimeout(()=>{t.setTranslate(c),o.transitionEnd(()=>{t&&!t.destroyed&&t.transitionEnd()})},0))})):t.velocity?(t.updateProgress(a),t.setTransition(e),t.setTranslate(a),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,o.transitionEnd(()=>{t&&!t.destroyed&&t.transitionEnd()}))):t.updateProgress(a),t.updateActiveIndex(),t.updateSlidesClasses()}else if(r.freeModeSticky)return void t.slideToClosest();return void((!r.freeModeMomentum||d>=r.longSwipesMs)&&(t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses()))}let h=0,f=t.slidesSizesGrid[0];for(let v=0;v<a.length;v+=v<r.slidesPerGroupSkip?1:r.slidesPerGroup){const e=v<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;"undefined"!==typeof a[v+e]?p>=a[v]&&p<a[v+e]&&(h=v,f=a[v+e]-a[v]):p>=a[v]&&(h=v,f=a[a.length-1]-a[a.length-2])}const m=(p-a[h])/f,g=h<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(d>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(m>=r.longSwipesRatio?t.slideTo(h+g):t.slideTo(h)),"prev"===t.swipeDirection&&(m>1-r.longSwipesRatio?t.slideTo(h+g):t.slideTo(h))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);const e=t.navigation&&(c.target===t.navigation.nextEl||c.target===t.navigation.prevEl);e?c.target===t.navigation.nextEl?t.slideTo(h+g):t.slideTo(h):("next"===t.swipeDirection&&t.slideTo(h+g),"prev"===t.swipeDirection&&t.slideTo(h))}}function Ge(){const e=this,{params:t,el:n}=e;if(n&&0===n.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:i,snapGrid:s}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=i,e.allowSlideNext=r,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}function Ve(e){const t=this;t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function We(){const e=this,{wrapperEl:t,rtlTranslate:n}=e;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=n?t.scrollWidth-t.offsetWidth-t.scrollLeft:-t.scrollLeft:e.translate=-t.scrollTop,-0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const i=e.maxTranslate()-e.minTranslate();r=0===i?0:(e.translate-e.minTranslate())/i,r!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}let Ye=!1;function Xe(){}function Ke(){const e=this,{params:t,touchEvents:n,el:r,wrapperEl:i}=e;e.onTouchStart=Ue.bind(e),e.onTouchMove=qe.bind(e),e.onTouchEnd=He.bind(e),t.cssMode&&(e.onScroll=We.bind(e)),e.onClick=Ve.bind(e);const o=!!t.nested;if(!Z.touch&&Z.pointerEvents)r.addEventListener(n.start,e.onTouchStart,!1),s.addEventListener(n.move,e.onTouchMove,o),s.addEventListener(n.end,e.onTouchEnd,!1);else{if(Z.touch){const i=!("touchstart"!==n.start||!Z.passiveListener||!t.passiveListeners)&&{passive:!0,capture:!1};r.addEventListener(n.start,e.onTouchStart,i),r.addEventListener(n.move,e.onTouchMove,Z.passiveListener?{passive:!1,capture:o}:o),r.addEventListener(n.end,e.onTouchEnd,i),n.cancel&&r.addEventListener(n.cancel,e.onTouchEnd,i),Ye||(s.addEventListener("touchstart",Xe),Ye=!0)}(t.simulateTouch&&!Be.ios&&!Be.android||t.simulateTouch&&!Z.touch&&Be.ios)&&(r.addEventListener("mousedown",e.onTouchStart,!1),s.addEventListener("mousemove",e.onTouchMove,o),s.addEventListener("mouseup",e.onTouchEnd,!1))}(t.preventClicks||t.preventClicksPropagation)&&r.addEventListener("click",e.onClick,!0),t.cssMode&&i.addEventListener("scroll",e.onScroll),t.updateOnWindowResize?e.on(Be.ios||Be.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ge,!0):e.on("observerUpdate",Ge,!0)}function Je(){const e=this,{params:t,touchEvents:n,el:r,wrapperEl:i}=e,o=!!t.nested;if(!Z.touch&&Z.pointerEvents)r.removeEventListener(n.start,e.onTouchStart,!1),s.removeEventListener(n.move,e.onTouchMove,o),s.removeEventListener(n.end,e.onTouchEnd,!1);else{if(Z.touch){const i=!("onTouchStart"!==n.start||!Z.passiveListener||!t.passiveListeners)&&{passive:!0,capture:!1};r.removeEventListener(n.start,e.onTouchStart,i),r.removeEventListener(n.move,e.onTouchMove,o),r.removeEventListener(n.end,e.onTouchEnd,i),n.cancel&&r.removeEventListener(n.cancel,e.onTouchEnd,i)}(t.simulateTouch&&!Be.ios&&!Be.android||t.simulateTouch&&!Z.touch&&Be.ios)&&(r.removeEventListener("mousedown",e.onTouchStart,!1),s.removeEventListener("mousemove",e.onTouchMove,o),s.removeEventListener("mouseup",e.onTouchEnd,!1))}(t.preventClicks||t.preventClicksPropagation)&&r.removeEventListener("click",e.onClick,!0),t.cssMode&&i.removeEventListener("scroll",e.onScroll),e.off(Be.ios||Be.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ge)}var Ze={attachEvents:Ke,detachEvents:Je};function Qe(){const e=this,{activeIndex:t,initialized:n,loopedSlides:r=0,params:i,$el:s}=e,o=i.breakpoints;if(!o||o&&0===Object.keys(o).length)return;const a=e.getBreakpoint(o);if(a&&e.currentBreakpoint!==a){const l=a in o?o[a]:void 0;l&&["slidesPerView","spaceBetween","slidesPerGroup","slidesPerGroupSkip","slidesPerColumn"].forEach(e=>{const t=l[e];"undefined"!==typeof t&&(l[e]="slidesPerView"!==e||"AUTO"!==t&&"auto"!==t?"slidesPerView"===e?parseFloat(t):parseInt(t,10):"auto")});const c=l||e.originalParams,u=i.slidesPerColumn>1,d=c.slidesPerColumn>1;u&&!d?s.removeClass(`${i.containerModifierClass}multirow ${i.containerModifierClass}multirow-column`):!u&&d&&(s.addClass(`${i.containerModifierClass}multirow`),"column"===c.slidesPerColumnFill&&s.addClass(`${i.containerModifierClass}multirow-column`));const p=c.direction&&c.direction!==i.direction,h=i.loop&&(c.slidesPerView!==i.slidesPerView||p);p&&n&&e.changeDirection(),J.extend(e.params,c),J.extend(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),e.currentBreakpoint=a,h&&n&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-r+e.loopedSlides,0,!1)),e.emit("breakpoint",c)}}function et(e){if(!e)return;let t=!1;const n=Object.keys(e).map(e=>{if("string"===typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1)),n=a.innerHeight*t;return{value:n,point:e}}return{value:e,point:e}});n.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let r=0;r<n.length;r+=1){const{point:e,value:i}=n[r];i<=a.innerWidth&&(t=e)}return t||"max"}var tt={setBreakpoint:Qe,getBreakpoint:et};function nt(){const e=this,{classNames:t,params:n,rtl:r,$el:i}=e,s=[];s.push("initialized"),s.push(n.direction),n.freeMode&&s.push("free-mode"),n.autoHeight&&s.push("autoheight"),r&&s.push("rtl"),n.slidesPerColumn>1&&(s.push("multirow"),"column"===n.slidesPerColumnFill&&s.push("multirow-column")),Be.android&&s.push("android"),Be.ios&&s.push("ios"),n.cssMode&&s.push("css-mode"),s.forEach(e=>{t.push(n.containerModifierClass+e)}),i.addClass(t.join(" "))}function rt(){const e=this,{$el:t,classNames:n}=e;t.removeClass(n.join(" "))}var it={addClasses:nt,removeClasses:rt};function st(e,t,n,r,i,s){let o;function l(){s&&s()}const c=u(e).parent("picture")[0];c||e.complete&&i?l():t?(o=new a.Image,o.onload=l,o.onerror=l,r&&(o.sizes=r),n&&(o.srcset=n),t&&(o.src=t)):l()}function ot(){const e=this;function t(){"undefined"!==typeof e&&null!==e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let n=0;n<e.imagesToLoad.length;n+=1){const r=e.imagesToLoad[n];e.loadImage(r,r.currentSrc||r.getAttribute("src"),r.srcset||r.getAttribute("srcset"),r.sizes||r.getAttribute("sizes"),!0,t)}}var at={loadImage:st,preloadImages:ot};function lt(){const e=this,t=e.params,n=e.isLocked,r=e.slides.length>0&&t.slidesOffsetBefore+t.spaceBetween*(e.slides.length-1)+e.slides[0].offsetWidth*e.slides.length;t.slidesOffsetBefore&&t.slidesOffsetAfter&&r?e.isLocked=r<=e.size:e.isLocked=1===e.snapGrid.length,e.allowSlideNext=!e.isLocked,e.allowSlidePrev=!e.isLocked,n!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock"),n&&n!==e.isLocked&&(e.isEnd=!1,e.navigation&&e.navigation.update())}var ct={checkOverflow:lt},ut={init:!0,direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,preventInteractionOnTransition:!1,edgeSwipeDetection:!1,edgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,slidesPerGroupSkip:0,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!1,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-container-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0};const dt={update:ce,translate:me,transition:be,slide:_e,loop:Pe,grabCursor:Le,manipulation:Fe,events:Ze,breakpoints:tt,checkOverflow:ct,classes:it,images:at},pt={};class ht extends Q{constructor(...e){let t,n;1===e.length&&e[0].constructor&&e[0].constructor===Object?n=e[0]:[t,n]=e,n||(n={}),n=J.extend({},n),t&&!n.el&&(n.el=t),super(n),Object.keys(dt).forEach(e=>{Object.keys(dt[e]).forEach(t=>{ht.prototype[t]||(ht.prototype[t]=dt[e][t])})});const r=this;"undefined"===typeof r.modules&&(r.modules={}),Object.keys(r.modules).forEach(e=>{const t=r.modules[e];if(t.params){const e=Object.keys(t.params)[0],r=t.params[e];if("object"!==typeof r||null===r)return;if(!(e in n)||!("enabled"in r))return;!0===n[e]&&(n[e]={enabled:!0}),"object"!==typeof n[e]||"enabled"in n[e]||(n[e].enabled=!0),n[e]||(n[e]={enabled:!1})}});const i=J.extend({},ut);r.useModulesParams(i),r.params=J.extend({},i,pt,n),r.originalParams=J.extend({},r.params),r.passedParams=J.extend({},n),r.$=u;const s=u(r.params.el);if(t=s[0],!t)return;if(s.length>1){const e=[];return s.each((t,r)=>{const i=J.extend({},n,{el:r});e.push(new ht(i))}),e}let o;return t.swiper=r,s.data("swiper",r),t&&t.shadowRoot&&t.shadowRoot.querySelector?(o=u(t.shadowRoot.querySelector(`.${r.params.wrapperClass}`)),o.children=e=>s.children(e)):o=s.children(`.${r.params.wrapperClass}`),J.extend(r,{$el:s,el:t,$wrapperEl:o,wrapperEl:o[0],classNames:[],slides:u(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return"horizontal"===r.params.direction},isVertical(){return"vertical"===r.params.direction},rtl:"rtl"===t.dir.toLowerCase()||"rtl"===s.css("direction"),rtlTranslate:"horizontal"===r.params.direction&&("rtl"===t.dir.toLowerCase()||"rtl"===s.css("direction")),wrongRTL:"-webkit-box"===o.css("display"),activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:r.params.allowSlideNext,allowSlidePrev:r.params.allowSlidePrev,touchEvents:function(){const e=["touchstart","touchmove","touchend","touchcancel"];let t=["mousedown","mousemove","mouseup"];return Z.pointerEvents&&(t=["pointerdown","pointermove","pointerup"]),r.touchEventsTouch={start:e[0],move:e[1],end:e[2],cancel:e[3]},r.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},Z.touch||!r.params.simulateTouch?r.touchEventsTouch:r.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,formElements:"input, select, option, textarea, button, video, label",lastClickTime:J.now(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:r.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),r.useModules(),r.params.init&&r.init(),r}slidesPerViewDynamic(){const e=this,{params:t,slides:n,slidesGrid:r,size:i,activeIndex:s}=e;let o=1;if(t.centeredSlides){let e,t=n[s].swiperSlideSize;for(let r=s+1;r<n.length;r+=1)n[r]&&!e&&(t+=n[r].swiperSlideSize,o+=1,t>i&&(e=!0));for(let r=s-1;r>=0;r-=1)n[r]&&!e&&(t+=n[r].swiperSlideSize,o+=1,t>i&&(e=!0))}else for(let a=s+1;a<n.length;a+=1)r[a]-r[s]<i&&(o+=1);return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:n}=e;function r(){const t=e.rtlTranslate?-1*e.translate:e.translate,n=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(n),e.updateActiveIndex(),e.updateSlidesClasses()}let i;n.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode?(r(),e.params.autoHeight&&e.updateAutoHeight()):(i=("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),i||r()),n.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t=!0){const n=this,r=n.params.direction;return e||(e="horizontal"===r?"vertical":"horizontal"),e===r||"horizontal"!==e&&"vertical"!==e||(n.$el.removeClass(`${n.params.containerModifierClass}${r}`).addClass(`${n.params.containerModifierClass}${e}`),n.params.direction=e,n.slides.each((t,n)=>{"vertical"===e?n.style.width="":n.style.height=""}),n.emit("changeDirection"),t&&n.update()),n}init(){const e=this;e.initialized||(e.emit("beforeInit"),e.params.breakpoints&&e.setBreakpoint(),e.addClasses(),e.params.loop&&e.loopCreate(),e.updateSize(),e.updateSlides(),e.params.watchOverflow&&e.checkOverflow(),e.params.grabCursor&&e.setGrabCursor(),e.params.preloadImages&&e.preloadImages(),e.params.loop?e.slideTo(e.params.initialSlide+e.loopedSlides,0,e.params.runCallbacksOnInit):e.slideTo(e.params.initialSlide,0,e.params.runCallbacksOnInit),e.attachEvents(),e.initialized=!0,e.emit("init"))}destroy(e=!0,t=!0){const n=this,{params:r,$el:i,$wrapperEl:s,slides:o}=n;return"undefined"===typeof n.params||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),r.loop&&n.loopDestroy(),t&&(n.removeClasses(),i.removeAttr("style"),s.removeAttr("style"),o&&o.length&&o.removeClass([r.slideVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(e=>{n.off(e)}),!1!==e&&(n.$el[0].swiper=null,n.$el.data("swiper",null),J.deleteProps(n)),n.destroyed=!0),null}static extendDefaults(e){J.extend(pt,e)}static get extendedDefaults(){return pt}static get defaults(){return ut}static get Class(){return Q}static get $(){return u}}var ft={name:"device",proto:{device:Be},static:{device:Be}},mt={name:"support",proto:{support:Z},static:{support:Z}};const gt=function(){function e(){const e=a.navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0}return{isEdge:!!a.navigator.userAgent.match(/Edge/g),isSafari:e(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(a.navigator.userAgent)}}();var vt={name:"browser",proto:{browser:gt},static:{browser:gt}},yt={name:"resize",create(){const e=this;J.extend(e,{resize:{resizeHandler(){e&&!e.destroyed&&e.initialized&&(e.emit("beforeResize"),e.emit("resize"))},orientationChangeHandler(){e&&!e.destroyed&&e.initialized&&e.emit("orientationchange")}}})},on:{init(){const e=this;a.addEventListener("resize",e.resize.resizeHandler),a.addEventListener("orientationchange",e.resize.orientationChangeHandler)},destroy(){const e=this;a.removeEventListener("resize",e.resize.resizeHandler),a.removeEventListener("orientationchange",e.resize.orientationChangeHandler)}}};const bt={func:a.MutationObserver||a.WebkitMutationObserver,attach(e,t={}){const n=this,r=bt.func,i=new r(e=>{if(1===e.length)return void n.emit("observerUpdate",e[0]);const t=function(){n.emit("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(t):a.setTimeout(t,0)});i.observe(e,{attributes:"undefined"===typeof t.attributes||t.attributes,childList:"undefined"===typeof t.childList||t.childList,characterData:"undefined"===typeof t.characterData||t.characterData}),n.observer.observers.push(i)},init(){const e=this;if(Z.observer&&e.params.observer){if(e.params.observeParents){const t=e.$el.parents();for(let n=0;n<t.length;n+=1)e.observer.attach(t[n])}e.observer.attach(e.$el[0],{childList:e.params.observeSlideChildren}),e.observer.attach(e.$wrapperEl[0],{attributes:!1})}},destroy(){const e=this;e.observer.observers.forEach(e=>{e.disconnect()}),e.observer.observers=[]}};var wt={name:"observer",params:{observer:!1,observeParents:!1,observeSlideChildren:!1},create(){const e=this;J.extend(e,{observer:{init:bt.init.bind(e),attach:bt.attach.bind(e),destroy:bt.destroy.bind(e),observers:[]}})},on:{init(){const e=this;e.observer.init()},destroy(){const e=this;e.observer.destroy()}}};const xt={update(e){const t=this,{slidesPerView:n,slidesPerGroup:r,centeredSlides:i}=t.params,{addSlidesBefore:s,addSlidesAfter:o}=t.params.virtual,{from:a,to:l,slides:c,slidesGrid:u,renderSlide:d,offset:p}=t.virtual;t.updateActiveIndex();const h=t.activeIndex||0;let f,m,g;f=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",i?(m=Math.floor(n/2)+r+s,g=Math.floor(n/2)+r+o):(m=n+(r-1)+s,g=r+o);const v=Math.max((h||0)-g,0),y=Math.min((h||0)+m,c.length-1),b=(t.slidesGrid[v]||0)-(t.slidesGrid[0]||0);function w(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.lazy&&t.params.lazy.enabled&&t.lazy.load()}if(J.extend(t.virtual,{from:v,to:y,offset:b,slidesGrid:t.slidesGrid}),a===v&&l===y&&!e)return t.slidesGrid!==u&&b!==p&&t.slides.css(f,`${b}px`),void t.updateProgress();if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:b,from:v,to:y,slides:function(){const e=[];for(let t=v;t<=y;t+=1)e.push(c[t]);return e}()}),void w();const x=[],E=[];if(e)t.$wrapperEl.find(`.${t.params.slideClass}`).remove();else for(let S=a;S<=l;S+=1)(S<v||S>y)&&t.$wrapperEl.find(`.${t.params.slideClass}[data-swiper-slide-index="${S}"]`).remove();for(let S=0;S<c.length;S+=1)S>=v&&S<=y&&("undefined"===typeof l||e?E.push(S):(S>l&&E.push(S),S<a&&x.push(S)));E.forEach(e=>{t.$wrapperEl.append(d(c[e],e))}),x.sort((e,t)=>t-e).forEach(e=>{t.$wrapperEl.prepend(d(c[e],e))}),t.$wrapperEl.children(".swiper-slide").css(f,`${b}px`),w()},renderSlide(e,t){const n=this,r=n.params.virtual;if(r.cache&&n.virtual.cache[t])return n.virtual.cache[t];const i=r.renderSlide?u(r.renderSlide.call(n,e,t)):u(`<div class="${n.params.slideClass}" data-swiper-slide-index="${t}">${e}</div>`);return i.attr("data-swiper-slide-index")||i.attr("data-swiper-slide-index",t),r.cache&&(n.virtual.cache[t]=i),i},appendSlide(e){const t=this;if("object"===typeof e&&"length"in e)for(let n=0;n<e.length;n+=1)e[n]&&t.virtual.slides.push(e[n]);else t.virtual.slides.push(e);t.virtual.update(!0)},prependSlide(e){const t=this,n=t.activeIndex;let r=n+1,i=1;if(Array.isArray(e)){for(let n=0;n<e.length;n+=1)e[n]&&t.virtual.slides.unshift(e[n]);r=n+e.length,i=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){const e=t.virtual.cache,n={};Object.keys(e).forEach(t=>{const r=e[t],s=r.attr("data-swiper-slide-index");s&&r.attr("data-swiper-slide-index",parseInt(s,10)+1),n[parseInt(t,10)+i]=r}),t.virtual.cache=n}t.virtual.update(!0),t.slideTo(r,0)},removeSlide(e){const t=this;if("undefined"===typeof e||null===e)return;let n=t.activeIndex;if(Array.isArray(e))for(let r=e.length-1;r>=0;r-=1)t.virtual.slides.splice(e[r],1),t.params.virtual.cache&&delete t.virtual.cache[e[r]],e[r]<n&&(n-=1),n=Math.max(n,0);else t.virtual.slides.splice(e,1),t.params.virtual.cache&&delete t.virtual.cache[e],e<n&&(n-=1),n=Math.max(n,0);t.virtual.update(!0),t.slideTo(n,0)},removeAllSlides(){const e=this;e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),e.virtual.update(!0),e.slideTo(0,0)}};var Et={name:"virtual",params:{virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,addSlidesBefore:0,addSlidesAfter:0}},create(){const e=this;J.extend(e,{virtual:{update:xt.update.bind(e),appendSlide:xt.appendSlide.bind(e),prependSlide:xt.prependSlide.bind(e),removeSlide:xt.removeSlide.bind(e),removeAllSlides:xt.removeAllSlides.bind(e),renderSlide:xt.renderSlide.bind(e),slides:e.params.virtual.slides,cache:{}}})},on:{beforeInit(){const e=this;if(!e.params.virtual.enabled)return;e.classNames.push(`${e.params.containerModifierClass}virtual`);const t={watchSlidesProgress:!0};J.extend(e.params,t),J.extend(e.originalParams,t),e.params.initialSlide||e.virtual.update()},setTranslate(){const e=this;e.params.virtual.enabled&&e.virtual.update()}}};const St={handle(e){const t=this,{rtlTranslate:n}=t;let r=e;r.originalEvent&&(r=r.originalEvent);const i=r.keyCode||r.charCode,o=t.params.keyboard.pageUpDown,l=o&&33===i,c=o&&34===i,u=37===i,d=39===i,p=38===i,h=40===i;if(!t.allowSlideNext&&(t.isHorizontal()&&d||t.isVertical()&&h||c))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&u||t.isVertical()&&p||l))return!1;if(!(r.shiftKey||r.altKey||r.ctrlKey||r.metaKey)&&(!s.activeElement||!s.activeElement.nodeName||"input"!==s.activeElement.nodeName.toLowerCase()&&"textarea"!==s.activeElement.nodeName.toLowerCase())){if(t.params.keyboard.onlyInViewport&&(l||c||u||d||p||h)){let e=!1;if(t.$el.parents(`.${t.params.slideClass}`).length>0&&0===t.$el.parents(`.${t.params.slideActiveClass}`).length)return;const r=a.innerWidth,i=a.innerHeight,s=t.$el.offset();n&&(s.left-=t.$el[0].scrollLeft);const o=[[s.left,s.top],[s.left+t.width,s.top],[s.left,s.top+t.height],[s.left+t.width,s.top+t.height]];for(let t=0;t<o.length;t+=1){const n=o[t];n[0]>=0&&n[0]<=r&&n[1]>=0&&n[1]<=i&&(e=!0)}if(!e)return}t.isHorizontal()?((l||c||u||d)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),((c||d)&&!n||(l||u)&&n)&&t.slideNext(),((l||u)&&!n||(c||d)&&n)&&t.slidePrev()):((l||c||p||h)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),(c||h)&&t.slideNext(),(l||p)&&t.slidePrev()),t.emit("keyPress",i)}},enable(){const e=this;e.keyboard.enabled||(u(s).on("keydown",e.keyboard.handle),e.keyboard.enabled=!0)},disable(){const e=this;e.keyboard.enabled&&(u(s).off("keydown",e.keyboard.handle),e.keyboard.enabled=!1)}};var Ct={name:"keyboard",params:{keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}},create(){const e=this;J.extend(e,{keyboard:{enabled:!1,enable:St.enable.bind(e),disable:St.disable.bind(e),handle:St.handle.bind(e)}})},on:{init(){const e=this;e.params.keyboard.enabled&&e.keyboard.enable()},destroy(){const e=this;e.keyboard.enabled&&e.keyboard.disable()}}};function Tt(){const e="onwheel";let t=e in s;if(!t){const n=s.createElement("div");n.setAttribute(e,"return;"),t="function"===typeof n[e]}return!t&&s.implementation&&s.implementation.hasFeature&&!0!==s.implementation.hasFeature("","")&&(t=s.implementation.hasFeature("Events.wheel","3.0")),t}const kt={lastScrollTime:J.now(),lastEventBeforeSnap:void 0,recentWheelEvents:[],event(){return a.navigator.userAgent.indexOf("firefox")>-1?"DOMMouseScroll":Tt()?"wheel":"mousewheel"},normalize(e){const t=10,n=40,r=800;let i=0,s=0,o=0,a=0;return"detail"in e&&(s=e.detail),"wheelDelta"in e&&(s=-e.wheelDelta/120),"wheelDeltaY"in e&&(s=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(i=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(i=s,s=0),o=i*t,a=s*t,"deltaY"in e&&(a=e.deltaY),"deltaX"in e&&(o=e.deltaX),e.shiftKey&&!o&&(o=a,a=0),(o||a)&&e.deltaMode&&(1===e.deltaMode?(o*=n,a*=n):(o*=r,a*=r)),o&&!i&&(i=o<1?-1:1),a&&!s&&(s=a<1?-1:1),{spinX:i,spinY:s,pixelX:o,pixelY:a}},handleMouseEnter(){const e=this;e.mouseEntered=!0},handleMouseLeave(){const e=this;e.mouseEntered=!1},handle(e){let t=e;const n=this,r=n.params.mousewheel;n.params.cssMode&&t.preventDefault();let i=n.$el;if("container"!==n.params.mousewheel.eventsTarged&&(i=u(n.params.mousewheel.eventsTarged)),!n.mouseEntered&&!i[0].contains(t.target)&&!r.releaseOnEdges)return!0;t.originalEvent&&(t=t.originalEvent);let s=0;const o=n.rtlTranslate?-1:1,a=kt.normalize(t);if(r.forceToAxis)if(n.isHorizontal()){if(!(Math.abs(a.pixelX)>Math.abs(a.pixelY)))return!0;s=-a.pixelX*o}else{if(!(Math.abs(a.pixelY)>Math.abs(a.pixelX)))return!0;s=-a.pixelY}else s=Math.abs(a.pixelX)>Math.abs(a.pixelY)?-a.pixelX*o:-a.pixelY;if(0===s)return!0;if(r.invert&&(s=-s),n.params.freeMode){const e={time:J.now(),delta:Math.abs(s),direction:Math.sign(s)},{lastEventBeforeSnap:i}=n.mousewheel,o=i&&e.time<i.time+500&&e.delta<=i.delta&&e.direction===i.direction;if(!o){n.mousewheel.lastEventBeforeSnap=void 0,n.params.loop&&n.loopFix();let i=n.getTranslate()+s*r.sensitivity;const a=n.isBeginning,l=n.isEnd;if(i>=n.minTranslate()&&(i=n.minTranslate()),i<=n.maxTranslate()&&(i=n.maxTranslate()),n.setTransition(0),n.setTranslate(i),n.updateProgress(),n.updateActiveIndex(),n.updateSlidesClasses(),(!a&&n.isBeginning||!l&&n.isEnd)&&n.updateSlidesClasses(),n.params.freeModeSticky){clearTimeout(n.mousewheel.timeout),n.mousewheel.timeout=void 0;const t=n.mousewheel.recentWheelEvents;t.length>=15&&t.shift();const r=t.length?t[t.length-1]:void 0,i=t[0];if(t.push(e),r&&(e.delta>r.delta||e.direction!==r.direction))t.splice(0);else if(t.length>=15&&e.time-i.time<500&&i.delta-e.delta>=1&&e.delta<=6){const r=s>0?.8:.2;n.mousewheel.lastEventBeforeSnap=e,t.splice(0),n.mousewheel.timeout=J.nextTick(()=>{n.slideToClosest(n.params.speed,!0,void 0,r)},0)}n.mousewheel.timeout||(n.mousewheel.timeout=J.nextTick(()=>{const r=.5;n.mousewheel.lastEventBeforeSnap=e,t.splice(0),n.slideToClosest(n.params.speed,!0,void 0,r)},500))}if(o||n.emit("scroll",t),n.params.autoplay&&n.params.autoplayDisableOnInteraction&&n.autoplay.stop(),i===n.minTranslate()||i===n.maxTranslate())return!0}}else{const t={time:J.now(),delta:Math.abs(s),direction:Math.sign(s),raw:e},r=n.mousewheel.recentWheelEvents;r.length>=2&&r.shift();const i=r.length?r[r.length-1]:void 0;if(r.push(t),i?(t.direction!==i.direction||t.delta>i.delta||t.time>i.time+150)&&n.mousewheel.animateSlider(t):n.mousewheel.animateSlider(t),n.mousewheel.releaseScroll(t))return!0}return t.preventDefault?t.preventDefault():t.returnValue=!1,!1},animateSlider(e){const t=this;return e.delta>=6&&J.now()-t.mousewheel.lastScrollTime<60||(e.direction<0?t.isEnd&&!t.params.loop||t.animating||(t.slideNext(),t.emit("scroll",e.raw)):t.isBeginning&&!t.params.loop||t.animating||(t.slidePrev(),t.emit("scroll",e.raw)),t.mousewheel.lastScrollTime=(new a.Date).getTime(),!1)},releaseScroll(e){const t=this,n=t.params.mousewheel;if(e.direction<0){if(t.isEnd&&!t.params.loop&&n.releaseOnEdges)return!0}else if(t.isBeginning&&!t.params.loop&&n.releaseOnEdges)return!0;return!1},enable(){const e=this,t=kt.event();if(e.params.cssMode)return e.wrapperEl.removeEventListener(t,e.mousewheel.handle),!0;if(!t)return!1;if(e.mousewheel.enabled)return!1;let n=e.$el;return"container"!==e.params.mousewheel.eventsTarged&&(n=u(e.params.mousewheel.eventsTarged)),n.on("mouseenter",e.mousewheel.handleMouseEnter),n.on("mouseleave",e.mousewheel.handleMouseLeave),n.on(t,e.mousewheel.handle),e.mousewheel.enabled=!0,!0},disable(){const e=this,t=kt.event();if(e.params.cssMode)return e.wrapperEl.addEventListener(t,e.mousewheel.handle),!0;if(!t)return!1;if(!e.mousewheel.enabled)return!1;let n=e.$el;return"container"!==e.params.mousewheel.eventsTarged&&(n=u(e.params.mousewheel.eventsTarged)),n.off(t,e.mousewheel.handle),e.mousewheel.enabled=!1,!0}};var _t={name:"mousewheel",params:{mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarged:"container"}},create(){const e=this;J.extend(e,{mousewheel:{enabled:!1,enable:kt.enable.bind(e),disable:kt.disable.bind(e),handle:kt.handle.bind(e),handleMouseEnter:kt.handleMouseEnter.bind(e),handleMouseLeave:kt.handleMouseLeave.bind(e),animateSlider:kt.animateSlider.bind(e),releaseScroll:kt.releaseScroll.bind(e),lastScrollTime:J.now(),lastEventBeforeSnap:void 0,recentWheelEvents:[]}})},on:{init(){const e=this;!e.params.mousewheel.enabled&&e.params.cssMode&&e.mousewheel.disable(),e.params.mousewheel.enabled&&e.mousewheel.enable()},destroy(){const e=this;e.params.cssMode&&e.mousewheel.enable(),e.mousewheel.enabled&&e.mousewheel.disable()}}};const Ot={update(){const e=this,t=e.params.navigation;if(e.params.loop)return;const{$nextEl:n,$prevEl:r}=e.navigation;r&&r.length>0&&(e.isBeginning?r.addClass(t.disabledClass):r.removeClass(t.disabledClass),r[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](t.lockClass)),n&&n.length>0&&(e.isEnd?n.addClass(t.disabledClass):n.removeClass(t.disabledClass),n[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](t.lockClass))},onPrevClick(e){const t=this;e.preventDefault(),t.isBeginning&&!t.params.loop||t.slidePrev()},onNextClick(e){const t=this;e.preventDefault(),t.isEnd&&!t.params.loop||t.slideNext()},init(){const e=this,t=e.params.navigation;if(!t.nextEl&&!t.prevEl)return;let n,r;t.nextEl&&(n=u(t.nextEl),e.params.uniqueNavElements&&"string"===typeof t.nextEl&&n.length>1&&1===e.$el.find(t.nextEl).length&&(n=e.$el.find(t.nextEl))),t.prevEl&&(r=u(t.prevEl),e.params.uniqueNavElements&&"string"===typeof t.prevEl&&r.length>1&&1===e.$el.find(t.prevEl).length&&(r=e.$el.find(t.prevEl))),n&&n.length>0&&n.on("click",e.navigation.onNextClick),r&&r.length>0&&r.on("click",e.navigation.onPrevClick),J.extend(e.navigation,{$nextEl:n,nextEl:n&&n[0],$prevEl:r,prevEl:r&&r[0]})},destroy(){const e=this,{$nextEl:t,$prevEl:n}=e.navigation;t&&t.length&&(t.off("click",e.navigation.onNextClick),t.removeClass(e.params.navigation.disabledClass)),n&&n.length&&(n.off("click",e.navigation.onPrevClick),n.removeClass(e.params.navigation.disabledClass))}};var $t={name:"navigation",params:{navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}},create(){const e=this;J.extend(e,{navigation:{init:Ot.init.bind(e),update:Ot.update.bind(e),destroy:Ot.destroy.bind(e),onNextClick:Ot.onNextClick.bind(e),onPrevClick:Ot.onPrevClick.bind(e)}})},on:{init(){const e=this;e.navigation.init(),e.navigation.update()},toEdge(){const e=this;e.navigation.update()},fromEdge(){const e=this;e.navigation.update()},destroy(){const e=this;e.navigation.destroy()},click(e){const t=this,{$nextEl:n,$prevEl:r}=t.navigation;if(t.params.navigation.hideOnClick&&!u(e.target).is(r)&&!u(e.target).is(n)){let e;n?e=n.hasClass(t.params.navigation.hiddenClass):r&&(e=r.hasClass(t.params.navigation.hiddenClass)),!0===e?t.emit("navigationShow",t):t.emit("navigationHide",t),n&&n.toggleClass(t.params.navigation.hiddenClass),r&&r.toggleClass(t.params.navigation.hiddenClass)}}}};const Rt={update(){const e=this,t=e.rtl,n=e.params.pagination;if(!n.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length)return;const r=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el;let s;const o=e.params.loop?Math.ceil((r-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(s=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup),s>r-1-2*e.loopedSlides&&(s-=r-2*e.loopedSlides),s>o-1&&(s-=o),s<0&&"bullets"!==e.params.paginationType&&(s=o+s)):s="undefined"!==typeof e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===n.type&&e.pagination.bullets&&e.pagination.bullets.length>0){const r=e.pagination.bullets;let o,a,l;if(n.dynamicBullets&&(e.pagination.bulletSize=r.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),i.css(e.isHorizontal()?"width":"height",e.pagination.bulletSize*(n.dynamicMainBullets+4)+"px"),n.dynamicMainBullets>1&&void 0!==e.previousIndex&&(e.pagination.dynamicBulletIndex+=s-e.previousIndex,e.pagination.dynamicBulletIndex>n.dynamicMainBullets-1?e.pagination.dynamicBulletIndex=n.dynamicMainBullets-1:e.pagination.dynamicBulletIndex<0&&(e.pagination.dynamicBulletIndex=0)),o=s-e.pagination.dynamicBulletIndex,a=o+(Math.min(r.length,n.dynamicMainBullets)-1),l=(a+o)/2),r.removeClass(`${n.bulletActiveClass} ${n.bulletActiveClass}-next ${n.bulletActiveClass}-next-next ${n.bulletActiveClass}-prev ${n.bulletActiveClass}-prev-prev ${n.bulletActiveClass}-main`),i.length>1)r.each((e,t)=>{const r=u(t),i=r.index();i===s&&r.addClass(n.bulletActiveClass),n.dynamicBullets&&(i>=o&&i<=a&&r.addClass(`${n.bulletActiveClass}-main`),i===o&&r.prev().addClass(`${n.bulletActiveClass}-prev`).prev().addClass(`${n.bulletActiveClass}-prev-prev`),i===a&&r.next().addClass(`${n.bulletActiveClass}-next`).next().addClass(`${n.bulletActiveClass}-next-next`))});else{const t=r.eq(s),i=t.index();if(t.addClass(n.bulletActiveClass),n.dynamicBullets){const t=r.eq(o),s=r.eq(a);for(let e=o;e<=a;e+=1)r.eq(e).addClass(`${n.bulletActiveClass}-main`);if(e.params.loop)if(i>=r.length-n.dynamicMainBullets){for(let e=n.dynamicMainBullets;e>=0;e-=1)r.eq(r.length-e).addClass(`${n.bulletActiveClass}-main`);r.eq(r.length-n.dynamicMainBullets-1).addClass(`${n.bulletActiveClass}-prev`)}else t.prev().addClass(`${n.bulletActiveClass}-prev`).prev().addClass(`${n.bulletActiveClass}-prev-prev`),s.next().addClass(`${n.bulletActiveClass}-next`).next().addClass(`${n.bulletActiveClass}-next-next`);else t.prev().addClass(`${n.bulletActiveClass}-prev`).prev().addClass(`${n.bulletActiveClass}-prev-prev`),s.next().addClass(`${n.bulletActiveClass}-next`).next().addClass(`${n.bulletActiveClass}-next-next`)}}if(n.dynamicBullets){const i=Math.min(r.length,n.dynamicMainBullets+4),s=(e.pagination.bulletSize*i-e.pagination.bulletSize)/2-l*e.pagination.bulletSize,o=t?"right":"left";r.css(e.isHorizontal()?o:"top",`${s}px`)}}if("fraction"===n.type&&(i.find(`.${n.currentClass}`).text(n.formatFractionCurrent(s+1)),i.find(`.${n.totalClass}`).text(n.formatFractionTotal(o))),"progressbar"===n.type){let t;t=n.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";const r=(s+1)/o;let a=1,l=1;"horizontal"===t?a=r:l=r,i.find(`.${n.progressbarFillClass}`).transform(`translate3d(0,0,0) scaleX(${a}) scaleY(${l})`).transition(e.params.speed)}"custom"===n.type&&n.renderCustom?(i.html(n.renderCustom(e,s+1,o)),e.emit("paginationRender",e,i[0])):e.emit("paginationUpdate",e,i[0]),i[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](n.lockClass)},render(){const e=this,t=e.params.pagination;if(!t.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length)return;const n=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,r=e.pagination.$el;let i="";if("bullets"===t.type){const s=e.params.loop?Math.ceil((n-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;for(let n=0;n<s;n+=1)t.renderBullet?i+=t.renderBullet.call(e,n,t.bulletClass):i+=`<${t.bulletElement} class="${t.bulletClass}"></${t.bulletElement}>`;r.html(i),e.pagination.bullets=r.find(`.${t.bulletClass}`)}"fraction"===t.type&&(i=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):`<span class="${t.currentClass}"></span> / <span class="${t.totalClass}"></span>`,r.html(i)),"progressbar"===t.type&&(i=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):`<span class="${t.progressbarFillClass}"></span>`,r.html(i)),"custom"!==t.type&&e.emit("paginationRender",e.pagination.$el[0])},init(){const e=this,t=e.params.pagination;if(!t.el)return;let n=u(t.el);0!==n.length&&(e.params.uniqueNavElements&&"string"===typeof t.el&&n.length>1&&(n=e.$el.find(t.el)),"bullets"===t.type&&t.clickable&&n.addClass(t.clickableClass),n.addClass(t.modifierClass+t.type),"bullets"===t.type&&t.dynamicBullets&&(n.addClass(`${t.modifierClass}${t.type}-dynamic`),e.pagination.dynamicBulletIndex=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&n.addClass(t.progressbarOppositeClass),t.clickable&&n.on("click",`.${t.bulletClass}`,function(t){t.preventDefault();let n=u(this).index()*e.params.slidesPerGroup;e.params.loop&&(n+=e.loopedSlides),e.slideTo(n)}),J.extend(e.pagination,{$el:n,el:n[0]}))},destroy(){const e=this,t=e.params.pagination;if(!t.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length)return;const n=e.pagination.$el;n.removeClass(t.hiddenClass),n.removeClass(t.modifierClass+t.type),e.pagination.bullets&&e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&n.off("click",`.${t.bulletClass}`)}};var Pt={name:"pagination",params:{pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",modifierClass:"swiper-pagination-",currentClass:"swiper-pagination-current",totalClass:"swiper-pagination-total",hiddenClass:"swiper-pagination-hidden",progressbarFillClass:"swiper-pagination-progressbar-fill",progressbarOppositeClass:"swiper-pagination-progressbar-opposite",clickableClass:"swiper-pagination-clickable",lockClass:"swiper-pagination-lock"}},create(){const e=this;J.extend(e,{pagination:{init:Rt.init.bind(e),render:Rt.render.bind(e),update:Rt.update.bind(e),destroy:Rt.destroy.bind(e),dynamicBulletIndex:0}})},on:{init(){const e=this;e.pagination.init(),e.pagination.render(),e.pagination.update()},activeIndexChange(){const e=this;(e.params.loop||"undefined"===typeof e.snapIndex)&&e.pagination.update()},snapIndexChange(){const e=this;e.params.loop||e.pagination.update()},slidesLengthChange(){const e=this;e.params.loop&&(e.pagination.render(),e.pagination.update())},snapGridLengthChange(){const e=this;e.params.loop||(e.pagination.render(),e.pagination.update())},destroy(){const e=this;e.pagination.destroy()},click(e){const t=this;if(t.params.pagination.el&&t.params.pagination.hideOnClick&&t.pagination.$el.length>0&&!u(e.target).hasClass(t.params.pagination.bulletClass)){const e=t.pagination.$el.hasClass(t.params.pagination.hiddenClass);!0===e?t.emit("paginationShow",t):t.emit("paginationHide",t),t.pagination.$el.toggleClass(t.params.pagination.hiddenClass)}}}};const At={setTranslate(){const e=this;if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t,rtlTranslate:n,progress:r}=e,{dragSize:i,trackSize:s,$dragEl:o,$el:a}=t,l=e.params.scrollbar;let c=i,u=(s-i)*r;n?(u=-u,u>0?(c=i-u,u=0):-u+i>s&&(c=s+u)):u<0?(c=i+u,u=0):u+i>s&&(c=s-u),e.isHorizontal()?(o.transform(`translate3d(${u}px, 0, 0)`),o[0].style.width=`${c}px`):(o.transform(`translate3d(0px, ${u}px, 0)`),o[0].style.height=`${c}px`),l.hide&&(clearTimeout(e.scrollbar.timeout),a[0].style.opacity=1,e.scrollbar.timeout=setTimeout(()=>{a[0].style.opacity=0,a.transition(400)},1e3))},setTransition(e){const t=this;t.params.scrollbar.el&&t.scrollbar.el&&t.scrollbar.$dragEl.transition(e)},updateSize(){const e=this;if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:t}=e,{$dragEl:n,$el:r}=t;n[0].style.width="",n[0].style.height="";const i=e.isHorizontal()?r[0].offsetWidth:r[0].offsetHeight,s=e.size/e.virtualSize,o=s*(i/e.size);let a;a="auto"===e.params.scrollbar.dragSize?i*s:parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?n[0].style.width=`${a}px`:n[0].style.height=`${a}px`,r[0].style.display=s>=1?"none":"",e.params.scrollbar.hide&&(r[0].style.opacity=0),J.extend(t,{trackSize:i,divider:s,moveDivider:o,dragSize:a}),t.$el[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](e.params.scrollbar.lockClass)},getPointerPosition(e){const t=this;return t.isHorizontal()?"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientX:e.clientX:"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientY:e.clientY},setDragPosition(e){const t=this,{scrollbar:n,rtlTranslate:r}=t,{$el:i,dragSize:s,trackSize:o,dragStartPos:a}=n;let l;l=(n.getPointerPosition(e)-i.offset()[t.isHorizontal()?"left":"top"]-(null!==a?a:s/2))/(o-s),l=Math.max(Math.min(l,1),0),r&&(l=1-l);const c=t.minTranslate()+(t.maxTranslate()-t.minTranslate())*l;t.updateProgress(c),t.setTranslate(c),t.updateActiveIndex(),t.updateSlidesClasses()},onDragStart(e){const t=this,n=t.params.scrollbar,{scrollbar:r,$wrapperEl:i}=t,{$el:s,$dragEl:o}=r;t.scrollbar.isTouched=!0,t.scrollbar.dragStartPos=e.target===o[0]||e.target===o?r.getPointerPosition(e)-e.target.getBoundingClientRect()[t.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),i.transition(100),o.transition(100),r.setDragPosition(e),clearTimeout(t.scrollbar.dragTimeout),s.transition(0),n.hide&&s.css("opacity",1),t.params.cssMode&&t.$wrapperEl.css("scroll-snap-type","none"),t.emit("scrollbarDragStart",e)},onDragMove(e){const t=this,{scrollbar:n,$wrapperEl:r}=t,{$el:i,$dragEl:s}=n;t.scrollbar.isTouched&&(e.preventDefault?e.preventDefault():e.returnValue=!1,n.setDragPosition(e),r.transition(0),i.transition(0),s.transition(0),t.emit("scrollbarDragMove",e))},onDragEnd(e){const t=this,n=t.params.scrollbar,{scrollbar:r,$wrapperEl:i}=t,{$el:s}=r;t.scrollbar.isTouched&&(t.scrollbar.isTouched=!1,t.params.cssMode&&(t.$wrapperEl.css("scroll-snap-type",""),i.transition("")),n.hide&&(clearTimeout(t.scrollbar.dragTimeout),t.scrollbar.dragTimeout=J.nextTick(()=>{s.css("opacity",0),s.transition(400)},1e3)),t.emit("scrollbarDragEnd",e),n.snapOnRelease&&t.slideToClosest())},enableDraggable(){const e=this;if(!e.params.scrollbar.el)return;const{scrollbar:t,touchEventsTouch:n,touchEventsDesktop:r,params:i}=e,o=t.$el,a=o[0],l=!(!Z.passiveListener||!i.passiveListeners)&&{passive:!1,capture:!1},c=!(!Z.passiveListener||!i.passiveListeners)&&{passive:!0,capture:!1};Z.touch?(a.addEventListener(n.start,e.scrollbar.onDragStart,l),a.addEventListener(n.move,e.scrollbar.onDragMove,l),a.addEventListener(n.end,e.scrollbar.onDragEnd,c)):(a.addEventListener(r.start,e.scrollbar.onDragStart,l),s.addEventListener(r.move,e.scrollbar.onDragMove,l),s.addEventListener(r.end,e.scrollbar.onDragEnd,c))},disableDraggable(){const e=this;if(!e.params.scrollbar.el)return;const{scrollbar:t,touchEventsTouch:n,touchEventsDesktop:r,params:i}=e,o=t.$el,a=o[0],l=!(!Z.passiveListener||!i.passiveListeners)&&{passive:!1,capture:!1},c=!(!Z.passiveListener||!i.passiveListeners)&&{passive:!0,capture:!1};Z.touch?(a.removeEventListener(n.start,e.scrollbar.onDragStart,l),a.removeEventListener(n.move,e.scrollbar.onDragMove,l),a.removeEventListener(n.end,e.scrollbar.onDragEnd,c)):(a.removeEventListener(r.start,e.scrollbar.onDragStart,l),s.removeEventListener(r.move,e.scrollbar.onDragMove,l),s.removeEventListener(r.end,e.scrollbar.onDragEnd,c))},init(){const e=this;if(!e.params.scrollbar.el)return;const{scrollbar:t,$el:n}=e,r=e.params.scrollbar;let i=u(r.el);e.params.uniqueNavElements&&"string"===typeof r.el&&i.length>1&&1===n.find(r.el).length&&(i=n.find(r.el));let s=i.find(`.${e.params.scrollbar.dragClass}`);0===s.length&&(s=u(`<div class="${e.params.scrollbar.dragClass}"></div>`),i.append(s)),J.extend(t,{$el:i,el:i[0],$dragEl:s,dragEl:s[0]}),r.draggable&&t.enableDraggable()},destroy(){const e=this;e.scrollbar.disableDraggable()}};var It={name:"scrollbar",params:{scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag"}},create(){const e=this;J.extend(e,{scrollbar:{init:At.init.bind(e),destroy:At.destroy.bind(e),updateSize:At.updateSize.bind(e),setTranslate:At.setTranslate.bind(e),setTransition:At.setTransition.bind(e),enableDraggable:At.enableDraggable.bind(e),disableDraggable:At.disableDraggable.bind(e),setDragPosition:At.setDragPosition.bind(e),getPointerPosition:At.getPointerPosition.bind(e),onDragStart:At.onDragStart.bind(e),onDragMove:At.onDragMove.bind(e),onDragEnd:At.onDragEnd.bind(e),isTouched:!1,timeout:null,dragTimeout:null}})},on:{init(){const e=this;e.scrollbar.init(),e.scrollbar.updateSize(),e.scrollbar.setTranslate()},update(){const e=this;e.scrollbar.updateSize()},resize(){const e=this;e.scrollbar.updateSize()},observerUpdate(){const e=this;e.scrollbar.updateSize()},setTranslate(){const e=this;e.scrollbar.setTranslate()},setTransition(e){const t=this;t.scrollbar.setTransition(e)},destroy(){const e=this;e.scrollbar.destroy()}}};const Lt={setTransform(e,t){const n=this,{rtl:r}=n,i=u(e),s=r?-1:1,o=i.attr("data-swiper-parallax")||"0";let a=i.attr("data-swiper-parallax-x"),l=i.attr("data-swiper-parallax-y");const c=i.attr("data-swiper-parallax-scale"),d=i.attr("data-swiper-parallax-opacity");if(a||l?(a=a||"0",l=l||"0"):n.isHorizontal()?(a=o,l="0"):(l=o,a="0"),a=a.indexOf("%")>=0?parseInt(a,10)*t*s+"%":a*t*s+"px",l=l.indexOf("%")>=0?parseInt(l,10)*t+"%":l*t+"px","undefined"!==typeof d&&null!==d){const e=d-(d-1)*(1-Math.abs(t));i[0].style.opacity=e}if("undefined"===typeof c||null===c)i.transform(`translate3d(${a}, ${l}, 0px)`);else{const e=c-(c-1)*(1-Math.abs(t));i.transform(`translate3d(${a}, ${l}, 0px) scale(${e})`)}},setTranslate(){const e=this,{$el:t,slides:n,progress:r,snapGrid:i}=e;t.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((t,n)=>{e.parallax.setTransform(n,r)}),n.each((t,n)=>{let s=n.progress;e.params.slidesPerGroup>1&&"auto"!==e.params.slidesPerView&&(s+=Math.ceil(t/2)-r*(i.length-1)),s=Math.min(Math.max(s,-1),1),u(n).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((t,n)=>{e.parallax.setTransform(n,s)})})},setTransition(e=this.params.speed){const t=this,{$el:n}=t;n.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((t,n)=>{const r=u(n);let i=parseInt(r.attr("data-swiper-parallax-duration"),10)||e;0===e&&(i=0),r.transition(i)})}};var Mt={name:"parallax",params:{parallax:{enabled:!1}},create(){const e=this;J.extend(e,{parallax:{setTransform:Lt.setTransform.bind(e),setTranslate:Lt.setTranslate.bind(e),setTransition:Lt.setTransition.bind(e)}})},on:{beforeInit(){const e=this;e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},init(){const e=this;e.params.parallax.enabled&&e.parallax.setTranslate()},setTranslate(){const e=this;e.params.parallax.enabled&&e.parallax.setTranslate()},setTransition(e){const t=this;t.params.parallax.enabled&&t.parallax.setTransition(e)}}};const Nt={getDistanceBetweenTouches(e){if(e.targetTouches.length<2)return 1;const t=e.targetTouches[0].pageX,n=e.targetTouches[0].pageY,r=e.targetTouches[1].pageX,i=e.targetTouches[1].pageY,s=Math.sqrt((r-t)**2+(i-n)**2);return s},onGestureStart(e){const t=this,n=t.params.zoom,r=t.zoom,{gesture:i}=r;if(r.fakeGestureTouched=!1,r.fakeGestureMoved=!1,!Z.gestures){if("touchstart"!==e.type||"touchstart"===e.type&&e.targetTouches.length<2)return;r.fakeGestureTouched=!0,i.scaleStart=Nt.getDistanceBetweenTouches(e)}i.$slideEl&&i.$slideEl.length||(i.$slideEl=u(e.target).closest(`.${t.params.slideClass}`),0===i.$slideEl.length&&(i.$slideEl=t.slides.eq(t.activeIndex)),i.$imageEl=i.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),i.$imageWrapEl=i.$imageEl.parent(`.${n.containerClass}`),i.maxRatio=i.$imageWrapEl.attr("data-swiper-zoom")||n.maxRatio,0!==i.$imageWrapEl.length)?(i.$imageEl&&i.$imageEl.transition(0),t.zoom.isScaling=!0):i.$imageEl=void 0},onGestureChange(e){const t=this,n=t.params.zoom,r=t.zoom,{gesture:i}=r;if(!Z.gestures){if("touchmove"!==e.type||"touchmove"===e.type&&e.targetTouches.length<2)return;r.fakeGestureMoved=!0,i.scaleMove=Nt.getDistanceBetweenTouches(e)}i.$imageEl&&0!==i.$imageEl.length&&(Z.gestures?r.scale=e.scale*r.currentScale:r.scale=i.scaleMove/i.scaleStart*r.currentScale,r.scale>i.maxRatio&&(r.scale=i.maxRatio-1+(r.scale-i.maxRatio+1)**.5),r.scale<n.minRatio&&(r.scale=n.minRatio+1-(n.minRatio-r.scale+1)**.5),i.$imageEl.transform(`translate3d(0,0,0) scale(${r.scale})`))},onGestureEnd(e){const t=this,n=t.params.zoom,r=t.zoom,{gesture:i}=r;if(!Z.gestures){if(!r.fakeGestureTouched||!r.fakeGestureMoved)return;if("touchend"!==e.type||"touchend"===e.type&&e.changedTouches.length<2&&!Be.android)return;r.fakeGestureTouched=!1,r.fakeGestureMoved=!1}i.$imageEl&&0!==i.$imageEl.length&&(r.scale=Math.max(Math.min(r.scale,i.maxRatio),n.minRatio),i.$imageEl.transition(t.params.speed).transform(`translate3d(0,0,0) scale(${r.scale})`),r.currentScale=r.scale,r.isScaling=!1,1===r.scale&&(i.$slideEl=void 0))},onTouchStart(e){const t=this,n=t.zoom,{gesture:r,image:i}=n;r.$imageEl&&0!==r.$imageEl.length&&(i.isTouched||(Be.android&&e.cancelable&&e.preventDefault(),i.isTouched=!0,i.touchesStart.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,i.touchesStart.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY))},onTouchMove(e){const t=this,n=t.zoom,{gesture:r,image:i,velocity:s}=n;if(!r.$imageEl||0===r.$imageEl.length)return;if(t.allowClick=!1,!i.isTouched||!r.$slideEl)return;i.isMoved||(i.width=r.$imageEl[0].offsetWidth,i.height=r.$imageEl[0].offsetHeight,i.startX=J.getTranslate(r.$imageWrapEl[0],"x")||0,i.startY=J.getTranslate(r.$imageWrapEl[0],"y")||0,r.slideWidth=r.$slideEl[0].offsetWidth,r.slideHeight=r.$slideEl[0].offsetHeight,r.$imageWrapEl.transition(0),t.rtl&&(i.startX=-i.startX,i.startY=-i.startY));const o=i.width*n.scale,a=i.height*n.scale;if(!(o<r.slideWidth&&a<r.slideHeight)){if(i.minX=Math.min(r.slideWidth/2-o/2,0),i.maxX=-i.minX,i.minY=Math.min(r.slideHeight/2-a/2,0),i.maxY=-i.minY,i.touchesCurrent.x="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,i.touchesCurrent.y="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,!i.isMoved&&!n.isScaling){if(t.isHorizontal()&&(Math.floor(i.minX)===Math.floor(i.startX)&&i.touchesCurrent.x<i.touchesStart.x||Math.floor(i.maxX)===Math.floor(i.startX)&&i.touchesCurrent.x>i.touchesStart.x))return void(i.isTouched=!1);if(!t.isHorizontal()&&(Math.floor(i.minY)===Math.floor(i.startY)&&i.touchesCurrent.y<i.touchesStart.y||Math.floor(i.maxY)===Math.floor(i.startY)&&i.touchesCurrent.y>i.touchesStart.y))return void(i.isTouched=!1)}e.cancelable&&e.preventDefault(),e.stopPropagation(),i.isMoved=!0,i.currentX=i.touchesCurrent.x-i.touchesStart.x+i.startX,i.currentY=i.touchesCurrent.y-i.touchesStart.y+i.startY,i.currentX<i.minX&&(i.currentX=i.minX+1-(i.minX-i.currentX+1)**.8),i.currentX>i.maxX&&(i.currentX=i.maxX-1+(i.currentX-i.maxX+1)**.8),i.currentY<i.minY&&(i.currentY=i.minY+1-(i.minY-i.currentY+1)**.8),i.currentY>i.maxY&&(i.currentY=i.maxY-1+(i.currentY-i.maxY+1)**.8),s.prevPositionX||(s.prevPositionX=i.touchesCurrent.x),s.prevPositionY||(s.prevPositionY=i.touchesCurrent.y),s.prevTime||(s.prevTime=Date.now()),s.x=(i.touchesCurrent.x-s.prevPositionX)/(Date.now()-s.prevTime)/2,s.y=(i.touchesCurrent.y-s.prevPositionY)/(Date.now()-s.prevTime)/2,Math.abs(i.touchesCurrent.x-s.prevPositionX)<2&&(s.x=0),Math.abs(i.touchesCurrent.y-s.prevPositionY)<2&&(s.y=0),s.prevPositionX=i.touchesCurrent.x,s.prevPositionY=i.touchesCurrent.y,s.prevTime=Date.now(),r.$imageWrapEl.transform(`translate3d(${i.currentX}px, ${i.currentY}px,0)`)}},onTouchEnd(){const e=this,t=e.zoom,{gesture:n,image:r,velocity:i}=t;if(!n.$imageEl||0===n.$imageEl.length)return;if(!r.isTouched||!r.isMoved)return r.isTouched=!1,void(r.isMoved=!1);r.isTouched=!1,r.isMoved=!1;let s=300,o=300;const a=i.x*s,l=r.currentX+a,c=i.y*o,u=r.currentY+c;0!==i.x&&(s=Math.abs((l-r.currentX)/i.x)),0!==i.y&&(o=Math.abs((u-r.currentY)/i.y));const d=Math.max(s,o);r.currentX=l,r.currentY=u;const p=r.width*t.scale,h=r.height*t.scale;r.minX=Math.min(n.slideWidth/2-p/2,0),r.maxX=-r.minX,r.minY=Math.min(n.slideHeight/2-h/2,0),r.maxY=-r.minY,r.currentX=Math.max(Math.min(r.currentX,r.maxX),r.minX),r.currentY=Math.max(Math.min(r.currentY,r.maxY),r.minY),n.$imageWrapEl.transition(d).transform(`translate3d(${r.currentX}px, ${r.currentY}px,0)`)},onTransitionEnd(){const e=this,t=e.zoom,{gesture:n}=t;n.$slideEl&&e.previousIndex!==e.activeIndex&&(n.$imageEl&&n.$imageEl.transform("translate3d(0,0,0) scale(1)"),n.$imageWrapEl&&n.$imageWrapEl.transform("translate3d(0,0,0)"),t.scale=1,t.currentScale=1,n.$slideEl=void 0,n.$imageEl=void 0,n.$imageWrapEl=void 0)},toggle(e){const t=this,n=t.zoom;n.scale&&1!==n.scale?n.out():n.in(e)},in(e){const t=this,n=t.zoom,r=t.params.zoom,{gesture:i,image:s}=n;if(i.$slideEl||(t.params.virtual&&t.params.virtual.enabled&&t.virtual?i.$slideEl=t.$wrapperEl.children(`.${t.params.slideActiveClass}`):i.$slideEl=t.slides.eq(t.activeIndex),i.$imageEl=i.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),i.$imageWrapEl=i.$imageEl.parent(`.${r.containerClass}`)),!i.$imageEl||0===i.$imageEl.length)return;let o,a,l,c,u,d,p,h,f,m,g,v,y,b,w,x,E,S;i.$slideEl.addClass(`${r.zoomedSlideClass}`),"undefined"===typeof s.touchesStart.x&&e?(o="touchend"===e.type?e.changedTouches[0].pageX:e.pageX,a="touchend"===e.type?e.changedTouches[0].pageY:e.pageY):(o=s.touchesStart.x,a=s.touchesStart.y),n.scale=i.$imageWrapEl.attr("data-swiper-zoom")||r.maxRatio,n.currentScale=i.$imageWrapEl.attr("data-swiper-zoom")||r.maxRatio,e?(E=i.$slideEl[0].offsetWidth,S=i.$slideEl[0].offsetHeight,l=i.$slideEl.offset().left,c=i.$slideEl.offset().top,u=l+E/2-o,d=c+S/2-a,f=i.$imageEl[0].offsetWidth,m=i.$imageEl[0].offsetHeight,g=f*n.scale,v=m*n.scale,y=Math.min(E/2-g/2,0),b=Math.min(S/2-v/2,0),w=-y,x=-b,p=u*n.scale,h=d*n.scale,p<y&&(p=y),p>w&&(p=w),h<b&&(h=b),h>x&&(h=x)):(p=0,h=0),i.$imageWrapEl.transition(300).transform(`translate3d(${p}px, ${h}px,0)`),i.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${n.scale})`)},out(){const e=this,t=e.zoom,n=e.params.zoom,{gesture:r}=t;r.$slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?r.$slideEl=e.$wrapperEl.children(`.${e.params.slideActiveClass}`):r.$slideEl=e.slides.eq(e.activeIndex),r.$imageEl=r.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),r.$imageWrapEl=r.$imageEl.parent(`.${n.containerClass}`)),r.$imageEl&&0!==r.$imageEl.length&&(t.scale=1,t.currentScale=1,r.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),r.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),r.$slideEl.removeClass(`${n.zoomedSlideClass}`),r.$slideEl=void 0)},enable(){const e=this,t=e.zoom;if(t.enabled)return;t.enabled=!0;const n=!("touchstart"!==e.touchEvents.start||!Z.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1},r=!Z.passiveListener||{passive:!1,capture:!0},i=`.${e.params.slideClass}`;Z.gestures?(e.$wrapperEl.on("gesturestart",i,t.onGestureStart,n),e.$wrapperEl.on("gesturechange",i,t.onGestureChange,n),e.$wrapperEl.on("gestureend",i,t.onGestureEnd,n)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.on(e.touchEvents.start,i,t.onGestureStart,n),e.$wrapperEl.on(e.touchEvents.move,i,t.onGestureChange,r),e.$wrapperEl.on(e.touchEvents.end,i,t.onGestureEnd,n),e.touchEvents.cancel&&e.$wrapperEl.on(e.touchEvents.cancel,i,t.onGestureEnd,n)),e.$wrapperEl.on(e.touchEvents.move,`.${e.params.zoom.containerClass}`,t.onTouchMove,r)},disable(){const e=this,t=e.zoom;if(!t.enabled)return;e.zoom.enabled=!1;const n=!("touchstart"!==e.touchEvents.start||!Z.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1},r=!Z.passiveListener||{passive:!1,capture:!0},i=`.${e.params.slideClass}`;Z.gestures?(e.$wrapperEl.off("gesturestart",i,t.onGestureStart,n),e.$wrapperEl.off("gesturechange",i,t.onGestureChange,n),e.$wrapperEl.off("gestureend",i,t.onGestureEnd,n)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.off(e.touchEvents.start,i,t.onGestureStart,n),e.$wrapperEl.off(e.touchEvents.move,i,t.onGestureChange,r),e.$wrapperEl.off(e.touchEvents.end,i,t.onGestureEnd,n),e.touchEvents.cancel&&e.$wrapperEl.off(e.touchEvents.cancel,i,t.onGestureEnd,n)),e.$wrapperEl.off(e.touchEvents.move,`.${e.params.zoom.containerClass}`,t.onTouchMove,r)}};var jt={name:"zoom",params:{zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}},create(){const e=this,t={enabled:!1,scale:1,currentScale:1,isScaling:!1,gesture:{$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},image:{isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},velocity:{x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0}};"onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out".split(" ").forEach(n=>{t[n]=Nt[n].bind(e)}),J.extend(e,{zoom:t});let n=1;Object.defineProperty(e.zoom,"scale",{get(){return n},set(t){if(n!==t){const n=e.zoom.gesture.$imageEl?e.zoom.gesture.$imageEl[0]:void 0,r=e.zoom.gesture.$slideEl?e.zoom.gesture.$slideEl[0]:void 0;e.emit("zoomChange",t,n,r)}n=t}})},on:{init(){const e=this;e.params.zoom.enabled&&e.zoom.enable()},destroy(){const e=this;e.zoom.disable()},touchStart(e){const t=this;t.zoom.enabled&&t.zoom.onTouchStart(e)},touchEnd(e){const t=this;t.zoom.enabled&&t.zoom.onTouchEnd(e)},doubleTap(e){const t=this;t.params.zoom.enabled&&t.zoom.enabled&&t.params.zoom.toggle&&t.zoom.toggle(e)},transitionEnd(){const e=this;e.zoom.enabled&&e.params.zoom.enabled&&e.zoom.onTransitionEnd()},slideChange(){const e=this;e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&e.zoom.onTransitionEnd()}}};const Dt={loadInSlide(e,t=!0){const n=this,r=n.params.lazy;if("undefined"===typeof e)return;if(0===n.slides.length)return;const i=n.virtual&&n.params.virtual.enabled,s=i?n.$wrapperEl.children(`.${n.params.slideClass}[data-swiper-slide-index="${e}"]`):n.slides.eq(e);let o=s.find(`.${r.elementClass}:not(.${r.loadedClass}):not(.${r.loadingClass})`);!s.hasClass(r.elementClass)||s.hasClass(r.loadedClass)||s.hasClass(r.loadingClass)||(o=o.add(s[0])),0!==o.length&&o.each((e,i)=>{const o=u(i);o.addClass(r.loadingClass);const a=o.attr("data-background"),l=o.attr("data-src"),c=o.attr("data-srcset"),d=o.attr("data-sizes"),p=o.parent("picture");n.loadImage(o[0],l||a,c,d,!1,()=>{if("undefined"!==typeof n&&null!==n&&n&&(!n||n.params)&&!n.destroyed){if(a?(o.css("background-image",`url("${a}")`),o.removeAttr("data-background")):(c&&(o.attr("srcset",c),o.removeAttr("data-srcset")),d&&(o.attr("sizes",d),o.removeAttr("data-sizes")),p.length&&p.children("source").each((e,t)=>{const n=u(t);n.attr("data-srcset")&&(n.attr("srcset",n.attr("data-srcset")),n.removeAttr("data-srcset"))}),l&&(o.attr("src",l),o.removeAttr("data-src"))),o.addClass(r.loadedClass).removeClass(r.loadingClass),s.find(`.${r.preloaderClass}`).remove(),n.params.loop&&t){const e=s.attr("data-swiper-slide-index");if(s.hasClass(n.params.slideDuplicateClass)){const t=n.$wrapperEl.children(`[data-swiper-slide-index="${e}"]:not(.${n.params.slideDuplicateClass})`);n.lazy.loadInSlide(t.index(),!1)}else{const t=n.$wrapperEl.children(`.${n.params.slideDuplicateClass}[data-swiper-slide-index="${e}"]`);n.lazy.loadInSlide(t.index(),!1)}}n.emit("lazyImageReady",s[0],o[0]),n.params.autoHeight&&n.updateAutoHeight()}}),n.emit("lazyImageLoad",s[0],o[0])})},load(){const e=this,{$wrapperEl:t,params:n,slides:r,activeIndex:i}=e,s=e.virtual&&n.virtual.enabled,o=n.lazy;let a=n.slidesPerView;function l(e){if(s){if(t.children(`.${n.slideClass}[data-swiper-slide-index="${e}"]`).length)return!0}else if(r[e])return!0;return!1}function c(e){return s?u(e).attr("data-swiper-slide-index"):u(e).index()}if("auto"===a&&(a=0),e.lazy.initialImageLoaded||(e.lazy.initialImageLoaded=!0),e.params.watchSlidesVisibility)t.children(`.${n.slideVisibleClass}`).each((t,n)=>{const r=s?u(n).attr("data-swiper-slide-index"):u(n).index();e.lazy.loadInSlide(r)});else if(a>1)for(let u=i;u<i+a;u+=1)l(u)&&e.lazy.loadInSlide(u);else e.lazy.loadInSlide(i);if(o.loadPrevNext)if(a>1||o.loadPrevNextAmount&&o.loadPrevNextAmount>1){const t=o.loadPrevNextAmount,n=a,s=Math.min(i+n+Math.max(t,n),r.length),c=Math.max(i-Math.max(n,t),0);for(let r=i+a;r<s;r+=1)l(r)&&e.lazy.loadInSlide(r);for(let r=c;r<i;r+=1)l(r)&&e.lazy.loadInSlide(r)}else{const r=t.children(`.${n.slideNextClass}`);r.length>0&&e.lazy.loadInSlide(c(r));const i=t.children(`.${n.slidePrevClass}`);i.length>0&&e.lazy.loadInSlide(c(i))}}};var zt={name:"lazy",params:{lazy:{enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}},create(){const e=this;J.extend(e,{lazy:{initialImageLoaded:!1,load:Dt.load.bind(e),loadInSlide:Dt.loadInSlide.bind(e)}})},on:{beforeInit(){const e=this;e.params.lazy.enabled&&e.params.preloadImages&&(e.params.preloadImages=!1)},init(){const e=this;e.params.lazy.enabled&&!e.params.loop&&0===e.params.initialSlide&&e.lazy.load()},scroll(){const e=this;e.params.freeMode&&!e.params.freeModeSticky&&e.lazy.load()},resize(){const e=this;e.params.lazy.enabled&&e.lazy.load()},scrollbarDragMove(){const e=this;e.params.lazy.enabled&&e.lazy.load()},transitionStart(){const e=this;e.params.lazy.enabled&&(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&&!e.lazy.initialImageLoaded)&&e.lazy.load()},transitionEnd(){const e=this;e.params.lazy.enabled&&!e.params.lazy.loadOnTransitionStart&&e.lazy.load()},slideChange(){const e=this;e.params.lazy.enabled&&e.params.cssMode&&e.lazy.load()}}};const Ft={LinearSpline:function(e,t){const n=function(){let e,t,n;return(r,i)=>{t=-1,e=r.length;while(e-t>1)n=e+t>>1,r[n]<=i?t=n:e=n;return e}}();let r,i;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=n(this.x,e),r=i-1,(e-this.x[r])*(this.y[i]-this.y[r])/(this.x[i]-this.x[r])+this.y[r]):0},this},getInterpolateFunction(e){const t=this;t.controller.spline||(t.controller.spline=t.params.loop?new Ft.LinearSpline(t.slidesGrid,e.slidesGrid):new Ft.LinearSpline(t.snapGrid,e.snapGrid))},setTranslate(e,t){const n=this,r=n.controller.control;let i,s;function o(e){const t=n.rtlTranslate?-n.translate:n.translate;"slide"===n.params.controller.by&&(n.controller.getInterpolateFunction(e),s=-n.controller.spline.interpolate(-t)),s&&"container"!==n.params.controller.by||(i=(e.maxTranslate()-e.minTranslate())/(n.maxTranslate()-n.minTranslate()),s=(t-n.minTranslate())*i+e.minTranslate()),n.params.controller.inverse&&(s=e.maxTranslate()-s),e.updateProgress(s),e.setTranslate(s,n),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(r))for(let a=0;a<r.length;a+=1)r[a]!==t&&r[a]instanceof ht&&o(r[a]);else r instanceof ht&&t!==r&&o(r)},setTransition(e,t){const n=this,r=n.controller.control;let i;function s(t){t.setTransition(e,n),0!==e&&(t.transitionStart(),t.params.autoHeight&&J.nextTick(()=>{t.updateAutoHeight()}),t.$wrapperEl.transitionEnd(()=>{r&&(t.params.loop&&"slide"===n.params.controller.by&&t.loopFix(),t.transitionEnd())}))}if(Array.isArray(r))for(i=0;i<r.length;i+=1)r[i]!==t&&r[i]instanceof ht&&s(r[i]);else r instanceof ht&&t!==r&&s(r)}};var Bt={name:"controller",params:{controller:{control:void 0,inverse:!1,by:"slide"}},create(){const e=this;J.extend(e,{controller:{control:e.params.controller.control,getInterpolateFunction:Ft.getInterpolateFunction.bind(e),setTranslate:Ft.setTranslate.bind(e),setTransition:Ft.setTransition.bind(e)}})},on:{update(){const e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},resize(){const e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},observerUpdate(){const e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},setTranslate(e,t){const n=this;n.controller.control&&n.controller.setTranslate(e,t)},setTransition(e,t){const n=this;n.controller.control&&n.controller.setTransition(e,t)}}};const Ut={makeElFocusable(e){return e.attr("tabIndex","0"),e},makeElNotFocusable(e){return e.attr("tabIndex","-1"),e},addElRole(e,t){return e.attr("role",t),e},addElLabel(e,t){return e.attr("aria-label",t),e},disableEl(e){return e.attr("aria-disabled",!0),e},enableEl(e){return e.attr("aria-disabled",!1),e},onEnterKey(e){const t=this,n=t.params.a11y;if(13!==e.keyCode)return;const r=u(e.target);t.navigation&&t.navigation.$nextEl&&r.is(t.navigation.$nextEl)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?t.a11y.notify(n.lastSlideMessage):t.a11y.notify(n.nextSlideMessage)),t.navigation&&t.navigation.$prevEl&&r.is(t.navigation.$prevEl)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?t.a11y.notify(n.firstSlideMessage):t.a11y.notify(n.prevSlideMessage)),t.pagination&&r.is(`.${t.params.pagination.bulletClass}`)&&r[0].click()},notify(e){const t=this,n=t.a11y.liveRegion;0!==n.length&&(n.html(""),n.html(e))},updateNavigation(){const e=this;if(e.params.loop||!e.navigation)return;const{$nextEl:t,$prevEl:n}=e.navigation;n&&n.length>0&&(e.isBeginning?(e.a11y.disableEl(n),e.a11y.makeElNotFocusable(n)):(e.a11y.enableEl(n),e.a11y.makeElFocusable(n))),t&&t.length>0&&(e.isEnd?(e.a11y.disableEl(t),e.a11y.makeElNotFocusable(t)):(e.a11y.enableEl(t),e.a11y.makeElFocusable(t)))},updatePagination(){const e=this,t=e.params.a11y;e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.bullets.each((n,r)=>{const i=u(r);e.a11y.makeElFocusable(i),e.a11y.addElRole(i,"button"),e.a11y.addElLabel(i,t.paginationBulletMessage.replace(/\{\{index\}\}/,i.index()+1))})},init(){const e=this;e.$el.append(e.a11y.liveRegion);const t=e.params.a11y;let n,r;e.navigation&&e.navigation.$nextEl&&(n=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(r=e.navigation.$prevEl),n&&(e.a11y.makeElFocusable(n),e.a11y.addElRole(n,"button"),e.a11y.addElLabel(n,t.nextSlideMessage),n.on("keydown",e.a11y.onEnterKey)),r&&(e.a11y.makeElFocusable(r),e.a11y.addElRole(r,"button"),e.a11y.addElLabel(r,t.prevSlideMessage),r.on("keydown",e.a11y.onEnterKey)),e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.$el.on("keydown",`.${e.params.pagination.bulletClass}`,e.a11y.onEnterKey)},destroy(){const e=this;let t,n;e.a11y.liveRegion&&e.a11y.liveRegion.length>0&&e.a11y.liveRegion.remove(),e.navigation&&e.navigation.$nextEl&&(t=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(n=e.navigation.$prevEl),t&&t.off("keydown",e.a11y.onEnterKey),n&&n.off("keydown",e.a11y.onEnterKey),e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.$el.off("keydown",`.${e.params.pagination.bulletClass}`,e.a11y.onEnterKey)}};var qt={name:"a11y",params:{a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}"}},create(){const e=this;J.extend(e,{a11y:{liveRegion:u(`<span class="${e.params.a11y.notificationClass}" aria-live="assertive" aria-atomic="true"></span>`)}}),Object.keys(Ut).forEach(t=>{e.a11y[t]=Ut[t].bind(e)})},on:{init(){const e=this;e.params.a11y.enabled&&(e.a11y.init(),e.a11y.updateNavigation())},toEdge(){const e=this;e.params.a11y.enabled&&e.a11y.updateNavigation()},fromEdge(){const e=this;e.params.a11y.enabled&&e.a11y.updateNavigation()},paginationUpdate(){const e=this;e.params.a11y.enabled&&e.a11y.updatePagination()},destroy(){const e=this;e.params.a11y.enabled&&e.a11y.destroy()}}};const Ht={init(){const e=this;if(!e.params.history)return;if(!a.history||!a.history.pushState)return e.params.history.enabled=!1,void(e.params.hashNavigation.enabled=!0);const t=e.history;t.initialized=!0,t.paths=Ht.getPathValues(),(t.paths.key||t.paths.value)&&(t.scrollToSlide(0,t.paths.value,e.params.runCallbacksOnInit),e.params.history.replaceState||a.addEventListener("popstate",e.history.setHistoryPopState))},destroy(){const e=this;e.params.history.replaceState||a.removeEventListener("popstate",e.history.setHistoryPopState)},setHistoryPopState(){const e=this;e.history.paths=Ht.getPathValues(),e.history.scrollToSlide(e.params.speed,e.history.paths.value,!1)},getPathValues(){const e=a.location.pathname.slice(1).split("/").filter(e=>""!==e),t=e.length,n=e[t-2],r=e[t-1];return{key:n,value:r}},setHistory(e,t){const n=this;if(!n.history.initialized||!n.params.history.enabled)return;const r=n.slides.eq(t);let i=Ht.slugify(r.attr("data-history"));a.location.pathname.includes(e)||(i=`${e}/${i}`);const s=a.history.state;s&&s.value===i||(n.params.history.replaceState?a.history.replaceState({value:i},null,i):a.history.pushState({value:i},null,i))},slugify(e){return e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,"")},scrollToSlide(e,t,n){const r=this;if(t)for(let i=0,s=r.slides.length;i<s;i+=1){const s=r.slides.eq(i),o=Ht.slugify(s.attr("data-history"));if(o===t&&!s.hasClass(r.params.slideDuplicateClass)){const t=s.index();r.slideTo(t,e,n)}}else r.slideTo(0,e,n)}};var Gt={name:"history",params:{history:{enabled:!1,replaceState:!1,key:"slides"}},create(){const e=this;J.extend(e,{history:{init:Ht.init.bind(e),setHistory:Ht.setHistory.bind(e),setHistoryPopState:Ht.setHistoryPopState.bind(e),scrollToSlide:Ht.scrollToSlide.bind(e),destroy:Ht.destroy.bind(e)}})},on:{init(){const e=this;e.params.history.enabled&&e.history.init()},destroy(){const e=this;e.params.history.enabled&&e.history.destroy()},transitionEnd(){const e=this;e.history.initialized&&e.history.setHistory(e.params.history.key,e.activeIndex)},slideChange(){const e=this;e.history.initialized&&e.params.cssMode&&e.history.setHistory(e.params.history.key,e.activeIndex)}}};const Vt={onHashCange(){const e=this;e.emit("hashChange");const t=s.location.hash.replace("#",""),n=e.slides.eq(e.activeIndex).attr("data-hash");if(t!==n){const n=e.$wrapperEl.children(`.${e.params.slideClass}[data-hash="${t}"]`).index();if("undefined"===typeof n)return;e.slideTo(n)}},setHash(){const e=this;if(e.hashNavigation.initialized&&e.params.hashNavigation.enabled)if(e.params.hashNavigation.replaceState&&a.history&&a.history.replaceState)a.history.replaceState(null,null,`#${e.slides.eq(e.activeIndex).attr("data-hash")}`||""),e.emit("hashSet");else{const t=e.slides.eq(e.activeIndex),n=t.attr("data-hash")||t.attr("data-history");s.location.hash=n||"",e.emit("hashSet")}},init(){const e=this;if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;e.hashNavigation.initialized=!0;const t=s.location.hash.replace("#","");if(t){const n=0;for(let r=0,i=e.slides.length;r<i;r+=1){const i=e.slides.eq(r),s=i.attr("data-hash")||i.attr("data-history");if(s===t&&!i.hasClass(e.params.slideDuplicateClass)){const t=i.index();e.slideTo(t,n,e.params.runCallbacksOnInit,!0)}}}e.params.hashNavigation.watchState&&u(a).on("hashchange",e.hashNavigation.onHashCange)},destroy(){const e=this;e.params.hashNavigation.watchState&&u(a).off("hashchange",e.hashNavigation.onHashCange)}};var Wt={name:"hash-navigation",params:{hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}},create(){const e=this;J.extend(e,{hashNavigation:{initialized:!1,init:Vt.init.bind(e),destroy:Vt.destroy.bind(e),setHash:Vt.setHash.bind(e),onHashCange:Vt.onHashCange.bind(e)}})},on:{init(){const e=this;e.params.hashNavigation.enabled&&e.hashNavigation.init()},destroy(){const e=this;e.params.hashNavigation.enabled&&e.hashNavigation.destroy()},transitionEnd(){const e=this;e.hashNavigation.initialized&&e.hashNavigation.setHash()},slideChange(){const e=this;e.hashNavigation.initialized&&e.params.cssMode&&e.hashNavigation.setHash()}}};const Yt={run(){const e=this,t=e.slides.eq(e.activeIndex);let n=e.params.autoplay.delay;t.attr("data-swiper-autoplay")&&(n=t.attr("data-swiper-autoplay")||e.params.autoplay.delay),clearTimeout(e.autoplay.timeout),e.autoplay.timeout=J.nextTick(()=>{e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.isBeginning?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(e.slideTo(e.slides.length-1,e.params.speed,!0,!0),e.emit("autoplay")):(e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.params.loop?(e.loopFix(),e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")):e.isEnd?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(e.slideTo(0,e.params.speed,!0,!0),e.emit("autoplay")):(e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")),e.params.cssMode&&e.autoplay.running&&e.autoplay.run()},n)},start(){const e=this;return"undefined"===typeof e.autoplay.timeout&&(!e.autoplay.running&&(e.autoplay.running=!0,e.emit("autoplayStart"),e.autoplay.run(),!0))},stop(){const e=this;return!!e.autoplay.running&&("undefined"!==typeof e.autoplay.timeout&&(e.autoplay.timeout&&(clearTimeout(e.autoplay.timeout),e.autoplay.timeout=void 0),e.autoplay.running=!1,e.emit("autoplayStop"),!0))},pause(e){const t=this;t.autoplay.running&&(t.autoplay.paused||(t.autoplay.timeout&&clearTimeout(t.autoplay.timeout),t.autoplay.paused=!0,0!==e&&t.params.autoplay.waitForTransition?(t.$wrapperEl[0].addEventListener("transitionend",t.autoplay.onTransitionEnd),t.$wrapperEl[0].addEventListener("webkitTransitionEnd",t.autoplay.onTransitionEnd)):(t.autoplay.paused=!1,t.autoplay.run())))}};var Xt={name:"autoplay",params:{autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1}},create(){const e=this;J.extend(e,{autoplay:{running:!1,paused:!1,run:Yt.run.bind(e),start:Yt.start.bind(e),stop:Yt.stop.bind(e),pause:Yt.pause.bind(e),onVisibilityChange(){"hidden"===document.visibilityState&&e.autoplay.running&&e.autoplay.pause(),"visible"===document.visibilityState&&e.autoplay.paused&&(e.autoplay.run(),e.autoplay.paused=!1)},onTransitionEnd(t){e&&!e.destroyed&&e.$wrapperEl&&t.target===this&&(e.$wrapperEl[0].removeEventListener("transitionend",e.autoplay.onTransitionEnd),e.$wrapperEl[0].removeEventListener("webkitTransitionEnd",e.autoplay.onTransitionEnd),e.autoplay.paused=!1,e.autoplay.running?e.autoplay.run():e.autoplay.stop())}}})},on:{init(){const e=this;e.params.autoplay.enabled&&(e.autoplay.start(),document.addEventListener("visibilitychange",e.autoplay.onVisibilityChange))},beforeTransitionStart(e,t){const n=this;n.autoplay.running&&(t||!n.params.autoplay.disableOnInteraction?n.autoplay.pause(e):n.autoplay.stop())},sliderFirstMove(){const e=this;e.autoplay.running&&(e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause())},touchEnd(){const e=this;e.params.cssMode&&e.autoplay.paused&&!e.params.autoplay.disableOnInteraction&&e.autoplay.run()},destroy(){const e=this;e.autoplay.running&&e.autoplay.stop(),document.removeEventListener("visibilitychange",e.autoplay.onVisibilityChange)}}};const Kt={setTranslate(){const e=this,{slides:t}=e;for(let n=0;n<t.length;n+=1){const t=e.slides.eq(n),r=t[0].swiperSlideOffset;let i=-r;e.params.virtualTranslate||(i-=e.translate);let s=0;e.isHorizontal()||(s=i,i=0);const o=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(t[0].progress),0):1+Math.min(Math.max(t[0].progress,-1),0);t.css({opacity:o}).transform(`translate3d(${i}px, ${s}px, 0px)`)}},setTransition(e){const t=this,{slides:n,$wrapperEl:r}=t;if(n.transition(e),t.params.virtualTranslate&&0!==e){let e=!1;n.transitionEnd(()=>{if(e)return;if(!t||t.destroyed)return;e=!0,t.animating=!1;const n=["webkitTransitionEnd","transitionend"];for(let e=0;e<n.length;e+=1)r.trigger(n[e])})}}};var Jt={name:"effect-fade",params:{fadeEffect:{crossFade:!1}},create(){const e=this;J.extend(e,{fadeEffect:{setTranslate:Kt.setTranslate.bind(e),setTransition:Kt.setTransition.bind(e)}})},on:{beforeInit(){const e=this;if("fade"!==e.params.effect)return;e.classNames.push(`${e.params.containerModifierClass}fade`);const t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};J.extend(e.params,t),J.extend(e.originalParams,t)},setTranslate(){const e=this;"fade"===e.params.effect&&e.fadeEffect.setTranslate()},setTransition(e){const t=this;"fade"===t.params.effect&&t.fadeEffect.setTransition(e)}}};const Zt={setTranslate(){const e=this,{$el:t,$wrapperEl:n,slides:r,width:i,height:s,rtlTranslate:o,size:a}=e,l=e.params.cubeEffect,c=e.isHorizontal(),d=e.virtual&&e.params.virtual.enabled;let p,h=0;l.shadow&&(c?(p=n.find(".swiper-cube-shadow"),0===p.length&&(p=u('<div class="swiper-cube-shadow"></div>'),n.append(p)),p.css({height:`${i}px`})):(p=t.find(".swiper-cube-shadow"),0===p.length&&(p=u('<div class="swiper-cube-shadow"></div>'),t.append(p))));for(let m=0;m<r.length;m+=1){const e=r.eq(m);let t=m;d&&(t=parseInt(e.attr("data-swiper-slide-index"),10));let n=90*t,i=Math.floor(n/360);o&&(n=-n,i=Math.floor(-n/360));const s=Math.max(Math.min(e[0].progress,1),-1);let p=0,f=0,g=0;t%4===0?(p=4*-i*a,g=0):(t-1)%4===0?(p=0,g=4*-i*a):(t-2)%4===0?(p=a+4*i*a,g=a):(t-3)%4===0&&(p=-a,g=3*a+4*a*i),o&&(p=-p),c||(f=p,p=0);const v=`rotateX(${c?0:-n}deg) rotateY(${c?n:0}deg) translate3d(${p}px, ${f}px, ${g}px)`;if(s<=1&&s>-1&&(h=90*t+90*s,o&&(h=90*-t-90*s)),e.transform(v),l.slideShadows){let t=c?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),n=c?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===t.length&&(t=u(`<div class="swiper-slide-shadow-${c?"left":"top"}"></div>`),e.append(t)),0===n.length&&(n=u(`<div class="swiper-slide-shadow-${c?"right":"bottom"}"></div>`),e.append(n)),t.length&&(t[0].style.opacity=Math.max(-s,0)),n.length&&(n[0].style.opacity=Math.max(s,0))}}if(n.css({"-webkit-transform-origin":`50% 50% -${a/2}px`,"-moz-transform-origin":`50% 50% -${a/2}px`,"-ms-transform-origin":`50% 50% -${a/2}px`,"transform-origin":`50% 50% -${a/2}px`}),l.shadow)if(c)p.transform(`translate3d(0px, ${i/2+l.shadowOffset}px, ${-i/2}px) rotateX(90deg) rotateZ(0deg) scale(${l.shadowScale})`);else{const e=Math.abs(h)-90*Math.floor(Math.abs(h)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),n=l.shadowScale,r=l.shadowScale/t,i=l.shadowOffset;p.transform(`scale3d(${n}, 1, ${r}) translate3d(0px, ${s/2+i}px, ${-s/2/r}px) rotateX(-90deg)`)}const f=gt.isSafari||gt.isWebView?-a/2:0;n.transform(`translate3d(0px,0,${f}px) rotateX(${e.isHorizontal()?0:h}deg) rotateY(${e.isHorizontal()?-h:0}deg)`)},setTransition(e){const t=this,{$el:n,slides:r}=t;r.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.cubeEffect.shadow&&!t.isHorizontal()&&n.find(".swiper-cube-shadow").transition(e)}};var Qt={name:"effect-cube",params:{cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}},create(){const e=this;J.extend(e,{cubeEffect:{setTranslate:Zt.setTranslate.bind(e),setTransition:Zt.setTransition.bind(e)}})},on:{beforeInit(){const e=this;if("cube"!==e.params.effect)return;e.classNames.push(`${e.params.containerModifierClass}cube`),e.classNames.push(`${e.params.containerModifierClass}3d`);const t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0};J.extend(e.params,t),J.extend(e.originalParams,t)},setTranslate(){const e=this;"cube"===e.params.effect&&e.cubeEffect.setTranslate()},setTransition(e){const t=this;"cube"===t.params.effect&&t.cubeEffect.setTransition(e)}}};const en={setTranslate(){const e=this,{slides:t,rtlTranslate:n}=e;for(let r=0;r<t.length;r+=1){const i=t.eq(r);let s=i[0].progress;e.params.flipEffect.limitRotation&&(s=Math.max(Math.min(i[0].progress,1),-1));const o=i[0].swiperSlideOffset,a=-180*s;let l=a,c=0,d=-o,p=0;if(e.isHorizontal()?n&&(l=-l):(p=d,d=0,c=-l,l=0),i[0].style.zIndex=-Math.abs(Math.round(s))+t.length,e.params.flipEffect.slideShadows){let t=e.isHorizontal()?i.find(".swiper-slide-shadow-left"):i.find(".swiper-slide-shadow-top"),n=e.isHorizontal()?i.find(".swiper-slide-shadow-right"):i.find(".swiper-slide-shadow-bottom");0===t.length&&(t=u(`<div class="swiper-slide-shadow-${e.isHorizontal()?"left":"top"}"></div>`),i.append(t)),0===n.length&&(n=u(`<div class="swiper-slide-shadow-${e.isHorizontal()?"right":"bottom"}"></div>`),i.append(n)),t.length&&(t[0].style.opacity=Math.max(-s,0)),n.length&&(n[0].style.opacity=Math.max(s,0))}i.transform(`translate3d(${d}px, ${p}px, 0px) rotateX(${c}deg) rotateY(${l}deg)`)}},setTransition(e){const t=this,{slides:n,activeIndex:r,$wrapperEl:i}=t;if(n.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.virtualTranslate&&0!==e){let e=!1;n.eq(r).transitionEnd(function(){if(e)return;if(!t||t.destroyed)return;e=!0,t.animating=!1;const n=["webkitTransitionEnd","transitionend"];for(let e=0;e<n.length;e+=1)i.trigger(n[e])})}}};var tn={name:"effect-flip",params:{flipEffect:{slideShadows:!0,limitRotation:!0}},create(){const e=this;J.extend(e,{flipEffect:{setTranslate:en.setTranslate.bind(e),setTransition:en.setTransition.bind(e)}})},on:{beforeInit(){const e=this;if("flip"!==e.params.effect)return;e.classNames.push(`${e.params.containerModifierClass}flip`),e.classNames.push(`${e.params.containerModifierClass}3d`);const t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};J.extend(e.params,t),J.extend(e.originalParams,t)},setTranslate(){const e=this;"flip"===e.params.effect&&e.flipEffect.setTranslate()},setTransition(e){const t=this;"flip"===t.params.effect&&t.flipEffect.setTransition(e)}}};const nn={setTranslate(){const e=this,{width:t,height:n,slides:r,$wrapperEl:i,slidesSizesGrid:s}=e,o=e.params.coverflowEffect,a=e.isHorizontal(),l=e.translate,c=a?t/2-l:n/2-l,d=a?o.rotate:-o.rotate,p=o.depth;for(let h=0,f=r.length;h<f;h+=1){const e=r.eq(h),t=s[h],n=e[0].swiperSlideOffset,i=(c-n-t/2)/t*o.modifier;let l=a?d*i:0,f=a?0:d*i,m=-p*Math.abs(i),g=o.stretch;"string"===typeof g&&-1!==g.indexOf("%")&&(g=parseFloat(o.stretch)/100*t);let v=a?0:g*i,y=a?g*i:0,b=1-(1-o.scale)*Math.abs(i);Math.abs(y)<.001&&(y=0),Math.abs(v)<.001&&(v=0),Math.abs(m)<.001&&(m=0),Math.abs(l)<.001&&(l=0),Math.abs(f)<.001&&(f=0),Math.abs(b)<.001&&(b=0);const w=`translate3d(${y}px,${v}px,${m}px)  rotateX(${f}deg) rotateY(${l}deg) scale(${b})`;if(e.transform(w),e[0].style.zIndex=1-Math.abs(Math.round(i)),o.slideShadows){let t=a?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),n=a?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===t.length&&(t=u(`<div class="swiper-slide-shadow-${a?"left":"top"}"></div>`),e.append(t)),0===n.length&&(n=u(`<div class="swiper-slide-shadow-${a?"right":"bottom"}"></div>`),e.append(n)),t.length&&(t[0].style.opacity=i>0?i:0),n.length&&(n[0].style.opacity=-i>0?-i:0)}}if(Z.pointerEvents||Z.prefixedPointerEvents){const e=i[0].style;e.perspectiveOrigin=`${c}px 50%`}},setTransition(e){const t=this;t.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)}};var rn={name:"effect-coverflow",params:{coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}},create(){const e=this;J.extend(e,{coverflowEffect:{setTranslate:nn.setTranslate.bind(e),setTransition:nn.setTransition.bind(e)}})},on:{beforeInit(){const e=this;"coverflow"===e.params.effect&&(e.classNames.push(`${e.params.containerModifierClass}coverflow`),e.classNames.push(`${e.params.containerModifierClass}3d`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},setTranslate(){const e=this;"coverflow"===e.params.effect&&e.coverflowEffect.setTranslate()},setTransition(e){const t=this;"coverflow"===t.params.effect&&t.coverflowEffect.setTransition(e)}}};const sn={init(){const e=this,{thumbs:t}=e.params,n=e.constructor;t.swiper instanceof n?(e.thumbs.swiper=t.swiper,J.extend(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),J.extend(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1})):J.isObject(t.swiper)&&(e.thumbs.swiper=new n(J.extend({},t.swiper,{watchSlidesVisibility:!0,watchSlidesProgress:!0,slideToClickedSlide:!1})),e.thumbs.swiperCreated=!0),e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",e.thumbs.onThumbClick)},onThumbClick(){const e=this,t=e.thumbs.swiper;if(!t)return;const n=t.clickedIndex,r=t.clickedSlide;if(r&&u(r).hasClass(e.params.thumbs.slideThumbActiveClass))return;if("undefined"===typeof n||null===n)return;let i;if(i=t.params.loop?parseInt(u(t.clickedSlide).attr("data-swiper-slide-index"),10):n,e.params.loop){let t=e.activeIndex;e.slides.eq(t).hasClass(e.params.slideDuplicateClass)&&(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,t=e.activeIndex);const n=e.slides.eq(t).prevAll(`[data-swiper-slide-index="${i}"]`).eq(0).index(),r=e.slides.eq(t).nextAll(`[data-swiper-slide-index="${i}"]`).eq(0).index();i="undefined"===typeof n?r:"undefined"===typeof r?n:r-t<t-n?r:n}e.slideTo(i)},update(e){const t=this,n=t.thumbs.swiper;if(!n)return;const r="auto"===n.params.slidesPerView?n.slidesPerViewDynamic():n.params.slidesPerView,i=t.params.thumbs.autoScrollOffset,s=i&&!n.params.loop;if(t.realIndex!==n.realIndex||s){let o,a,l=n.activeIndex;if(n.params.loop){n.slides.eq(l).hasClass(n.params.slideDuplicateClass)&&(n.loopFix(),n._clientLeft=n.$wrapperEl[0].clientLeft,l=n.activeIndex);const e=n.slides.eq(l).prevAll(`[data-swiper-slide-index="${t.realIndex}"]`).eq(0).index(),r=n.slides.eq(l).nextAll(`[data-swiper-slide-index="${t.realIndex}"]`).eq(0).index();o="undefined"===typeof e?r:"undefined"===typeof r?e:r-l===l-e?l:r-l<l-e?r:e,a=t.activeIndex>t.previousIndex?"next":"prev"}else o=t.realIndex,a=o>t.previousIndex?"next":"prev";s&&(o+="next"===a?i:-1*i),n.visibleSlidesIndexes&&n.visibleSlidesIndexes.indexOf(o)<0&&(n.params.centeredSlides?o=o>l?o-Math.floor(r/2)+1:o+Math.floor(r/2)-1:o>l&&(o=o-r+1),n.slideTo(o,e?0:void 0))}let o=1;const a=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(o=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(o=1),o=Math.floor(o),n.slides.removeClass(a),n.params.loop||n.params.virtual&&n.params.virtual.enabled)for(let l=0;l<o;l+=1)n.$wrapperEl.children(`[data-swiper-slide-index="${t.realIndex+l}"]`).addClass(a);else for(let l=0;l<o;l+=1)n.slides.eq(t.realIndex+l).addClass(a)}};var on={name:"thumbs",params:{thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-container-thumbs"}},create(){const e=this;J.extend(e,{thumbs:{swiper:null,init:sn.init.bind(e),update:sn.update.bind(e),onThumbClick:sn.onThumbClick.bind(e)}})},on:{beforeInit(){const e=this,{thumbs:t}=e.params;t&&t.swiper&&(e.thumbs.init(),e.thumbs.update(!0))},slideChange(){const e=this;e.thumbs.swiper&&e.thumbs.update()},update(){const e=this;e.thumbs.swiper&&e.thumbs.update()},resize(){const e=this;e.thumbs.swiper&&e.thumbs.update()},observerUpdate(){const e=this;e.thumbs.swiper&&e.thumbs.update()},setTransition(e){const t=this,n=t.thumbs.swiper;n&&n.setTransition(e)},beforeDestroy(){const e=this,t=e.thumbs.swiper;t&&e.thumbs.swiperCreated&&t&&t.destroy()}}};const an=[ft,mt,vt,yt,wt,Et,Ct,_t,$t,Pt,It,Mt,jt,zt,Bt,qt,Gt,Wt,Xt,Jt,Qt,tn,rn,on];"undefined"===typeof ht.use&&(ht.use=ht.Class.use,ht.installModule=ht.Class.installModule),ht.use(an);const ln=ht},5353:(e,t,n)=>{"use strict";
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
function r(e){var t=Number(e.version.split(".")[0]);if(t>=2)e.mixin({beforeCreate:r});else{var n=e.prototype._init;e.prototype._init=function(e){void 0===e&&(e={}),e.init=e.init?[r].concat(e.init):r,n.call(this,e)}}function r(){var e=this.$options;e.store?this.$store="function"===typeof e.store?e.store():e.store:e.parent&&e.parent.$store&&(this.$store=e.parent.$store)}}n.d(t,{Ay:()=>X,L8:()=>M});var i="undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{},s=i.__VUE_DEVTOOLS_GLOBAL_HOOK__;function o(e){s&&(e._devtoolHook=s,s.emit("vuex:init",e),s.on("vuex:travel-to-state",function(t){e.replaceState(t)}),e.subscribe(function(e,t){s.emit("vuex:mutation",e,t)},{prepend:!0}),e.subscribeAction(function(e,t){s.emit("vuex:action",e,t)},{prepend:!0}))}function a(e,t){return e.filter(t)[0]}function l(e,t){if(void 0===t&&(t=[]),null===e||"object"!==typeof e)return e;var n=a(t,function(t){return t.original===e});if(n)return n.copy;var r=Array.isArray(e)?[]:{};return t.push({original:e,copy:r}),Object.keys(e).forEach(function(n){r[n]=l(e[n],t)}),r}function c(e,t){Object.keys(e).forEach(function(n){return t(e[n],n)})}function u(e){return null!==e&&"object"===typeof e}function d(e){return e&&"function"===typeof e.then}function p(e,t){return function(){return e(t)}}var h=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"===typeof n?n():n)||{}},f={namespaced:{configurable:!0}};f.namespaced.get=function(){return!!this._rawModule.namespaced},h.prototype.addChild=function(e,t){this._children[e]=t},h.prototype.removeChild=function(e){delete this._children[e]},h.prototype.getChild=function(e){return this._children[e]},h.prototype.hasChild=function(e){return e in this._children},h.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},h.prototype.forEachChild=function(e){c(this._children,e)},h.prototype.forEachGetter=function(e){this._rawModule.getters&&c(this._rawModule.getters,e)},h.prototype.forEachAction=function(e){this._rawModule.actions&&c(this._rawModule.actions,e)},h.prototype.forEachMutation=function(e){this._rawModule.mutations&&c(this._rawModule.mutations,e)},Object.defineProperties(h.prototype,f);var m=function(e){this.register([],e,!1)};function g(e,t,n){if(t.update(n),n.modules)for(var r in n.modules){if(!t.getChild(r))return void 0;g(e.concat(r),t.getChild(r),n.modules[r])}}m.prototype.get=function(e){return e.reduce(function(e,t){return e.getChild(t)},this.root)},m.prototype.getNamespace=function(e){var t=this.root;return e.reduce(function(e,n){return t=t.getChild(n),e+(t.namespaced?n+"/":"")},"")},m.prototype.update=function(e){g([],this.root,e)},m.prototype.register=function(e,t,n){var r=this;void 0===n&&(n=!0);var i=new h(t,n);if(0===e.length)this.root=i;else{var s=this.get(e.slice(0,-1));s.addChild(e[e.length-1],i)}t.modules&&c(t.modules,function(t,i){r.register(e.concat(i),t,n)})},m.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],r=t.getChild(n);r&&r.runtime&&t.removeChild(n)},m.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var v;var y=function(e){var t=this;void 0===e&&(e={}),!v&&"undefined"!==typeof window&&window.Vue&&A(window.Vue);var n=e.plugins;void 0===n&&(n=[]);var r=e.strict;void 0===r&&(r=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new m(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new v,this._makeLocalGettersCache=Object.create(null);var i=this,s=this,a=s.dispatch,l=s.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return l.call(i,e,t,n)},this.strict=r;var c=this._modules.root.state;S(this,c,[],this._modules.root),E(this,c),n.forEach(function(e){return e(t)});var u=void 0!==e.devtools?e.devtools:v.config.devtools;u&&o(this)},b={state:{configurable:!0}};function w(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function x(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;S(e,n,[],e._modules.root,!0),E(e,n,t)}function E(e,t,n){var r=e._vm;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,s={};c(i,function(t,n){s[n]=p(t,e),Object.defineProperty(e.getters,n,{get:function(){return e._vm[n]},enumerable:!0})});var o=v.config.silent;v.config.silent=!0,e._vm=new v({data:{$$state:t},computed:s}),v.config.silent=o,e.strict&&$(e),r&&(n&&e._withCommit(function(){r._data.$$state=null}),v.nextTick(function(){return r.$destroy()}))}function S(e,t,n,r,i){var s=!n.length,o=e._modules.getNamespace(n);if(r.namespaced&&(e._modulesNamespaceMap[o],e._modulesNamespaceMap[o]=r),!s&&!i){var a=R(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit(function(){v.set(a,l,r.state)})}var c=r.context=C(e,o,n);r.forEachMutation(function(t,n){var r=o+n;k(e,r,t,c)}),r.forEachAction(function(t,n){var r=t.root?n:o+n,i=t.handler||t;_(e,r,i,c)}),r.forEachGetter(function(t,n){var r=o+n;O(e,r,t,c)}),r.forEachChild(function(r,s){S(e,t,n.concat(s),r,i)})}function C(e,t,n){var r=""===t,i={dispatch:r?e.dispatch:function(n,r,i){var s=P(n,r,i),o=s.payload,a=s.options,l=s.type;return a&&a.root||(l=t+l),e.dispatch(l,o)},commit:r?e.commit:function(n,r,i){var s=P(n,r,i),o=s.payload,a=s.options,l=s.type;a&&a.root||(l=t+l),e.commit(l,o,a)}};return Object.defineProperties(i,{getters:{get:r?function(){return e.getters}:function(){return T(e,t)}},state:{get:function(){return R(e.state,n)}}}),i}function T(e,t){if(!e._makeLocalGettersCache[t]){var n={},r=t.length;Object.keys(e.getters).forEach(function(i){if(i.slice(0,r)===t){var s=i.slice(r);Object.defineProperty(n,s,{get:function(){return e.getters[i]},enumerable:!0})}}),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function k(e,t,n,r){var i=e._mutations[t]||(e._mutations[t]=[]);i.push(function(t){n.call(e,r.state,t)})}function _(e,t,n,r){var i=e._actions[t]||(e._actions[t]=[]);i.push(function(t){var i=n.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},t);return d(i)||(i=Promise.resolve(i)),e._devtoolHook?i.catch(function(t){throw e._devtoolHook.emit("vuex:error",t),t}):i})}function O(e,t,n,r){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(e){return n(r.state,r.getters,e.state,e.getters)})}function $(e){e._vm.$watch(function(){return this._data.$$state},function(){0},{deep:!0,sync:!0})}function R(e,t){return t.reduce(function(e,t){return e[t]},e)}function P(e,t,n){return u(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}function A(e){v&&e===v||(v=e,r(v))}b.state.get=function(){return this._vm._data.$$state},b.state.set=function(e){0},y.prototype.commit=function(e,t,n){var r=this,i=P(e,t,n),s=i.type,o=i.payload,a=(i.options,{type:s,payload:o}),l=this._mutations[s];l&&(this._withCommit(function(){l.forEach(function(e){e(o)})}),this._subscribers.slice().forEach(function(e){return e(a,r.state)}))},y.prototype.dispatch=function(e,t){var n=this,r=P(e,t),i=r.type,s=r.payload,o={type:i,payload:s},a=this._actions[i];if(a){try{this._actionSubscribers.slice().filter(function(e){return e.before}).forEach(function(e){return e.before(o,n.state)})}catch(c){0}var l=a.length>1?Promise.all(a.map(function(e){return e(s)})):a[0](s);return new Promise(function(e,t){l.then(function(t){try{n._actionSubscribers.filter(function(e){return e.after}).forEach(function(e){return e.after(o,n.state)})}catch(c){0}e(t)},function(e){try{n._actionSubscribers.filter(function(e){return e.error}).forEach(function(t){return t.error(o,n.state,e)})}catch(c){0}t(e)})})}},y.prototype.subscribe=function(e,t){return w(e,this._subscribers,t)},y.prototype.subscribeAction=function(e,t){var n="function"===typeof e?{before:e}:e;return w(n,this._actionSubscribers,t)},y.prototype.watch=function(e,t,n){var r=this;return this._watcherVM.$watch(function(){return e(r.state,r.getters)},t,n)},y.prototype.replaceState=function(e){var t=this;this._withCommit(function(){t._vm._data.$$state=e})},y.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"===typeof e&&(e=[e]),this._modules.register(e,t),S(this,this.state,e,this._modules.get(e),n.preserveState),E(this,this.state)},y.prototype.unregisterModule=function(e){var t=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit(function(){var n=R(t.state,e.slice(0,-1));v.delete(n,e[e.length-1])}),x(this)},y.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},y.prototype.hotUpdate=function(e){this._modules.update(e),x(this,!0)},y.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(y.prototype,b);var I=F(function(e,t){var n={};return D(t).forEach(function(t){var r=t.key,i=t.val;n[r]=function(){var t=this.$store.state,n=this.$store.getters;if(e){var r=B(this.$store,"mapState",e);if(!r)return;t=r.context.state,n=r.context.getters}return"function"===typeof i?i.call(this,t,n):t[i]},n[r].vuex=!0}),n}),L=F(function(e,t){var n={};return D(t).forEach(function(t){var r=t.key,i=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.commit;if(e){var s=B(this.$store,"mapMutations",e);if(!s)return;r=s.context.commit}return"function"===typeof i?i.apply(this,[r].concat(t)):r.apply(this.$store,[i].concat(t))}}),n}),M=F(function(e,t){var n={};return D(t).forEach(function(t){var r=t.key,i=t.val;i=e+i,n[r]=function(){if(!e||B(this.$store,"mapGetters",e))return this.$store.getters[i]},n[r].vuex=!0}),n}),N=F(function(e,t){var n={};return D(t).forEach(function(t){var r=t.key,i=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.dispatch;if(e){var s=B(this.$store,"mapActions",e);if(!s)return;r=s.context.dispatch}return"function"===typeof i?i.apply(this,[r].concat(t)):r.apply(this.$store,[i].concat(t))}}),n}),j=function(e){return{mapState:I.bind(null,e),mapGetters:M.bind(null,e),mapMutations:L.bind(null,e),mapActions:N.bind(null,e)}};function D(e){return z(e)?Array.isArray(e)?e.map(function(e){return{key:e,val:e}}):Object.keys(e).map(function(t){return{key:t,val:e[t]}}):[]}function z(e){return Array.isArray(e)||u(e)}function F(e){return function(t,n){return"string"!==typeof t?(n=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,n)}}function B(e,t,n){var r=e._modulesNamespaceMap[n];return r}function U(e){void 0===e&&(e={});var t=e.collapsed;void 0===t&&(t=!0);var n=e.filter;void 0===n&&(n=function(e,t,n){return!0});var r=e.transformer;void 0===r&&(r=function(e){return e});var i=e.mutationTransformer;void 0===i&&(i=function(e){return e});var s=e.actionFilter;void 0===s&&(s=function(e,t){return!0});var o=e.actionTransformer;void 0===o&&(o=function(e){return e});var a=e.logMutations;void 0===a&&(a=!0);var c=e.logActions;void 0===c&&(c=!0);var u=e.logger;return void 0===u&&(u=console),function(e){var d=l(e.state);"undefined"!==typeof u&&(a&&e.subscribe(function(e,s){var o=l(s);if(n(e,d,o)){var a=G(),c=i(e),p="mutation "+e.type+a;q(u,p,t),u.log("%c prev state","color: #9E9E9E; font-weight: bold",r(d)),u.log("%c mutation","color: #03A9F4; font-weight: bold",c),u.log("%c next state","color: #4CAF50; font-weight: bold",r(o)),H(u)}d=o}),c&&e.subscribeAction(function(e,n){if(s(e,n)){var r=G(),i=o(e),a="action "+e.type+r;q(u,a,t),u.log("%c action","color: #03A9F4; font-weight: bold",i),H(u)}}))}}function q(e,t,n){var r=n?e.groupCollapsed:e.group;try{r.call(e,t)}catch(i){e.log(t)}}function H(e){try{e.groupEnd()}catch(t){e.log("—— log end ——")}}function G(){var e=new Date;return" @ "+W(e.getHours(),2)+":"+W(e.getMinutes(),2)+":"+W(e.getSeconds(),2)+"."+W(e.getMilliseconds(),3)}function V(e,t){return new Array(t+1).join(e)}function W(e,t){return V("0",t-e.toString().length)+e}var Y={Store:y,install:A,version:"3.6.2",mapState:I,mapMutations:L,mapGetters:M,mapActions:N,createNamespacedHelpers:j,createLogger:U};const X=Y},5471:(e,t,n)=>{"use strict";n.r(t),n.d(t,{EffectScope:()=>$t,computed:()=>vt,customRef:()=>lt,default:()=>is,defineAsyncComponent:()=>nr,defineComponent:()=>br,del:()=>Ue,effectScope:()=>Rt,getCurrentInstance:()=>ve,getCurrentScope:()=>At,h:()=>zn,inject:()=>Nt,isProxy:()=>Ke,isReactive:()=>We,isReadonly:()=>Xe,isRef:()=>et,isShallow:()=>Ye,markRaw:()=>Ze,mergeDefaults:()=>Tn,nextTick:()=>Qn,onActivated:()=>dr,onBeforeMount:()=>sr,onBeforeUnmount:()=>cr,onBeforeUpdate:()=>ar,onDeactivated:()=>pr,onErrorCaptured:()=>vr,onMounted:()=>or,onRenderTracked:()=>fr,onRenderTriggered:()=>mr,onScopeDispose:()=>It,onServerPrefetch:()=>hr,onUnmounted:()=>ur,onUpdated:()=>lr,provide:()=>Lt,proxyRefs:()=>ot,reactive:()=>He,readonly:()=>ht,ref:()=>tt,set:()=>Be,shallowReactive:()=>Ge,shallowReadonly:()=>gt,shallowRef:()=>nt,toRaw:()=>Je,toRef:()=>ut,toRefs:()=>ct,triggerRef:()=>it,unref:()=>st,useAttrs:()=>En,useCssModule:()=>er,useCssVars:()=>tr,useListeners:()=>Sn,useSlots:()=>xn,version:()=>yr,watch:()=>_t,watchEffect:()=>Et,watchPostEffect:()=>St,watchSyncEffect:()=>Ct});
/*!
 * Vue.js v2.7.16
 * (c) 2014-2023 Evan You
 * Released under the MIT License.
 */
var r=Object.freeze({}),i=Array.isArray;function s(e){return void 0===e||null===e}function o(e){return void 0!==e&&null!==e}function a(e){return!0===e}function l(e){return!1===e}function c(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function u(e){return"function"===typeof e}function d(e){return null!==e&&"object"===typeof e}var p=Object.prototype.toString;function h(e){return"[object Object]"===p.call(e)}function f(e){return"[object RegExp]"===p.call(e)}function m(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function g(e){return o(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function v(e){return null==e?"":Array.isArray(e)||h(e)&&e.toString===p?JSON.stringify(e,y,2):String(e)}function y(e,t){return t&&t.__v_isRef?t.value:t}function b(e){var t=parseFloat(e);return isNaN(t)?e:t}function w(e,t){for(var n=Object.create(null),r=e.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}w("slot,component",!0);var x=w("key,ref,slot,slot-scope,is");function E(e,t){var n=e.length;if(n){if(t===e[n-1])return void(e.length=n-1);var r=e.indexOf(t);if(r>-1)return e.splice(r,1)}}var S=Object.prototype.hasOwnProperty;function C(e,t){return S.call(e,t)}function T(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var k=/-(\w)/g,_=T(function(e){return e.replace(k,function(e,t){return t?t.toUpperCase():""})}),O=T(function(e){return e.charAt(0).toUpperCase()+e.slice(1)}),$=/\B([A-Z])/g,R=T(function(e){return e.replace($,"-$1").toLowerCase()});function P(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n}function A(e,t){return e.bind(t)}var I=Function.prototype.bind?A:P;function L(e,t){t=t||0;var n=e.length-t,r=new Array(n);while(n--)r[n]=e[n+t];return r}function M(e,t){for(var n in t)e[n]=t[n];return e}function N(e){for(var t={},n=0;n<e.length;n++)e[n]&&M(t,e[n]);return t}function j(e,t,n){}var D=function(e,t,n){return!1},z=function(e){return e};function F(e,t){if(e===t)return!0;var n=d(e),r=d(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var i=Array.isArray(e),s=Array.isArray(t);if(i&&s)return e.length===t.length&&e.every(function(e,n){return F(e,t[n])});if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(i||s)return!1;var o=Object.keys(e),a=Object.keys(t);return o.length===a.length&&o.every(function(n){return F(e[n],t[n])})}catch(l){return!1}}function B(e,t){for(var n=0;n<e.length;n++)if(F(e[n],t))return n;return-1}function U(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}function q(e,t){return e===t?0===e&&1/e!==1/t:e===e||t===t}var H="data-server-rendered",G=["component","directive","filter"],V=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch","renderTracked","renderTriggered"],W={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:D,isReservedAttr:D,isUnknownElement:D,getTagNamespace:j,parsePlatformTagName:z,mustUseProp:D,async:!0,_lifecycleHooks:V},Y=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function X(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function K(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var J=new RegExp("[^".concat(Y.source,".$_\\d]"));function Z(e){if(!J.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}var Q="__proto__"in{},ee="undefined"!==typeof window,te=ee&&window.navigator.userAgent.toLowerCase(),ne=te&&/msie|trident/.test(te),re=te&&te.indexOf("msie 9.0")>0,ie=te&&te.indexOf("edge/")>0;te&&te.indexOf("android");var se=te&&/iphone|ipad|ipod|ios/.test(te);te&&/chrome\/\d+/.test(te),te&&/phantomjs/.test(te);var oe,ae=te&&te.match(/firefox\/(\d+)/),le={}.watch,ce=!1;if(ee)try{var ue={};Object.defineProperty(ue,"passive",{get:function(){ce=!0}}),window.addEventListener("test-passive",null,ue)}catch(sl){}var de=function(){return void 0===oe&&(oe=!ee&&"undefined"!==typeof n.g&&(n.g["process"]&&"server"===n.g["process"].env.VUE_ENV)),oe},pe=ee&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function he(e){return"function"===typeof e&&/native code/.test(e.toString())}var fe,me="undefined"!==typeof Symbol&&he(Symbol)&&"undefined"!==typeof Reflect&&he(Reflect.ownKeys);fe="undefined"!==typeof Set&&he(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ge=null;function ve(){return ge&&{proxy:ge}}function ye(e){void 0===e&&(e=null),e||ge&&ge._scope.off(),ge=e,e&&e._scope.on()}var be=function(){function e(e,t,n,r,i,s,o,a){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=i,this.ns=void 0,this.context=s,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=o,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=a,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1}return Object.defineProperty(e.prototype,"child",{get:function(){return this.componentInstance},enumerable:!1,configurable:!0}),e}(),we=function(e){void 0===e&&(e="");var t=new be;return t.text=e,t.isComment=!0,t};function xe(e){return new be(void 0,void 0,void 0,String(e))}function Ee(e){var t=new be(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}"function"===typeof SuppressedError&&SuppressedError;var Se=0,Ce=[],Te=function(){for(var e=0;e<Ce.length;e++){var t=Ce[e];t.subs=t.subs.filter(function(e){return e}),t._pending=!1}Ce.length=0},ke=function(){function e(){this._pending=!1,this.id=Se++,this.subs=[]}return e.prototype.addSub=function(e){this.subs.push(e)},e.prototype.removeSub=function(e){this.subs[this.subs.indexOf(e)]=null,this._pending||(this._pending=!0,Ce.push(this))},e.prototype.depend=function(t){e.target&&e.target.addDep(this)},e.prototype.notify=function(e){var t=this.subs.filter(function(e){return e});for(var n=0,r=t.length;n<r;n++){var i=t[n];0,i.update()}},e}();ke.target=null;var _e=[];function Oe(e){_e.push(e),ke.target=e}function $e(){_e.pop(),ke.target=_e[_e.length-1]}var Re=Array.prototype,Pe=Object.create(Re),Ae=["push","pop","shift","unshift","splice","sort","reverse"];Ae.forEach(function(e){var t=Re[e];K(Pe,e,function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i,s=t.apply(this,n),o=this.__ob__;switch(e){case"push":case"unshift":i=n;break;case"splice":i=n.slice(2);break}return i&&o.observeArray(i),o.dep.notify(),s})});var Ie=Object.getOwnPropertyNames(Pe),Le={},Me=!0;function Ne(e){Me=e}var je={notify:j,depend:j,addSub:j,removeSub:j},De=function(){function e(e,t,n){if(void 0===t&&(t=!1),void 0===n&&(n=!1),this.value=e,this.shallow=t,this.mock=n,this.dep=n?je:new ke,this.vmCount=0,K(e,"__ob__",this),i(e)){if(!n)if(Q)e.__proto__=Pe;else for(var r=0,s=Ie.length;r<s;r++){var o=Ie[r];K(e,o,Pe[o])}t||this.observeArray(e)}else{var a=Object.keys(e);for(r=0;r<a.length;r++){o=a[r];Fe(e,o,Le,void 0,t,n)}}}return e.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)ze(e[t],!1,this.mock)},e}();function ze(e,t,n){return e&&C(e,"__ob__")&&e.__ob__ instanceof De?e.__ob__:!Me||!n&&de()||!i(e)&&!h(e)||!Object.isExtensible(e)||e.__v_skip||et(e)||e instanceof be?void 0:new De(e,t,n)}function Fe(e,t,n,r,s,o,a){void 0===a&&(a=!1);var l=new ke,c=Object.getOwnPropertyDescriptor(e,t);if(!c||!1!==c.configurable){var u=c&&c.get,d=c&&c.set;u&&!d||n!==Le&&2!==arguments.length||(n=e[t]);var p=s?n&&n.__ob__:ze(n,!1,o);return Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=u?u.call(e):n;return ke.target&&(l.depend(),p&&(p.dep.depend(),i(t)&&qe(t))),et(t)&&!s?t.value:t},set:function(t){var r=u?u.call(e):n;if(q(r,t)){if(d)d.call(e,t);else{if(u)return;if(!s&&et(r)&&!et(t))return void(r.value=t);n=t}p=s?t&&t.__ob__:ze(t,!1,o),l.notify()}}}),l}}function Be(e,t,n){if(!Xe(e)){var r=e.__ob__;return i(e)&&m(t)?(e.length=Math.max(e.length,t),e.splice(t,1,n),r&&!r.shallow&&r.mock&&ze(n,!1,!0),n):t in e&&!(t in Object.prototype)?(e[t]=n,n):e._isVue||r&&r.vmCount?n:r?(Fe(r.value,t,n,void 0,r.shallow,r.mock),r.dep.notify(),n):(e[t]=n,n)}}function Ue(e,t){if(i(e)&&m(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||Xe(e)||C(e,t)&&(delete e[t],n&&n.dep.notify())}}function qe(e){for(var t=void 0,n=0,r=e.length;n<r;n++)t=e[n],t&&t.__ob__&&t.__ob__.dep.depend(),i(t)&&qe(t)}function He(e){return Ve(e,!1),e}function Ge(e){return Ve(e,!0),K(e,"__v_isShallow",!0),e}function Ve(e,t){if(!Xe(e)){ze(e,t,de());0}}function We(e){return Xe(e)?We(e["__v_raw"]):!(!e||!e.__ob__)}function Ye(e){return!(!e||!e.__v_isShallow)}function Xe(e){return!(!e||!e.__v_isReadonly)}function Ke(e){return We(e)||Xe(e)}function Je(e){var t=e&&e["__v_raw"];return t?Je(t):e}function Ze(e){return Object.isExtensible(e)&&K(e,"__v_skip",!0),e}var Qe="__v_isRef";function et(e){return!(!e||!0!==e.__v_isRef)}function tt(e){return rt(e,!1)}function nt(e){return rt(e,!0)}function rt(e,t){if(et(e))return e;var n={};return K(n,Qe,!0),K(n,"__v_isShallow",t),K(n,"dep",Fe(n,"value",e,null,t,de())),n}function it(e){e.dep&&e.dep.notify()}function st(e){return et(e)?e.value:e}function ot(e){if(We(e))return e;for(var t={},n=Object.keys(e),r=0;r<n.length;r++)at(t,e,n[r]);return t}function at(e,t,n){Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:function(){var e=t[n];if(et(e))return e.value;var r=e&&e.__ob__;return r&&r.dep.depend(),e},set:function(e){var r=t[n];et(r)&&!et(e)?r.value=e:t[n]=e}})}function lt(e){var t=new ke,n=e(function(){t.depend()},function(){t.notify()}),r=n.get,i=n.set,s={get value(){return r()},set value(e){i(e)}};return K(s,Qe,!0),s}function ct(e){var t=i(e)?new Array(e.length):{};for(var n in e)t[n]=ut(e,n);return t}function ut(e,t,n){var r=e[t];if(et(r))return r;var i={get value(){var r=e[t];return void 0===r?n:r},set value(n){e[t]=n}};return K(i,Qe,!0),i}var dt="__v_rawToReadonly",pt="__v_rawToShallowReadonly";function ht(e){return ft(e,!1)}function ft(e,t){if(!h(e))return e;if(Xe(e))return e;var n=t?pt:dt,r=e[n];if(r)return r;var i=Object.create(Object.getPrototypeOf(e));K(e,n,i),K(i,"__v_isReadonly",!0),K(i,"__v_raw",e),et(e)&&K(i,Qe,!0),(t||Ye(e))&&K(i,"__v_isShallow",!0);for(var s=Object.keys(e),o=0;o<s.length;o++)mt(i,e,s[o],t);return i}function mt(e,t,n,r){Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:function(){var e=t[n];return r||!h(e)?e:ht(e)},set:function(){}})}function gt(e){return ft(e,!0)}function vt(e,t){var n,r,i=u(e);i?(n=e,r=j):(n=e.get,r=e.set);var s=de()?null:new Tr(ge,n,j,{lazy:!0});var o={effect:s,get value(){return s?(s.dirty&&s.evaluate(),ke.target&&s.depend(),s.value):n()},set value(e){r(e)}};return K(o,Qe,!0),K(o,"__v_isReadonly",i),o}var yt="watcher",bt="".concat(yt," callback"),wt="".concat(yt," getter"),xt="".concat(yt," cleanup");function Et(e,t){return Ot(e,null,t)}function St(e,t){return Ot(e,null,{flush:"post"})}function Ct(e,t){return Ot(e,null,{flush:"sync"})}var Tt,kt={};function _t(e,t,n){return Ot(e,t,n)}function Ot(e,t,n){var s=void 0===n?r:n,o=s.immediate,a=s.deep,l=s.flush,c=void 0===l?"pre":l;s.onTrack,s.onTrigger;var d,p,h=ge,f=function(e,t,n){void 0===n&&(n=null);var r=Bn(e,null,n,h,t);return a&&r&&r.__ob__&&r.__ob__.dep.depend(),r},m=!1,g=!1;if(et(e)?(d=function(){return e.value},m=Ye(e)):We(e)?(d=function(){return e.__ob__.dep.depend(),e},a=!0):i(e)?(g=!0,m=e.some(function(e){return We(e)||Ye(e)}),d=function(){return e.map(function(e){return et(e)?e.value:We(e)?(e.__ob__.dep.depend(),xr(e)):u(e)?f(e,wt):void 0})}):d=u(e)?t?function(){return f(e,wt)}:function(){if(!h||!h._isDestroyed)return p&&p(),f(e,yt,[y])}:j,t&&a){var v=d;d=function(){return xr(v())}}var y=function(e){p=b.onStop=function(){f(e,xt)}};if(de())return y=j,t?o&&f(t,bt,[d(),g?[]:void 0,y]):d(),j;var b=new Tr(ge,d,j,{lazy:!0});b.noRecurse=!t;var w=g?[]:kt;return b.run=function(){if(b.active)if(t){var e=b.get();(a||m||(g?e.some(function(e,t){return q(e,w[t])}):q(e,w)))&&(p&&p(),f(t,bt,[e,w===kt?void 0:w,y]),w=e)}else b.get()},"sync"===c?b.update=b.run:"post"===c?(b.post=!0,b.update=function(){return ri(b)}):b.update=function(){if(h&&h===ge&&!h._isMounted){var e=h._preWatchers||(h._preWatchers=[]);e.indexOf(b)<0&&e.push(b)}else ri(b)},t?o?b.run():w=b.get():"post"===c&&h?h.$once("hook:mounted",function(){return b.get()}):b.get(),function(){b.teardown()}}var $t=function(){function e(e){void 0===e&&(e=!1),this.detached=e,this.active=!0,this.effects=[],this.cleanups=[],this.parent=Tt,!e&&Tt&&(this.index=(Tt.scopes||(Tt.scopes=[])).push(this)-1)}return e.prototype.run=function(e){if(this.active){var t=Tt;try{return Tt=this,e()}finally{Tt=t}}else 0},e.prototype.on=function(){Tt=this},e.prototype.off=function(){Tt=this.parent},e.prototype.stop=function(e){if(this.active){var t=void 0,n=void 0;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].teardown();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this.active=!1}},e}();function Rt(e){return new $t(e)}function Pt(e,t){void 0===t&&(t=Tt),t&&t.active&&t.effects.push(e)}function At(){return Tt}function It(e){Tt&&Tt.cleanups.push(e)}function Lt(e,t){ge&&(Mt(ge)[e]=t)}function Mt(e){var t=e._provided,n=e.$parent&&e.$parent._provided;return n===t?e._provided=Object.create(n):t}function Nt(e,t,n){void 0===n&&(n=!1);var r=ge;if(r){var i=r.$parent&&r.$parent._provided;if(i&&e in i)return i[e];if(arguments.length>1)return n&&u(t)?t.call(r):t}else 0}var jt=T(function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var n="~"===e.charAt(0);e=n?e.slice(1):e;var r="!"===e.charAt(0);return e=r?e.slice(1):e,{name:e,once:n,capture:r,passive:t}});function Dt(e,t){function n(){var e=n.fns;if(!i(e))return Bn(e,null,arguments,t,"v-on handler");for(var r=e.slice(),s=0;s<r.length;s++)Bn(r[s],null,arguments,t,"v-on handler")}return n.fns=e,n}function zt(e,t,n,r,i,o){var l,c,u,d;for(l in e)c=e[l],u=t[l],d=jt(l),s(c)||(s(u)?(s(c.fns)&&(c=e[l]=Dt(c,o)),a(d.once)&&(c=e[l]=i(d.name,c,d.capture)),n(d.name,c,d.capture,d.passive,d.params)):c!==u&&(u.fns=c,e[l]=u));for(l in t)s(e[l])&&(d=jt(l),r(d.name,t[l],d.capture))}function Ft(e,t,n){var r;e instanceof be&&(e=e.data.hook||(e.data.hook={}));var i=e[t];function l(){n.apply(this,arguments),E(r.fns,l)}s(i)?r=Dt([l]):o(i.fns)&&a(i.merged)?(r=i,r.fns.push(l)):r=Dt([i,l]),r.merged=!0,e[t]=r}function Bt(e,t,n){var r=t.options.props;if(!s(r)){var i={},a=e.attrs,l=e.props;if(o(a)||o(l))for(var c in r){var u=R(c);Ut(i,l,c,u,!0)||Ut(i,a,c,u,!1)}return i}}function Ut(e,t,n,r,i){if(o(t)){if(C(t,n))return e[n]=t[n],i||delete t[n],!0;if(C(t,r))return e[n]=t[r],i||delete t[r],!0}return!1}function qt(e){for(var t=0;t<e.length;t++)if(i(e[t]))return Array.prototype.concat.apply([],e);return e}function Ht(e){return c(e)?[xe(e)]:i(e)?Vt(e):void 0}function Gt(e){return o(e)&&o(e.text)&&l(e.isComment)}function Vt(e,t){var n,r,l,u,d=[];for(n=0;n<e.length;n++)r=e[n],s(r)||"boolean"===typeof r||(l=d.length-1,u=d[l],i(r)?r.length>0&&(r=Vt(r,"".concat(t||"","_").concat(n)),Gt(r[0])&&Gt(u)&&(d[l]=xe(u.text+r[0].text),r.shift()),d.push.apply(d,r)):c(r)?Gt(u)?d[l]=xe(u.text+r):""!==r&&d.push(xe(r)):Gt(r)&&Gt(u)?d[l]=xe(u.text+r.text):(a(e._isVList)&&o(r.tag)&&s(r.key)&&o(t)&&(r.key="__vlist".concat(t,"_").concat(n,"__")),d.push(r)));return d}function Wt(e,t){var n,r,s,a,l=null;if(i(e)||"string"===typeof e)for(l=new Array(e.length),n=0,r=e.length;n<r;n++)l[n]=t(e[n],n);else if("number"===typeof e)for(l=new Array(e),n=0;n<e;n++)l[n]=t(n+1,n);else if(d(e))if(me&&e[Symbol.iterator]){l=[];var c=e[Symbol.iterator](),u=c.next();while(!u.done)l.push(t(u.value,l.length)),u=c.next()}else for(s=Object.keys(e),l=new Array(s.length),n=0,r=s.length;n<r;n++)a=s[n],l[n]=t(e[a],a,n);return o(l)||(l=[]),l._isVList=!0,l}function Yt(e,t,n,r){var i,s=this.$scopedSlots[e];s?(n=n||{},r&&(n=M(M({},r),n)),i=s(n)||(u(t)?t():t)):i=this.$slots[e]||(u(t)?t():t);var o=n&&n.slot;return o?this.$createElement("template",{slot:o},i):i}function Xt(e){return Pi(this.$options,"filters",e,!0)||z}function Kt(e,t){return i(e)?-1===e.indexOf(t):e!==t}function Jt(e,t,n,r,i){var s=W.keyCodes[t]||n;return i&&r&&!W.keyCodes[t]?Kt(i,r):s?Kt(s,e):r?R(r)!==t:void 0===e}function Zt(e,t,n,r,s){if(n)if(d(n)){i(n)&&(n=N(n));var o=void 0,a=function(i){if("class"===i||"style"===i||x(i))o=e;else{var a=e.attrs&&e.attrs.type;o=r||W.mustUseProp(t,a,i)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var l=_(i),c=R(i);if(!(l in o)&&!(c in o)&&(o[i]=n[i],s)){var u=e.on||(e.on={});u["update:".concat(i)]=function(e){n[i]=e}}};for(var l in n)a(l)}else;return e}function Qt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,this._c,this),tn(r,"__static__".concat(e),!1)),r}function en(e,t,n){return tn(e,"__once__".concat(t).concat(n?"_".concat(n):""),!0),e}function tn(e,t,n){if(i(e))for(var r=0;r<e.length;r++)e[r]&&"string"!==typeof e[r]&&nn(e[r],"".concat(t,"_").concat(r),n);else nn(e,t,n)}function nn(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function rn(e,t){if(t)if(h(t)){var n=e.on=e.on?M({},e.on):{};for(var r in t){var i=n[r],s=t[r];n[r]=i?[].concat(i,s):s}}else;return e}function sn(e,t,n,r){t=t||{$stable:!n};for(var s=0;s<e.length;s++){var o=e[s];i(o)?sn(o,t,n):o&&(o.proxy&&(o.fn.proxy=!0),t[o.key]=o.fn)}return r&&(t.$key=r),t}function on(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"===typeof r&&r&&(e[t[n]]=t[n+1])}return e}function an(e,t){return"string"===typeof e?t+e:e}function ln(e){e._o=en,e._n=b,e._s=v,e._l=Wt,e._t=Yt,e._q=F,e._i=B,e._m=Qt,e._f=Xt,e._k=Jt,e._b=Zt,e._v=xe,e._e=we,e._u=sn,e._g=rn,e._d=on,e._p=an}function cn(e,t){if(!e||!e.length)return{};for(var n={},r=0,i=e.length;r<i;r++){var s=e[r],o=s.data;if(o&&o.attrs&&o.attrs.slot&&delete o.attrs.slot,s.context!==t&&s.fnContext!==t||!o||null==o.slot)(n.default||(n.default=[])).push(s);else{var a=o.slot,l=n[a]||(n[a]=[]);"template"===s.tag?l.push.apply(l,s.children||[]):l.push(s)}}for(var c in n)n[c].every(un)&&delete n[c];return n}function un(e){return e.isComment&&!e.asyncFactory||" "===e.text}function dn(e){return e.isComment&&e.asyncFactory}function pn(e,t,n,i){var s,o=Object.keys(n).length>0,a=t?!!t.$stable:!o,l=t&&t.$key;if(t){if(t._normalized)return t._normalized;if(a&&i&&i!==r&&l===i.$key&&!o&&!i.$hasNormal)return i;for(var c in s={},t)t[c]&&"$"!==c[0]&&(s[c]=hn(e,n,c,t[c]))}else s={};for(var u in n)u in s||(s[u]=fn(n,u));return t&&Object.isExtensible(t)&&(t._normalized=s),K(s,"$stable",a),K(s,"$key",l),K(s,"$hasNormal",o),s}function hn(e,t,n,r){var s=function(){var t=ge;ye(e);var n=arguments.length?r.apply(null,arguments):r({});n=n&&"object"===typeof n&&!i(n)?[n]:Ht(n);var s=n&&n[0];return ye(t),n&&(!s||1===n.length&&s.isComment&&!dn(s))?void 0:n};return r.proxy&&Object.defineProperty(t,n,{get:s,enumerable:!0,configurable:!0}),s}function fn(e,t){return function(){return e[t]}}function mn(e){var t=e.$options,n=t.setup;if(n){var r=e._setupContext=gn(e);ye(e),Oe();var i=Bn(n,null,[e._props||Ge({}),r],e,"setup");if($e(),ye(),u(i))t.render=i;else if(d(i))if(e._setupState=i,i.__sfc){var s=e._setupProxy={};for(var o in i)"__sfc"!==o&&at(s,i,o)}else for(var o in i)X(o)||at(e,i,o);else 0}}function gn(e){return{get attrs(){if(!e._attrsProxy){var t=e._attrsProxy={};K(t,"_v_attr_proxy",!0),vn(t,e.$attrs,r,e,"$attrs")}return e._attrsProxy},get listeners(){if(!e._listenersProxy){var t=e._listenersProxy={};vn(t,e.$listeners,r,e,"$listeners")}return e._listenersProxy},get slots(){return bn(e)},emit:I(e.$emit,e),expose:function(t){t&&Object.keys(t).forEach(function(n){return at(e,t,n)})}}}function vn(e,t,n,r,i){var s=!1;for(var o in t)o in e?t[o]!==n[o]&&(s=!0):(s=!0,yn(e,o,r,i));for(var o in e)o in t||(s=!0,delete e[o]);return s}function yn(e,t,n,r){Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){return n[r][t]}})}function bn(e){return e._slotsProxy||wn(e._slotsProxy={},e.$scopedSlots),e._slotsProxy}function wn(e,t){for(var n in t)e[n]=t[n];for(var n in e)n in t||delete e[n]}function xn(){return Cn().slots}function En(){return Cn().attrs}function Sn(){return Cn().listeners}function Cn(){var e=ge;return e._setupContext||(e._setupContext=gn(e))}function Tn(e,t){var n=i(e)?e.reduce(function(e,t){return e[t]={},e},{}):e;for(var r in t){var s=n[r];s?i(s)||u(s)?n[r]={type:s,default:t[r]}:s.default=t[r]:null===s&&(n[r]={default:t[r]})}return n}function kn(e){e._vnode=null,e._staticTrees=null;var t=e.$options,n=e.$vnode=t._parentVnode,i=n&&n.context;e.$slots=cn(t._renderChildren,i),e.$scopedSlots=n?pn(e.$parent,n.data.scopedSlots,e.$slots):r,e._c=function(t,n,r,i){return Mn(e,t,n,r,i,!1)},e.$createElement=function(t,n,r,i){return Mn(e,t,n,r,i,!0)};var s=n&&n.data;Fe(e,"$attrs",s&&s.attrs||r,null,!0),Fe(e,"$listeners",t._parentListeners||r,null,!0)}var _n=null;function On(e){ln(e.prototype),e.prototype.$nextTick=function(e){return Qn(e,this)},e.prototype._render=function(){var e=this,t=e.$options,n=t.render,r=t._parentVnode;r&&e._isMounted&&(e.$scopedSlots=pn(e.$parent,r.data.scopedSlots,e.$slots,e.$scopedSlots),e._slotsProxy&&wn(e._slotsProxy,e.$scopedSlots)),e.$vnode=r;var s,o=ge,a=_n;try{ye(e),_n=e,s=n.call(e._renderProxy,e.$createElement)}catch(sl){Fn(sl,e,"render"),s=e._vnode}finally{_n=a,ye(o)}return i(s)&&1===s.length&&(s=s[0]),s instanceof be||(s=we()),s.parent=r,s}}function $n(e,t){return(e.__esModule||me&&"Module"===e[Symbol.toStringTag])&&(e=e.default),d(e)?t.extend(e):e}function Rn(e,t,n,r,i){var s=we();return s.asyncFactory=e,s.asyncMeta={data:t,context:n,children:r,tag:i},s}function Pn(e,t){if(a(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var n=_n;if(n&&o(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n),a(e.loading)&&o(e.loadingComp))return e.loadingComp;if(n&&!o(e.owners)){var r=e.owners=[n],i=!0,l=null,c=null;n.$on("hook:destroyed",function(){return E(r,n)});var u=function(e){for(var t=0,n=r.length;t<n;t++)r[t].$forceUpdate();e&&(r.length=0,null!==l&&(clearTimeout(l),l=null),null!==c&&(clearTimeout(c),c=null))},p=U(function(n){e.resolved=$n(n,t),i?r.length=0:u(!0)}),h=U(function(t){o(e.errorComp)&&(e.error=!0,u(!0))}),f=e(p,h);return d(f)&&(g(f)?s(e.resolved)&&f.then(p,h):g(f.component)&&(f.component.then(p,h),o(f.error)&&(e.errorComp=$n(f.error,t)),o(f.loading)&&(e.loadingComp=$n(f.loading,t),0===f.delay?e.loading=!0:l=setTimeout(function(){l=null,s(e.resolved)&&s(e.error)&&(e.loading=!0,u(!1))},f.delay||200)),o(f.timeout)&&(c=setTimeout(function(){c=null,s(e.resolved)&&h(null)},f.timeout)))),i=!1,e.loading?e.loadingComp:e.resolved}}function An(e){if(i(e))for(var t=0;t<e.length;t++){var n=e[t];if(o(n)&&(o(n.componentOptions)||dn(n)))return n}}var In=1,Ln=2;function Mn(e,t,n,r,s,o){return(i(n)||c(n))&&(s=r,r=n,n=void 0),a(o)&&(s=Ln),Nn(e,t,n,r,s)}function Nn(e,t,n,r,s){if(o(n)&&o(n.__ob__))return we();if(o(n)&&o(n.is)&&(t=n.is),!t)return we();var a,l;if(i(r)&&u(r[0])&&(n=n||{},n.scopedSlots={default:r[0]},r.length=0),s===Ln?r=Ht(r):s===In&&(r=qt(r)),"string"===typeof t){var c=void 0;l=e.$vnode&&e.$vnode.ns||W.getTagNamespace(t),a=W.isReservedTag(t)?new be(W.parsePlatformTagName(t),n,r,void 0,void 0,e):n&&n.pre||!o(c=Pi(e.$options,"components",t))?new be(t,n,r,void 0,void 0,e):fi(c,n,e,r,t)}else a=fi(t,n,e,r);return i(a)?a:o(a)?(o(l)&&jn(a,l),o(n)&&Dn(n),a):we()}function jn(e,t,n){if(e.ns=t,"foreignObject"===e.tag&&(t=void 0,n=!0),o(e.children))for(var r=0,i=e.children.length;r<i;r++){var l=e.children[r];o(l.tag)&&(s(l.ns)||a(n)&&"svg"!==l.tag)&&jn(l,t,n)}}function Dn(e){d(e.style)&&xr(e.style),d(e.class)&&xr(e.class)}function zn(e,t,n){return Mn(ge,e,t,n,2,!0)}function Fn(e,t,n){Oe();try{if(t){var r=t;while(r=r.$parent){var i=r.$options.errorCaptured;if(i)for(var s=0;s<i.length;s++)try{var o=!1===i[s].call(r,e,t,n);if(o)return}catch(sl){Un(sl,r,"errorCaptured hook")}}}Un(e,t,n)}finally{$e()}}function Bn(e,t,n,r,i){var s;try{s=n?e.apply(t,n):e.call(t),s&&!s._isVue&&g(s)&&!s._handled&&(s.catch(function(e){return Fn(e,r,i+" (Promise/async)")}),s._handled=!0)}catch(sl){Fn(sl,r,i)}return s}function Un(e,t,n){if(W.errorHandler)try{return W.errorHandler.call(null,e,t,n)}catch(sl){sl!==e&&qn(sl,null,"config.errorHandler")}qn(e,t,n)}function qn(e,t,n){if(!ee||"undefined"===typeof console)throw e;console.error(e)}var Hn,Gn=!1,Vn=[],Wn=!1;function Yn(){Wn=!1;var e=Vn.slice(0);Vn.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&he(Promise)){var Xn=Promise.resolve();Hn=function(){Xn.then(Yn),se&&setTimeout(j)},Gn=!0}else if(ne||"undefined"===typeof MutationObserver||!he(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Hn="undefined"!==typeof setImmediate&&he(setImmediate)?function(){setImmediate(Yn)}:function(){setTimeout(Yn,0)};else{var Kn=1,Jn=new MutationObserver(Yn),Zn=document.createTextNode(String(Kn));Jn.observe(Zn,{characterData:!0}),Hn=function(){Kn=(Kn+1)%2,Zn.data=String(Kn)},Gn=!0}function Qn(e,t){var n;if(Vn.push(function(){if(e)try{e.call(t)}catch(sl){Fn(sl,t,"nextTick")}else n&&n(t)}),Wn||(Wn=!0,Hn()),!e&&"undefined"!==typeof Promise)return new Promise(function(e){n=e})}function er(e){if(void 0===e&&(e="$style"),!ge)return r;var t=ge[e];return t||r}function tr(e){if(ee){var t=ge;t&&St(function(){var n=t.$el,r=e(t,t._setupProxy);if(n&&1===n.nodeType){var i=n.style;for(var s in r)i.setProperty("--".concat(s),r[s])}})}}function nr(e){u(e)&&(e={loader:e});var t=e.loader,n=e.loadingComponent,r=e.errorComponent,i=e.delay,s=void 0===i?200:i,o=e.timeout,a=(e.suspensible,e.onError);var l=null,c=0,d=function(){return c++,l=null,p()},p=function(){var e;return l||(e=l=t().catch(function(e){if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(function(t,n){var r=function(){return t(d())},i=function(){return n(e)};a(e,r,i,c+1)});throw e}).then(function(t){return e!==l&&l?l:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),t)}))};return function(){var e=p();return{component:e,delay:s,timeout:o,error:r,loading:n}}}function rr(e){return function(t,n){if(void 0===n&&(n=ge),n)return ir(n,e,t)}}function ir(e,t,n){var r=e.$options;r[t]=Si(r[t],n)}var sr=rr("beforeMount"),or=rr("mounted"),ar=rr("beforeUpdate"),lr=rr("updated"),cr=rr("beforeDestroy"),ur=rr("destroyed"),dr=rr("activated"),pr=rr("deactivated"),hr=rr("serverPrefetch"),fr=rr("renderTracked"),mr=rr("renderTriggered"),gr=rr("errorCaptured");function vr(e,t){void 0===t&&(t=ge),gr(e,t)}var yr="2.7.16";function br(e){return e}var wr=new fe;function xr(e){return Er(e,wr),wr.clear(),e}function Er(e,t){var n,r,s=i(e);if(!(!s&&!d(e)||e.__v_skip||Object.isFrozen(e)||e instanceof be)){if(e.__ob__){var o=e.__ob__.dep.id;if(t.has(o))return;t.add(o)}if(s){n=e.length;while(n--)Er(e[n],t)}else if(et(e))Er(e.value,t);else{r=Object.keys(e),n=r.length;while(n--)Er(e[r[n]],t)}}}var Sr,Cr=0,Tr=function(){function e(e,t,n,r,i){Pt(this,Tt&&!Tt._vm?Tt:e?e._scope:void 0),(this.vm=e)&&i&&(e._watcher=this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++Cr,this.active=!0,this.post=!1,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new fe,this.newDepIds=new fe,this.expression="",u(t)?this.getter=t:(this.getter=Z(t),this.getter||(this.getter=j)),this.value=this.lazy?void 0:this.get()}return e.prototype.get=function(){var e;Oe(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(sl){if(!this.user)throw sl;Fn(sl,t,'getter for watcher "'.concat(this.expression,'"'))}finally{this.deep&&xr(e),$e(),this.cleanupDeps()}return e},e.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},e.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},e.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():ri(this)},e.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||d(e)||this.deep){var t=this.value;if(this.value=e,this.user){var n='callback for watcher "'.concat(this.expression,'"');Bn(this.cb,this.vm,[e,t],this.vm,n)}else this.cb.call(this.vm,e,t)}}},e.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},e.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},e.prototype.teardown=function(){if(this.vm&&!this.vm._isBeingDestroyed&&E(this.vm._scope.effects,this),this.active){var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1,this.onStop&&this.onStop()}},e}();function kr(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Rr(e,t)}function _r(e,t){Sr.$on(e,t)}function Or(e,t){Sr.$off(e,t)}function $r(e,t){var n=Sr;return function r(){var i=t.apply(null,arguments);null!==i&&n.$off(e,r)}}function Rr(e,t,n){Sr=e,zt(t,n||{},_r,Or,$r,e),Sr=void 0}function Pr(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(i(e))for(var s=0,o=e.length;s<o;s++)r.$on(e[s],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(i(e)){for(var r=0,s=e.length;r<s;r++)n.$off(e[r],t);return n}var o,a=n._events[e];if(!a)return n;if(!t)return n._events[e]=null,n;var l=a.length;while(l--)if(o=a[l],o===t||o.fn===t){a.splice(l,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?L(n):n;for(var r=L(arguments,1),i='event handler for "'.concat(e,'"'),s=0,o=n.length;s<o;s++)Bn(n[s],t,r,t,i)}return t}}var Ar=null;function Ir(e){var t=Ar;return Ar=e,function(){Ar=t}}function Lr(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._provided=n?n._provided:Object.create(null),e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}function Mr(e){e.prototype._update=function(e,t){var n=this,r=n.$el,i=n._vnode,s=Ir(n);n._vnode=e,n.$el=i?n.__patch__(i,e):n.__patch__(n.$el,e,t,!1),s(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n);var o=n;while(o&&o.$vnode&&o.$parent&&o.$vnode===o.$parent._vnode)o.$parent.$el=o.$el,o=o.$parent},e.prototype.$forceUpdate=function(){var e=this;e._watcher&&e._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){Br(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||E(t.$children,e),e._scope.stop(),e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),Br(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}function Nr(e,t,n){var r;e.$el=t,e.$options.render||(e.$options.render=we),Br(e,"beforeMount"),r=function(){e._update(e._render(),n)};var i={before:function(){e._isMounted&&!e._isDestroyed&&Br(e,"beforeUpdate")}};new Tr(e,r,j,i,!0),n=!1;var s=e._preWatchers;if(s)for(var o=0;o<s.length;o++)s[o].run();return null==e.$vnode&&(e._isMounted=!0,Br(e,"mounted")),e}function jr(e,t,n,i,s){var o=i.data.scopedSlots,a=e.$scopedSlots,l=!!(o&&!o.$stable||a!==r&&!a.$stable||o&&e.$scopedSlots.$key!==o.$key||!o&&e.$scopedSlots.$key),c=!!(s||e.$options._renderChildren||l),u=e.$vnode;e.$options._parentVnode=i,e.$vnode=i,e._vnode&&(e._vnode.parent=i),e.$options._renderChildren=s;var d=i.data.attrs||r;e._attrsProxy&&vn(e._attrsProxy,d,u.data&&u.data.attrs||r,e,"$attrs")&&(c=!0),e.$attrs=d,n=n||r;var p=e.$options._parentListeners;if(e._listenersProxy&&vn(e._listenersProxy,n,p||r,e,"$listeners"),e.$listeners=e.$options._parentListeners=n,Rr(e,n,p),t&&e.$options.props){Ne(!1);for(var h=e._props,f=e.$options._propKeys||[],m=0;m<f.length;m++){var g=f[m],v=e.$options.props;h[g]=Ai(g,v,t,e)}Ne(!0),e.$options.propsData=t}c&&(e.$slots=cn(s,i.context),e.$forceUpdate())}function Dr(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function zr(e,t){if(t){if(e._directInactive=!1,Dr(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)zr(e.$children[n]);Br(e,"activated")}}function Fr(e,t){if((!t||(e._directInactive=!0,!Dr(e)))&&!e._inactive){e._inactive=!0;for(var n=0;n<e.$children.length;n++)Fr(e.$children[n]);Br(e,"deactivated")}}function Br(e,t,n,r){void 0===r&&(r=!0),Oe();var i=ge,s=At();r&&ye(e);var o=e.$options[t],a="".concat(t," hook");if(o)for(var l=0,c=o.length;l<c;l++)Bn(o[l],e,n||null,e,a);e._hasHookEvent&&e.$emit("hook:"+t),r&&(ye(i),s&&s.on()),$e()}var Ur=[],qr=[],Hr={},Gr=!1,Vr=!1,Wr=0;function Yr(){Wr=Ur.length=qr.length=0,Hr={},Gr=Vr=!1}var Xr=0,Kr=Date.now;if(ee&&!ne){var Jr=window.performance;Jr&&"function"===typeof Jr.now&&Kr()>document.createEvent("Event").timeStamp&&(Kr=function(){return Jr.now()})}var Zr=function(e,t){if(e.post){if(!t.post)return 1}else if(t.post)return-1;return e.id-t.id};function Qr(){var e,t;for(Xr=Kr(),Vr=!0,Ur.sort(Zr),Wr=0;Wr<Ur.length;Wr++)e=Ur[Wr],e.before&&e.before(),t=e.id,Hr[t]=null,e.run();var n=qr.slice(),r=Ur.slice();Yr(),ni(n),ei(r),Te(),pe&&W.devtools&&pe.emit("flush")}function ei(e){var t=e.length;while(t--){var n=e[t],r=n.vm;r&&r._watcher===n&&r._isMounted&&!r._isDestroyed&&Br(r,"updated")}}function ti(e){e._inactive=!1,qr.push(e)}function ni(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,zr(e[t],!0)}function ri(e){var t=e.id;if(null==Hr[t]&&(e!==ke.target||!e.noRecurse)){if(Hr[t]=!0,Vr){var n=Ur.length-1;while(n>Wr&&Ur[n].id>e.id)n--;Ur.splice(n+1,0,e)}else Ur.push(e);Gr||(Gr=!0,Qn(Qr))}}function ii(e){var t=e.$options.provide;if(t){var n=u(t)?t.call(e):t;if(!d(n))return;for(var r=Mt(e),i=me?Reflect.ownKeys(n):Object.keys(n),s=0;s<i.length;s++){var o=i[s];Object.defineProperty(r,o,Object.getOwnPropertyDescriptor(n,o))}}}function si(e){var t=oi(e.$options.inject,e);t&&(Ne(!1),Object.keys(t).forEach(function(n){Fe(e,n,t[n])}),Ne(!0))}function oi(e,t){if(e){for(var n=Object.create(null),r=me?Reflect.ownKeys(e):Object.keys(e),i=0;i<r.length;i++){var s=r[i];if("__ob__"!==s){var o=e[s].from;if(o in t._provided)n[s]=t._provided[o];else if("default"in e[s]){var a=e[s].default;n[s]=u(a)?a.call(t):a}else 0}}return n}}function ai(e,t,n,s,o){var l,c=this,u=o.options;C(s,"_uid")?(l=Object.create(s),l._original=s):(l=s,s=s._original);var d=a(u._compiled),p=!d;this.data=e,this.props=t,this.children=n,this.parent=s,this.listeners=e.on||r,this.injections=oi(u.inject,s),this.slots=function(){return c.$slots||pn(s,e.scopedSlots,c.$slots=cn(n,s)),c.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return pn(s,e.scopedSlots,this.slots())}}),d&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=pn(s,e.scopedSlots,this.$slots)),u._scopeId?this._c=function(e,t,n,r){var o=Mn(l,e,t,n,r,p);return o&&!i(o)&&(o.fnScopeId=u._scopeId,o.fnContext=s),o}:this._c=function(e,t,n,r){return Mn(l,e,t,n,r,p)}}function li(e,t,n,s,a){var l=e.options,c={},u=l.props;if(o(u))for(var d in u)c[d]=Ai(d,u,t||r);else o(n.attrs)&&ui(c,n.attrs),o(n.props)&&ui(c,n.props);var p=new ai(n,c,a,s,e),h=l.render.call(null,p._c,p);if(h instanceof be)return ci(h,n,p.parent,l,p);if(i(h)){for(var f=Ht(h)||[],m=new Array(f.length),g=0;g<f.length;g++)m[g]=ci(f[g],n,p.parent,l,p);return m}}function ci(e,t,n,r,i){var s=Ee(e);return s.fnContext=n,s.fnOptions=r,t.slot&&((s.data||(s.data={})).slot=t.slot),s}function ui(e,t){for(var n in t)e[_(n)]=t[n]}function di(e){return e.name||e.__name||e._componentTag}ln(ai.prototype);var pi={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;pi.prepatch(n,n)}else{var r=e.componentInstance=mi(e,Ar);r.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var n=t.componentOptions,r=t.componentInstance=e.componentInstance;jr(r,n.propsData,n.listeners,t,n.children)},insert:function(e){var t=e.context,n=e.componentInstance;n._isMounted||(n._isMounted=!0,Br(n,"mounted")),e.data.keepAlive&&(t._isMounted?ti(n):zr(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?Fr(t,!0):t.$destroy())}},hi=Object.keys(pi);function fi(e,t,n,r,i){if(!s(e)){var l=n.$options._base;if(d(e)&&(e=l.extend(e)),"function"===typeof e){var c;if(s(e.cid)&&(c=e,e=Pn(c,l),void 0===e))return Rn(c,t,n,r,i);t=t||{},ns(e),o(t.model)&&yi(e.options,t);var u=Bt(t,e,i);if(a(e.options.functional))return li(e,u,t,n,r);var p=t.on;if(t.on=t.nativeOn,a(e.options.abstract)){var h=t.slot;t={},h&&(t.slot=h)}gi(t);var f=di(e.options)||i,m=new be("vue-component-".concat(e.cid).concat(f?"-".concat(f):""),t,void 0,void 0,void 0,n,{Ctor:e,propsData:u,listeners:p,tag:i,children:r},c);return m}}}function mi(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;return o(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns),new e.componentOptions.Ctor(n)}function gi(e){for(var t=e.hook||(e.hook={}),n=0;n<hi.length;n++){var r=hi[n],i=t[r],s=pi[r];i===s||i&&i._merged||(t[r]=i?vi(s,i):s)}}function vi(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}function yi(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var s=t.on||(t.on={}),a=s[r],l=t.model.callback;o(a)?(i(a)?-1===a.indexOf(l):a!==l)&&(s[r]=[l].concat(a)):s[r]=l}var bi=j,wi=W.optionMergeStrategies;function xi(e,t,n){if(void 0===n&&(n=!0),!t)return e;for(var r,i,s,o=me?Reflect.ownKeys(t):Object.keys(t),a=0;a<o.length;a++)r=o[a],"__ob__"!==r&&(i=e[r],s=t[r],n&&C(e,r)?i!==s&&h(i)&&h(s)&&xi(i,s):Be(e,r,s));return e}function Ei(e,t,n){return n?function(){var r=u(t)?t.call(n,n):t,i=u(e)?e.call(n,n):e;return r?xi(r,i):i}:t?e?function(){return xi(u(t)?t.call(this,this):t,u(e)?e.call(this,this):e)}:t:e}function Si(e,t){var n=t?e?e.concat(t):i(t)?t:[t]:e;return n?Ci(n):n}function Ci(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}function Ti(e,t,n,r){var i=Object.create(e||null);return t?M(i,t):i}wi.data=function(e,t,n){return n?Ei(e,t,n):t&&"function"!==typeof t?e:Ei(e,t)},V.forEach(function(e){wi[e]=Si}),G.forEach(function(e){wi[e+"s"]=Ti}),wi.watch=function(e,t,n,r){if(e===le&&(e=void 0),t===le&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var s={};for(var o in M(s,e),t){var a=s[o],l=t[o];a&&!i(a)&&(a=[a]),s[o]=a?a.concat(l):i(l)?l:[l]}return s},wi.props=wi.methods=wi.inject=wi.computed=function(e,t,n,r){if(!e)return t;var i=Object.create(null);return M(i,e),t&&M(i,t),i},wi.provide=function(e,t){return e?function(){var n=Object.create(null);return xi(n,u(e)?e.call(this):e),t&&xi(n,u(t)?t.call(this):t,!1),n}:t};var ki=function(e,t){return void 0===t?e:t};function _i(e,t){var n=e.props;if(n){var r,s,o,a={};if(i(n)){r=n.length;while(r--)s=n[r],"string"===typeof s&&(o=_(s),a[o]={type:null})}else if(h(n))for(var l in n)s=n[l],o=_(l),a[o]=h(s)?s:{type:s};else 0;e.props=a}}function Oi(e,t){var n=e.inject;if(n){var r=e.inject={};if(i(n))for(var s=0;s<n.length;s++)r[n[s]]={from:n[s]};else if(h(n))for(var o in n){var a=n[o];r[o]=h(a)?M({from:o},a):{from:a}}else 0}}function $i(e){var t=e.directives;if(t)for(var n in t){var r=t[n];u(r)&&(t[n]={bind:r,update:r})}}function Ri(e,t,n){if(u(t)&&(t=t.options),_i(t,n),Oi(t,n),$i(t),!t._base&&(t.extends&&(e=Ri(e,t.extends,n)),t.mixins))for(var r=0,i=t.mixins.length;r<i;r++)e=Ri(e,t.mixins[r],n);var s,o={};for(s in e)a(s);for(s in t)C(e,s)||a(s);function a(r){var i=wi[r]||ki;o[r]=i(e[r],t[r],n,r)}return o}function Pi(e,t,n,r){if("string"===typeof n){var i=e[t];if(C(i,n))return i[n];var s=_(n);if(C(i,s))return i[s];var o=O(s);if(C(i,o))return i[o];var a=i[n]||i[s]||i[o];return a}}function Ai(e,t,n,r){var i=t[e],s=!C(n,e),o=n[e],a=ji(Boolean,i.type);if(a>-1)if(s&&!C(i,"default"))o=!1;else if(""===o||o===R(e)){var l=ji(String,i.type);(l<0||a<l)&&(o=!0)}if(void 0===o){o=Ii(r,i,e);var c=Me;Ne(!0),ze(o),Ne(c)}return o}function Ii(e,t,n){if(C(t,"default")){var r=t.default;return e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n]?e._props[n]:u(r)&&"Function"!==Mi(t.type)?r.call(e):r}}var Li=/^\s*function (\w+)/;function Mi(e){var t=e&&e.toString().match(Li);return t?t[1]:""}function Ni(e,t){return Mi(e)===Mi(t)}function ji(e,t){if(!i(t))return Ni(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(Ni(t[n],e))return n;return-1}var Di={enumerable:!0,configurable:!0,get:j,set:j};function zi(e,t,n){Di.get=function(){return this[t][n]},Di.set=function(e){this[t][n]=e},Object.defineProperty(e,n,Di)}function Fi(e){var t=e.$options;if(t.props&&Bi(e,t.props),mn(e),t.methods&&Xi(e,t.methods),t.data)Ui(e);else{var n=ze(e._data={});n&&n.vmCount++}t.computed&&Gi(e,t.computed),t.watch&&t.watch!==le&&Ki(e,t.watch)}function Bi(e,t){var n=e.$options.propsData||{},r=e._props=Ge({}),i=e.$options._propKeys=[],s=!e.$parent;s||Ne(!1);var o=function(s){i.push(s);var o=Ai(s,t,n,e);Fe(r,s,o,void 0,!0),s in e||zi(e,"_props",s)};for(var a in t)o(a);Ne(!0)}function Ui(e){var t=e.$options.data;t=e._data=u(t)?qi(t,e):t||{},h(t)||(t={});var n=Object.keys(t),r=e.$options.props,i=(e.$options.methods,n.length);while(i--){var s=n[i];0,r&&C(r,s)||X(s)||zi(e,"_data",s)}var o=ze(t);o&&o.vmCount++}function qi(e,t){Oe();try{return e.call(t,t)}catch(sl){return Fn(sl,t,"data()"),{}}finally{$e()}}var Hi={lazy:!0};function Gi(e,t){var n=e._computedWatchers=Object.create(null),r=de();for(var i in t){var s=t[i],o=u(s)?s:s.get;0,r||(n[i]=new Tr(e,o||j,j,Hi)),i in e||Vi(e,i,s)}}function Vi(e,t,n){var r=!de();u(n)?(Di.get=r?Wi(t):Yi(n),Di.set=j):(Di.get=n.get?r&&!1!==n.cache?Wi(t):Yi(n.get):j,Di.set=n.set||j),Object.defineProperty(e,t,Di)}function Wi(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ke.target&&t.depend(),t.value}}function Yi(e){return function(){return e.call(this,this)}}function Xi(e,t){e.$options.props;for(var n in t)e[n]="function"!==typeof t[n]?j:I(t[n],e)}function Ki(e,t){for(var n in t){var r=t[n];if(i(r))for(var s=0;s<r.length;s++)Ji(e,n,r[s]);else Ji(e,n,r)}}function Ji(e,t,n,r){return h(n)&&(r=n,n=n.handler),"string"===typeof n&&(n=e[n]),e.$watch(t,n,r)}function Zi(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=Be,e.prototype.$delete=Ue,e.prototype.$watch=function(e,t,n){var r=this;if(h(t))return Ji(r,e,t,n);n=n||{},n.user=!0;var i=new Tr(r,e,t,n);if(n.immediate){var s='callback for immediate watcher "'.concat(i.expression,'"');Oe(),Bn(t,r,[i.value],r,s),$e()}return function(){i.teardown()}}}var Qi=0;function es(e){e.prototype._init=function(e){var t=this;t._uid=Qi++,t._isVue=!0,t.__v_skip=!0,t._scope=new $t(!0),t._scope.parent=void 0,t._scope._vm=!0,e&&e._isComponent?ts(t,e):t.$options=Ri(ns(t.constructor),e||{},t),t._renderProxy=t,t._self=t,Lr(t),kr(t),kn(t),Br(t,"beforeCreate",void 0,!1),si(t),Fi(t),ii(t),Br(t,"created"),t.$options.el&&t.$mount(t.$options.el)}}function ts(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var i=r.componentOptions;n.propsData=i.propsData,n._parentListeners=i.listeners,n._renderChildren=i.children,n._componentTag=i.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}function ns(e){var t=e.options;if(e.super){var n=ns(e.super),r=e.superOptions;if(n!==r){e.superOptions=n;var i=rs(e);i&&M(e.extendOptions,i),t=e.options=Ri(n,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function rs(e){var t,n=e.options,r=e.sealedOptions;for(var i in n)n[i]!==r[i]&&(t||(t={}),t[i]=n[i]);return t}function is(e){this._init(e)}function ss(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=L(arguments,1);return n.unshift(this),u(e.install)?e.install.apply(e,n):u(e)&&e.apply(null,n),t.push(e),this}}function os(e){e.mixin=function(e){return this.options=Ri(this.options,e),this}}function as(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,i=e._Ctor||(e._Ctor={});if(i[r])return i[r];var s=di(e)||di(n.options);var o=function(e){this._init(e)};return o.prototype=Object.create(n.prototype),o.prototype.constructor=o,o.cid=t++,o.options=Ri(n.options,e),o["super"]=n,o.options.props&&ls(o),o.options.computed&&cs(o),o.extend=n.extend,o.mixin=n.mixin,o.use=n.use,G.forEach(function(e){o[e]=n[e]}),s&&(o.options.components[s]=o),o.superOptions=n.options,o.extendOptions=e,o.sealedOptions=M({},o.options),i[r]=o,o}}function ls(e){var t=e.options.props;for(var n in t)zi(e.prototype,"_props",n)}function cs(e){var t=e.options.computed;for(var n in t)Vi(e.prototype,n,t[n])}function us(e){G.forEach(function(t){e[t]=function(e,n){return n?("component"===t&&h(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&u(n)&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}})}function ds(e){return e&&(di(e.Ctor.options)||e.tag)}function ps(e,t){return i(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!f(e)&&e.test(t)}function hs(e,t){var n=e.cache,r=e.keys,i=e._vnode,s=e.$vnode;for(var o in n){var a=n[o];if(a){var l=a.name;l&&!t(l)&&fs(n,o,r,i)}}s.componentOptions.children=void 0}function fs(e,t,n,r){var i=e[t];!i||r&&i.tag===r.tag||i.componentInstance.$destroy(),e[t]=null,E(n,t)}es(is),Zi(is),Pr(is),Mr(is),On(is);var ms=[String,RegExp,Array],gs={name:"keep-alive",abstract:!0,props:{include:ms,exclude:ms,max:[String,Number]},methods:{cacheVNode:function(){var e=this,t=e.cache,n=e.keys,r=e.vnodeToCache,i=e.keyToCache;if(r){var s=r.tag,o=r.componentInstance,a=r.componentOptions;t[i]={name:ds(a),tag:s,componentInstance:o},n.push(i),this.max&&n.length>parseInt(this.max)&&fs(t,n[0],n,this._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)fs(this.cache,e,this.keys)},mounted:function(){var e=this;this.cacheVNode(),this.$watch("include",function(t){hs(e,function(e){return ps(t,e)})}),this.$watch("exclude",function(t){hs(e,function(e){return!ps(t,e)})})},updated:function(){this.cacheVNode()},render:function(){var e=this.$slots.default,t=An(e),n=t&&t.componentOptions;if(n){var r=ds(n),i=this,s=i.include,o=i.exclude;if(s&&(!r||!ps(s,r))||o&&r&&ps(o,r))return t;var a=this,l=a.cache,c=a.keys,u=null==t.key?n.Ctor.cid+(n.tag?"::".concat(n.tag):""):t.key;l[u]?(t.componentInstance=l[u].componentInstance,E(c,u),c.push(u)):(this.vnodeToCache=t,this.keyToCache=u),t.data.keepAlive=!0}return t||e&&e[0]}},vs={KeepAlive:gs};function ys(e){var t={get:function(){return W}};Object.defineProperty(e,"config",t),e.util={warn:bi,extend:M,mergeOptions:Ri,defineReactive:Fe},e.set=Be,e.delete=Ue,e.nextTick=Qn,e.observable=function(e){return ze(e),e},e.options=Object.create(null),G.forEach(function(t){e.options[t+"s"]=Object.create(null)}),e.options._base=e,M(e.options.components,vs),ss(e),os(e),as(e),us(e)}ys(is),Object.defineProperty(is.prototype,"$isServer",{get:de}),Object.defineProperty(is.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(is,"FunctionalRenderContext",{value:ai}),is.version=yr;var bs=w("style,class"),ws=w("input,textarea,option,select,progress"),xs=function(e,t,n){return"value"===n&&ws(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video"===e},Es=w("contenteditable,draggable,spellcheck"),Ss=w("events,caret,typing,plaintext-only"),Cs=function(e,t){return $s(t)||"false"===t?"false":"contenteditable"===e&&Ss(t)?t:"true"},Ts=w("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,truespeed,typemustmatch,visible"),ks="http://www.w3.org/1999/xlink",_s=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},Os=function(e){return _s(e)?e.slice(6,e.length):""},$s=function(e){return null==e||!1===e};function Rs(e){var t=e.data,n=e,r=e;while(o(r.componentInstance))r=r.componentInstance._vnode,r&&r.data&&(t=Ps(r.data,t));while(o(n=n.parent))n&&n.data&&(t=Ps(t,n.data));return As(t.staticClass,t.class)}function Ps(e,t){return{staticClass:Is(e.staticClass,t.staticClass),class:o(e.class)?[e.class,t.class]:t.class}}function As(e,t){return o(e)||o(t)?Is(e,Ls(t)):""}function Is(e,t){return e?t?e+" "+t:e:t||""}function Ls(e){return Array.isArray(e)?Ms(e):d(e)?Ns(e):"string"===typeof e?e:""}function Ms(e){for(var t,n="",r=0,i=e.length;r<i;r++)o(t=Ls(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}function Ns(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}var js={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Ds=w("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),zs=w("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Fs=function(e){return Ds(e)||zs(e)};function Bs(e){return zs(e)?"svg":"math"===e?"math":void 0}var Us=Object.create(null);function qs(e){if(!ee)return!0;if(Fs(e))return!1;if(e=e.toLowerCase(),null!=Us[e])return Us[e];var t=document.createElement(e);return e.indexOf("-")>-1?Us[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Us[e]=/HTMLUnknownElement/.test(t.toString())}var Hs=w("text,number,password,search,email,tel,url");function Gs(e){if("string"===typeof e){var t=document.querySelector(e);return t||document.createElement("div")}return e}function Vs(e,t){var n=document.createElement(e);return"select"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n}function Ws(e,t){return document.createElementNS(js[e],t)}function Ys(e){return document.createTextNode(e)}function Xs(e){return document.createComment(e)}function Ks(e,t,n){e.insertBefore(t,n)}function Js(e,t){e.removeChild(t)}function Zs(e,t){e.appendChild(t)}function Qs(e){return e.parentNode}function eo(e){return e.nextSibling}function to(e){return e.tagName}function no(e,t){e.textContent=t}function ro(e,t){e.setAttribute(t,"")}var io=Object.freeze({__proto__:null,createElement:Vs,createElementNS:Ws,createTextNode:Ys,createComment:Xs,insertBefore:Ks,removeChild:Js,appendChild:Zs,parentNode:Qs,nextSibling:eo,tagName:to,setTextContent:no,setStyleScope:ro}),so={create:function(e,t){oo(t)},update:function(e,t){e.data.ref!==t.data.ref&&(oo(e,!0),oo(t))},destroy:function(e){oo(e,!0)}};function oo(e,t){var n=e.data.ref;if(o(n)){var r=e.context,s=e.componentInstance||e.elm,a=t?null:s,l=t?void 0:s;if(u(n))Bn(n,r,[a],r,"template ref function");else{var c=e.data.refInFor,d="string"===typeof n||"number"===typeof n,p=et(n),h=r.$refs;if(d||p)if(c){var f=d?h[n]:n.value;t?i(f)&&E(f,s):i(f)?f.includes(s)||f.push(s):d?(h[n]=[s],ao(r,n,h[n])):n.value=[s]}else if(d){if(t&&h[n]!==s)return;h[n]=l,ao(r,n,a)}else if(p){if(t&&n.value!==s)return;n.value=a}else 0}}}function ao(e,t,n){var r=e._setupState;r&&C(r,t)&&(et(r[t])?r[t].value=n:r[t]=n)}var lo=new be("",{},[]),co=["create","activate","update","remove","destroy"];function uo(e,t){return e.key===t.key&&e.asyncFactory===t.asyncFactory&&(e.tag===t.tag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&po(e,t)||a(e.isAsyncPlaceholder)&&s(t.asyncFactory.error))}function po(e,t){if("input"!==e.tag)return!0;var n,r=o(n=e.data)&&o(n=n.attrs)&&n.type,i=o(n=t.data)&&o(n=n.attrs)&&n.type;return r===i||Hs(r)&&Hs(i)}function ho(e,t,n){var r,i,s={};for(r=t;r<=n;++r)i=e[r].key,o(i)&&(s[i]=r);return s}function fo(e){var t,n,r={},l=e.modules,u=e.nodeOps;for(t=0;t<co.length;++t)for(r[co[t]]=[],n=0;n<l.length;++n)o(l[n][co[t]])&&r[co[t]].push(l[n][co[t]]);function d(e){return new be(u.tagName(e).toLowerCase(),{},[],void 0,e)}function p(e,t){function n(){0===--n.listeners&&h(e)}return n.listeners=t,n}function h(e){var t=u.parentNode(e);o(t)&&u.removeChild(t,e)}function f(e,t,n,r,i,s,l){if(o(e.elm)&&o(s)&&(e=s[l]=Ee(e)),e.isRootInsert=!i,!m(e,t,n,r)){var c=e.data,d=e.children,p=e.tag;o(p)?(e.elm=e.ns?u.createElementNS(e.ns,p):u.createElement(p,e),S(e),b(e,d,t),o(c)&&E(e,t),y(n,e.elm,r)):a(e.isComment)?(e.elm=u.createComment(e.text),y(n,e.elm,r)):(e.elm=u.createTextNode(e.text),y(n,e.elm,r))}}function m(e,t,n,r){var i=e.data;if(o(i)){var s=o(e.componentInstance)&&i.keepAlive;if(o(i=i.hook)&&o(i=i.init)&&i(e,!1),o(e.componentInstance))return g(e,t),y(n,e.elm,r),a(s)&&v(e,t,n,r),!0}}function g(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,x(e)?(E(e,t),S(e)):(oo(e),t.push(e))}function v(e,t,n,i){var s,a=e;while(a.componentInstance)if(a=a.componentInstance._vnode,o(s=a.data)&&o(s=s.transition)){for(s=0;s<r.activate.length;++s)r.activate[s](lo,a);t.push(a);break}y(n,e.elm,i)}function y(e,t,n){o(e)&&(o(n)?u.parentNode(n)===e&&u.insertBefore(e,t,n):u.appendChild(e,t))}function b(e,t,n){if(i(t)){0;for(var r=0;r<t.length;++r)f(t[r],n,e.elm,null,!0,t,r)}else c(e.text)&&u.appendChild(e.elm,u.createTextNode(String(e.text)))}function x(e){while(e.componentInstance)e=e.componentInstance._vnode;return o(e.tag)}function E(e,n){for(var i=0;i<r.create.length;++i)r.create[i](lo,e);t=e.data.hook,o(t)&&(o(t.create)&&t.create(lo,e),o(t.insert)&&n.push(e))}function S(e){var t;if(o(t=e.fnScopeId))u.setStyleScope(e.elm,t);else{var n=e;while(n)o(t=n.context)&&o(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t),n=n.parent}o(t=Ar)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t)}function C(e,t,n,r,i,s){for(;r<=i;++r)f(n[r],s,e,t,!1,n,r)}function T(e){var t,n,i=e.data;if(o(i))for(o(t=i.hook)&&o(t=t.destroy)&&t(e),t=0;t<r.destroy.length;++t)r.destroy[t](e);if(o(t=e.children))for(n=0;n<e.children.length;++n)T(e.children[n])}function k(e,t,n){for(;t<=n;++t){var r=e[t];o(r)&&(o(r.tag)?(_(r),T(r)):h(r.elm))}}function _(e,t){if(o(t)||o(e.data)){var n,i=r.remove.length+1;for(o(t)?t.listeners+=i:t=p(e.elm,i),o(n=e.componentInstance)&&o(n=n._vnode)&&o(n.data)&&_(n,t),n=0;n<r.remove.length;++n)r.remove[n](e,t);o(n=e.data.hook)&&o(n=n.remove)?n(e,t):t()}else h(e.elm)}function O(e,t,n,r,i){var a,l,c,d,p=0,h=0,m=t.length-1,g=t[0],v=t[m],y=n.length-1,b=n[0],w=n[y],x=!i;while(p<=m&&h<=y)s(g)?g=t[++p]:s(v)?v=t[--m]:uo(g,b)?(R(g,b,r,n,h),g=t[++p],b=n[++h]):uo(v,w)?(R(v,w,r,n,y),v=t[--m],w=n[--y]):uo(g,w)?(R(g,w,r,n,y),x&&u.insertBefore(e,g.elm,u.nextSibling(v.elm)),g=t[++p],w=n[--y]):uo(v,b)?(R(v,b,r,n,h),x&&u.insertBefore(e,v.elm,g.elm),v=t[--m],b=n[++h]):(s(a)&&(a=ho(t,p,m)),l=o(b.key)?a[b.key]:$(b,t,p,m),s(l)?f(b,r,e,g.elm,!1,n,h):(c=t[l],uo(c,b)?(R(c,b,r,n,h),t[l]=void 0,x&&u.insertBefore(e,c.elm,g.elm)):f(b,r,e,g.elm,!1,n,h)),b=n[++h]);p>m?(d=s(n[y+1])?null:n[y+1].elm,C(e,d,n,h,y,r)):h>y&&k(t,p,m)}function $(e,t,n,r){for(var i=n;i<r;i++){var s=t[i];if(o(s)&&uo(e,s))return i}}function R(e,t,n,i,l,c){if(e!==t){o(t.elm)&&o(i)&&(t=i[l]=Ee(t));var d=t.elm=e.elm;if(a(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?I(e.elm,t,n):t.isAsyncPlaceholder=!0;else if(a(t.isStatic)&&a(e.isStatic)&&t.key===e.key&&(a(t.isCloned)||a(t.isOnce)))t.componentInstance=e.componentInstance;else{var p,h=t.data;o(h)&&o(p=h.hook)&&o(p=p.prepatch)&&p(e,t);var f=e.children,m=t.children;if(o(h)&&x(t)){for(p=0;p<r.update.length;++p)r.update[p](e,t);o(p=h.hook)&&o(p=p.update)&&p(e,t)}s(t.text)?o(f)&&o(m)?f!==m&&O(d,f,m,n,c):o(m)?(o(e.text)&&u.setTextContent(d,""),C(d,null,m,0,m.length-1,n)):o(f)?k(f,0,f.length-1):o(e.text)&&u.setTextContent(d,""):e.text!==t.text&&u.setTextContent(d,t.text),o(h)&&o(p=h.hook)&&o(p=p.postpatch)&&p(e,t)}}}function P(e,t,n){if(a(n)&&o(e.parent))e.parent.data.pendingInsert=t;else for(var r=0;r<t.length;++r)t[r].data.hook.insert(t[r])}var A=w("attrs,class,staticClass,staticStyle,key");function I(e,t,n,r){var i,s=t.tag,l=t.data,c=t.children;if(r=r||l&&l.pre,t.elm=e,a(t.isComment)&&o(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(o(l)&&(o(i=l.hook)&&o(i=i.init)&&i(t,!0),o(i=t.componentInstance)))return g(t,n),!0;if(o(s)){if(o(c))if(e.hasChildNodes())if(o(i=l)&&o(i=i.domProps)&&o(i=i.innerHTML)){if(i!==e.innerHTML)return!1}else{for(var u=!0,d=e.firstChild,p=0;p<c.length;p++){if(!d||!I(d,c[p],n,r)){u=!1;break}d=d.nextSibling}if(!u||d)return!1}else b(t,c,n);if(o(l)){var h=!1;for(var f in l)if(!A(f)){h=!0,E(t,n);break}!h&&l["class"]&&xr(l["class"])}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,i){if(!s(t)){var l=!1,c=[];if(s(e))l=!0,f(t,c);else{var p=o(e.nodeType);if(!p&&uo(e,t))R(e,t,c,null,null,i);else{if(p){if(1===e.nodeType&&e.hasAttribute(H)&&(e.removeAttribute(H),n=!0),a(n)&&I(e,t,c))return P(t,c,!0),e;e=d(e)}var h=e.elm,m=u.parentNode(h);if(f(t,c,h._leaveCb?null:m,u.nextSibling(h)),o(t.parent)){var g=t.parent,v=x(t);while(g){for(var y=0;y<r.destroy.length;++y)r.destroy[y](g);if(g.elm=t.elm,v){for(var b=0;b<r.create.length;++b)r.create[b](lo,g);var w=g.data.hook.insert;if(w.merged)for(var E=w.fns.slice(1),S=0;S<E.length;S++)E[S]()}else oo(g);g=g.parent}}o(m)?k([e],0,0):o(e.tag)&&T(e)}}return P(t,c,l),t.elm}o(e)&&T(e)}}var mo={create:go,update:go,destroy:function(e){go(e,lo)}};function go(e,t){(e.data.directives||t.data.directives)&&vo(e,t)}function vo(e,t){var n,r,i,s=e===lo,o=t===lo,a=bo(e.data.directives,e.context),l=bo(t.data.directives,t.context),c=[],u=[];for(n in l)r=a[n],i=l[n],r?(i.oldValue=r.value,i.oldArg=r.arg,xo(i,"update",t,e),i.def&&i.def.componentUpdated&&u.push(i)):(xo(i,"bind",t,e),i.def&&i.def.inserted&&c.push(i));if(c.length){var d=function(){for(var n=0;n<c.length;n++)xo(c[n],"inserted",t,e)};s?Ft(t,"insert",d):d()}if(u.length&&Ft(t,"postpatch",function(){for(var n=0;n<u.length;n++)xo(u[n],"componentUpdated",t,e)}),!s)for(n in a)l[n]||xo(a[n],"unbind",e,e,o)}var yo=Object.create(null);function bo(e,t){var n,r,i=Object.create(null);if(!e)return i;for(n=0;n<e.length;n++){if(r=e[n],r.modifiers||(r.modifiers=yo),i[wo(r)]=r,t._setupState&&t._setupState.__sfc){var s=r.def||Pi(t,"_setupState","v-"+r.name);r.def="function"===typeof s?{bind:s,update:s}:s}r.def=r.def||Pi(t.$options,"directives",r.name,!0)}return i}function wo(e){return e.rawName||"".concat(e.name,".").concat(Object.keys(e.modifiers||{}).join("."))}function xo(e,t,n,r,i){var s=e.def&&e.def[t];if(s)try{s(n.elm,e,n,r,i)}catch(sl){Fn(sl,n.context,"directive ".concat(e.name," ").concat(t," hook"))}}var Eo=[so,mo];function So(e,t){var n=t.componentOptions;if((!o(n)||!1!==n.Ctor.options.inheritAttrs)&&(!s(e.data.attrs)||!s(t.data.attrs))){var r,i,l,c=t.elm,u=e.data.attrs||{},d=t.data.attrs||{};for(r in(o(d.__ob__)||a(d._v_attr_proxy))&&(d=t.data.attrs=M({},d)),d)i=d[r],l=u[r],l!==i&&Co(c,r,i,t.data.pre);for(r in(ne||ie)&&d.value!==u.value&&Co(c,"value",d.value),u)s(d[r])&&(_s(r)?c.removeAttributeNS(ks,Os(r)):Es(r)||c.removeAttribute(r))}}function Co(e,t,n,r){r||e.tagName.indexOf("-")>-1?To(e,t,n):Ts(t)?$s(n)?e.removeAttribute(t):(n="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):Es(t)?e.setAttribute(t,Cs(t,n)):_s(t)?$s(n)?e.removeAttributeNS(ks,Os(t)):e.setAttributeNS(ks,t,n):To(e,t,n)}function To(e,t,n){if($s(n))e.removeAttribute(t);else{if(ne&&!re&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){var r=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",r)};e.addEventListener("input",r),e.__ieph=!0}e.setAttribute(t,n)}}var ko={create:So,update:So};function _o(e,t){var n=t.elm,r=t.data,i=e.data;if(!(s(r.staticClass)&&s(r.class)&&(s(i)||s(i.staticClass)&&s(i.class)))){var a=Rs(t),l=n._transitionClasses;o(l)&&(a=Is(a,Ls(l))),a!==n._prevClass&&(n.setAttribute("class",a),n._prevClass=a)}}var Oo,$o={create:_o,update:_o},Ro="__r",Po="__c";function Ao(e){if(o(e[Ro])){var t=ne?"change":"input";e[t]=[].concat(e[Ro],e[t]||[]),delete e[Ro]}o(e[Po])&&(e.change=[].concat(e[Po],e.change||[]),delete e[Po])}function Io(e,t,n){var r=Oo;return function i(){var s=t.apply(null,arguments);null!==s&&No(e,i,n,r)}}var Lo=Gn&&!(ae&&Number(ae[1])<=53);function Mo(e,t,n,r){if(Lo){var i=Xr,s=t;t=s._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=i||e.timeStamp<=0||e.target.ownerDocument!==document)return s.apply(this,arguments)}}Oo.addEventListener(e,t,ce?{capture:n,passive:r}:n)}function No(e,t,n,r){(r||Oo).removeEventListener(e,t._wrapper||t,n)}function jo(e,t){if(!s(e.data.on)||!s(t.data.on)){var n=t.data.on||{},r=e.data.on||{};Oo=t.elm||e.elm,Ao(n),zt(n,r,Mo,No,Io,t.context),Oo=void 0}}var Do,zo={create:jo,update:jo,destroy:function(e){return jo(e,lo)}};function Fo(e,t){if(!s(e.data.domProps)||!s(t.data.domProps)){var n,r,i=t.elm,l=e.data.domProps||{},c=t.data.domProps||{};for(n in(o(c.__ob__)||a(c._v_attr_proxy))&&(c=t.data.domProps=M({},c)),l)n in c||(i[n]="");for(n in c){if(r=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),r===l[n])continue;1===i.childNodes.length&&i.removeChild(i.childNodes[0])}if("value"===n&&"PROGRESS"!==i.tagName){i._value=r;var u=s(r)?"":String(r);Bo(i,u)&&(i.value=u)}else if("innerHTML"===n&&zs(i.tagName)&&s(i.innerHTML)){Do=Do||document.createElement("div"),Do.innerHTML="<svg>".concat(r,"</svg>");var d=Do.firstChild;while(i.firstChild)i.removeChild(i.firstChild);while(d.firstChild)i.appendChild(d.firstChild)}else if(r!==l[n])try{i[n]=r}catch(sl){}}}}function Bo(e,t){return!e.composing&&("OPTION"===e.tagName||Uo(e,t)||qo(e,t))}function Uo(e,t){var n=!0;try{n=document.activeElement!==e}catch(sl){}return n&&e.value!==t}function qo(e,t){var n=e.value,r=e._vModifiers;if(o(r)){if(r.number)return b(n)!==b(t);if(r.trim)return n.trim()!==t.trim()}return n!==t}var Ho={create:Fo,update:Fo},Go=T(function(e){var t={},n=/;(?![^(]*\))/g,r=/:(.+)/;return e.split(n).forEach(function(e){if(e){var n=e.split(r);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t});function Vo(e){var t=Wo(e.style);return e.staticStyle?M(e.staticStyle,t):t}function Wo(e){return Array.isArray(e)?N(e):"string"===typeof e?Go(e):e}function Yo(e,t){var n,r={};if(t){var i=e;while(i.componentInstance)i=i.componentInstance._vnode,i&&i.data&&(n=Vo(i.data))&&M(r,n)}(n=Vo(e.data))&&M(r,n);var s=e;while(s=s.parent)s.data&&(n=Vo(s.data))&&M(r,n);return r}var Xo,Ko=/^--/,Jo=/\s*!important$/,Zo=function(e,t,n){if(Ko.test(t))e.style.setProperty(t,n);else if(Jo.test(n))e.style.setProperty(R(t),n.replace(Jo,""),"important");else{var r=ea(t);if(Array.isArray(n))for(var i=0,s=n.length;i<s;i++)e.style[r]=n[i];else e.style[r]=n}},Qo=["Webkit","Moz","ms"],ea=T(function(e){if(Xo=Xo||document.createElement("div").style,e=_(e),"filter"!==e&&e in Xo)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<Qo.length;n++){var r=Qo[n]+t;if(r in Xo)return r}});function ta(e,t){var n=t.data,r=e.data;if(!(s(n.staticStyle)&&s(n.style)&&s(r.staticStyle)&&s(r.style))){var i,a,l=t.elm,c=r.staticStyle,u=r.normalizedStyle||r.style||{},d=c||u,p=Wo(t.data.style)||{};t.data.normalizedStyle=o(p.__ob__)?M({},p):p;var h=Yo(t,!0);for(a in d)s(h[a])&&Zo(l,a,"");for(a in h)i=h[a],Zo(l,a,null==i?"":i)}}var na={create:ta,update:ta},ra=/\s+/;function ia(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(ra).forEach(function(t){return e.classList.add(t)}):e.classList.add(t);else{var n=" ".concat(e.getAttribute("class")||""," ");n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function sa(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(ra).forEach(function(t){return e.classList.remove(t)}):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{var n=" ".concat(e.getAttribute("class")||""," "),r=" "+t+" ";while(n.indexOf(r)>=0)n=n.replace(r," ");n=n.trim(),n?e.setAttribute("class",n):e.removeAttribute("class")}}function oa(e){if(e){if("object"===typeof e){var t={};return!1!==e.css&&M(t,aa(e.name||"v")),M(t,e),t}return"string"===typeof e?aa(e):void 0}}var aa=T(function(e){return{enterClass:"".concat(e,"-enter"),enterToClass:"".concat(e,"-enter-to"),enterActiveClass:"".concat(e,"-enter-active"),leaveClass:"".concat(e,"-leave"),leaveToClass:"".concat(e,"-leave-to"),leaveActiveClass:"".concat(e,"-leave-active")}}),la=ee&&!re,ca="transition",ua="animation",da="transition",pa="transitionend",ha="animation",fa="animationend";la&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(da="WebkitTransition",pa="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(ha="WebkitAnimation",fa="webkitAnimationEnd"));var ma=ee?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function ga(e){ma(function(){ma(e)})}function va(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),ia(e,t))}function ya(e,t){e._transitionClasses&&E(e._transitionClasses,t),sa(e,t)}function ba(e,t,n){var r=xa(e,t),i=r.type,s=r.timeout,o=r.propCount;if(!i)return n();var a=i===ca?pa:fa,l=0,c=function(){e.removeEventListener(a,u),n()},u=function(t){t.target===e&&++l>=o&&c()};setTimeout(function(){l<o&&c()},s+1),e.addEventListener(a,u)}var wa=/\b(transform|all)(,|$)/;function xa(e,t){var n,r=window.getComputedStyle(e),i=(r[da+"Delay"]||"").split(", "),s=(r[da+"Duration"]||"").split(", "),o=Ea(i,s),a=(r[ha+"Delay"]||"").split(", "),l=(r[ha+"Duration"]||"").split(", "),c=Ea(a,l),u=0,d=0;t===ca?o>0&&(n=ca,u=o,d=s.length):t===ua?c>0&&(n=ua,u=c,d=l.length):(u=Math.max(o,c),n=u>0?o>c?ca:ua:null,d=n?n===ca?s.length:l.length:0);var p=n===ca&&wa.test(r[da+"Property"]);return{type:n,timeout:u,propCount:d,hasTransform:p}}function Ea(e,t){while(e.length<t.length)e=e.concat(e);return Math.max.apply(null,t.map(function(t,n){return Sa(t)+Sa(e[n])}))}function Sa(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function Ca(e,t){var n=e.elm;o(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var r=oa(e.data.transition);if(!s(r)&&!o(n._enterCb)&&1===n.nodeType){var i=r.css,a=r.type,l=r.enterClass,c=r.enterToClass,p=r.enterActiveClass,h=r.appearClass,f=r.appearToClass,m=r.appearActiveClass,g=r.beforeEnter,v=r.enter,y=r.afterEnter,w=r.enterCancelled,x=r.beforeAppear,E=r.appear,S=r.afterAppear,C=r.appearCancelled,T=r.duration,k=Ar,_=Ar.$vnode;while(_&&_.parent)k=_.context,_=_.parent;var O=!k._isMounted||!e.isRootInsert;if(!O||E||""===E){var $=O&&h?h:l,R=O&&m?m:p,P=O&&f?f:c,A=O&&x||g,I=O&&u(E)?E:v,L=O&&S||y,M=O&&C||w,N=b(d(T)?T.enter:T);0;var j=!1!==i&&!re,D=_a(I),z=n._enterCb=U(function(){j&&(ya(n,P),ya(n,R)),z.cancelled?(j&&ya(n,$),M&&M(n)):L&&L(n),n._enterCb=null});e.data.show||Ft(e,"insert",function(){var t=n.parentNode,r=t&&t._pending&&t._pending[e.key];r&&r.tag===e.tag&&r.elm._leaveCb&&r.elm._leaveCb(),I&&I(n,z)}),A&&A(n),j&&(va(n,$),va(n,R),ga(function(){ya(n,$),z.cancelled||(va(n,P),D||(ka(N)?setTimeout(z,N):ba(n,a,z)))})),e.data.show&&(t&&t(),I&&I(n,z)),j||D||z()}}}function Ta(e,t){var n=e.elm;o(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb());var r=oa(e.data.transition);if(s(r)||1!==n.nodeType)return t();if(!o(n._leaveCb)){var i=r.css,a=r.type,l=r.leaveClass,c=r.leaveToClass,u=r.leaveActiveClass,p=r.beforeLeave,h=r.leave,f=r.afterLeave,m=r.leaveCancelled,g=r.delayLeave,v=r.duration,y=!1!==i&&!re,w=_a(h),x=b(d(v)?v.leave:v);0;var E=n._leaveCb=U(function(){n.parentNode&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),y&&(ya(n,c),ya(n,u)),E.cancelled?(y&&ya(n,l),m&&m(n)):(t(),f&&f(n)),n._leaveCb=null});g?g(S):S()}function S(){E.cancelled||(!e.data.show&&n.parentNode&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),p&&p(n),y&&(va(n,l),va(n,u),ga(function(){ya(n,l),E.cancelled||(va(n,c),w||(ka(x)?setTimeout(E,x):ba(n,a,E)))})),h&&h(n,E),y||w||E())}}function ka(e){return"number"===typeof e&&!isNaN(e)}function _a(e){if(s(e))return!1;var t=e.fns;return o(t)?_a(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function Oa(e,t){!0!==t.data.show&&Ca(t)}var $a=ee?{create:Oa,activate:Oa,remove:function(e,t){!0!==e.data.show?Ta(e,t):t()}}:{},Ra=[ko,$o,zo,Ho,na,$a],Pa=Ra.concat(Eo),Aa=fo({nodeOps:io,modules:Pa});re&&document.addEventListener("selectionchange",function(){var e=document.activeElement;e&&e.vmodel&&Fa(e,"input")});var Ia={inserted:function(e,t,n,r){"select"===n.tag?(r.elm&&!r.elm._vOptions?Ft(n,"postpatch",function(){Ia.componentUpdated(e,t,n)}):La(e,t,n.context),e._vOptions=[].map.call(e.options,ja)):("textarea"===n.tag||Hs(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",Da),e.addEventListener("compositionend",za),e.addEventListener("change",za),re&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){La(e,t,n.context);var r=e._vOptions,i=e._vOptions=[].map.call(e.options,ja);if(i.some(function(e,t){return!F(e,r[t])})){var s=e.multiple?t.value.some(function(e){return Na(e,i)}):t.value!==t.oldValue&&Na(t.value,i);s&&Fa(e,"change")}}}};function La(e,t,n){Ma(e,t,n),(ne||ie)&&setTimeout(function(){Ma(e,t,n)},0)}function Ma(e,t,n){var r=t.value,i=e.multiple;if(!i||Array.isArray(r)){for(var s,o,a=0,l=e.options.length;a<l;a++)if(o=e.options[a],i)s=B(r,ja(o))>-1,o.selected!==s&&(o.selected=s);else if(F(ja(o),r))return void(e.selectedIndex!==a&&(e.selectedIndex=a));i||(e.selectedIndex=-1)}}function Na(e,t){return t.every(function(t){return!F(t,e)})}function ja(e){return"_value"in e?e._value:e.value}function Da(e){e.target.composing=!0}function za(e){e.target.composing&&(e.target.composing=!1,Fa(e.target,"input"))}function Fa(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)}function Ba(e){return!e.componentInstance||e.data&&e.data.transition?e:Ba(e.componentInstance._vnode)}var Ua={bind:function(e,t,n){var r=t.value;n=Ba(n);var i=n.data&&n.data.transition,s=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;r&&i?(n.data.show=!0,Ca(n,function(){e.style.display=s})):e.style.display=r?s:"none"},update:function(e,t,n){var r=t.value,i=t.oldValue;if(!r!==!i){n=Ba(n);var s=n.data&&n.data.transition;s?(n.data.show=!0,r?Ca(n,function(){e.style.display=e.__vOriginalDisplay}):Ta(n,function(){e.style.display="none"})):e.style.display=r?e.__vOriginalDisplay:"none"}},unbind:function(e,t,n,r,i){i||(e.style.display=e.__vOriginalDisplay)}},qa={model:Ia,show:Ua},Ha={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function Ga(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?Ga(An(t.children)):e}function Va(e){var t={},n=e.$options;for(var r in n.propsData)t[r]=e[r];var i=n._parentListeners;for(var r in i)t[_(r)]=i[r];return t}function Wa(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}function Ya(e){while(e=e.parent)if(e.data.transition)return!0}function Xa(e,t){return t.key===e.key&&t.tag===e.tag}var Ka=function(e){return e.tag||dn(e)},Ja=function(e){return"show"===e.name},Za={name:"transition",props:Ha,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&&(n=n.filter(Ka),n.length)){0;var r=this.mode;0;var i=n[0];if(Ya(this.$vnode))return i;var s=Ga(i);if(!s)return i;if(this._leaving)return Wa(e,i);var o="__transition-".concat(this._uid,"-");s.key=null==s.key?s.isComment?o+"comment":o+s.tag:c(s.key)?0===String(s.key).indexOf(o)?s.key:o+s.key:s.key;var a=(s.data||(s.data={})).transition=Va(this),l=this._vnode,u=Ga(l);if(s.data.directives&&s.data.directives.some(Ja)&&(s.data.show=!0),u&&u.data&&!Xa(s,u)&&!dn(u)&&(!u.componentInstance||!u.componentInstance._vnode.isComment)){var d=u.data.transition=M({},a);if("out-in"===r)return this._leaving=!0,Ft(d,"afterLeave",function(){t._leaving=!1,t.$forceUpdate()}),Wa(e,i);if("in-out"===r){if(dn(s))return l;var p,h=function(){p()};Ft(a,"afterEnter",h),Ft(a,"enterCancelled",h),Ft(d,"delayLeave",function(e){p=e})}}return i}}},Qa=M({tag:String,moveClass:String},Ha);delete Qa.mode;var el={props:Qa,beforeMount:function(){var e=this,t=this._update;this._update=function(n,r){var i=Ir(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,i(),t.call(e,n,r)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),r=this.prevChildren=this.children,i=this.$slots.default||[],s=this.children=[],o=Va(this),a=0;a<i.length;a++){var l=i[a];if(l.tag)if(null!=l.key&&0!==String(l.key).indexOf("__vlist"))s.push(l),n[l.key]=l,(l.data||(l.data={})).transition=o;else;}if(r){var c=[],u=[];for(a=0;a<r.length;a++){l=r[a];l.data.transition=o,l.data.pos=l.elm.getBoundingClientRect(),n[l.key]?c.push(l):u.push(l)}this.kept=e(t,null,c),this.removed=u}return e(t,null,s)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(tl),e.forEach(nl),e.forEach(rl),this._reflow=document.body.offsetHeight,e.forEach(function(e){if(e.data.moved){var n=e.elm,r=n.style;va(n,t),r.transform=r.WebkitTransform=r.transitionDuration="",n.addEventListener(pa,n._moveCb=function e(r){r&&r.target!==n||r&&!/transform$/.test(r.propertyName)||(n.removeEventListener(pa,e),n._moveCb=null,ya(n,t))})}}))},methods:{hasMove:function(e,t){if(!la)return!1;if(this._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach(function(e){sa(n,e)}),ia(n,t),n.style.display="none",this.$el.appendChild(n);var r=xa(n);return this.$el.removeChild(n),this._hasMove=r.hasTransform}}};function tl(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function nl(e){e.data.newPos=e.elm.getBoundingClientRect()}function rl(e){var t=e.data.pos,n=e.data.newPos,r=t.left-n.left,i=t.top-n.top;if(r||i){e.data.moved=!0;var s=e.elm.style;s.transform=s.WebkitTransform="translate(".concat(r,"px,").concat(i,"px)"),s.transitionDuration="0s"}}var il={Transition:Za,TransitionGroup:el};is.config.mustUseProp=xs,is.config.isReservedTag=Fs,is.config.isReservedAttr=bs,is.config.getTagNamespace=Bs,is.config.isUnknownElement=qs,M(is.options.directives,qa),M(is.options.components,il),is.prototype.__patch__=ee?Aa:j,is.prototype.$mount=function(e,t){return e=e&&ee?Gs(e):void 0,Nr(this,e,t)},ee&&setTimeout(function(){W.devtools&&pe&&pe.emit("init",is)},0)},5709:e=>{function t(e){return e&&e.__esModule?e:{default:e}}e.exports=t},5719:(e,t,n)=>{"use strict";var r,i,s;n.d(t,{ij:()=>pe}),function(e){e["STRING"]="string",e["NUMBER"]="number",e["INTEGER"]="integer",e["BOOLEAN"]="boolean",e["ARRAY"]="array",e["OBJECT"]="object"}(r||(r={})),function(e){e["LANGUAGE_UNSPECIFIED"]="language_unspecified",e["PYTHON"]="python"}(i||(i={})),function(e){e["OUTCOME_UNSPECIFIED"]="outcome_unspecified",e["OUTCOME_OK"]="outcome_ok",e["OUTCOME_FAILED"]="outcome_failed",e["OUTCOME_DEADLINE_EXCEEDED"]="outcome_deadline_exceeded"}(s||(s={}));
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const o=["user","model","function","system"];var a,l,c,u,d,p,h,f;(function(e){e["HARM_CATEGORY_UNSPECIFIED"]="HARM_CATEGORY_UNSPECIFIED",e["HARM_CATEGORY_HATE_SPEECH"]="HARM_CATEGORY_HATE_SPEECH",e["HARM_CATEGORY_SEXUALLY_EXPLICIT"]="HARM_CATEGORY_SEXUALLY_EXPLICIT",e["HARM_CATEGORY_HARASSMENT"]="HARM_CATEGORY_HARASSMENT",e["HARM_CATEGORY_DANGEROUS_CONTENT"]="HARM_CATEGORY_DANGEROUS_CONTENT",e["HARM_CATEGORY_CIVIC_INTEGRITY"]="HARM_CATEGORY_CIVIC_INTEGRITY"})(a||(a={})),function(e){e["HARM_BLOCK_THRESHOLD_UNSPECIFIED"]="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e["BLOCK_LOW_AND_ABOVE"]="BLOCK_LOW_AND_ABOVE",e["BLOCK_MEDIUM_AND_ABOVE"]="BLOCK_MEDIUM_AND_ABOVE",e["BLOCK_ONLY_HIGH"]="BLOCK_ONLY_HIGH",e["BLOCK_NONE"]="BLOCK_NONE"}(l||(l={})),function(e){e["HARM_PROBABILITY_UNSPECIFIED"]="HARM_PROBABILITY_UNSPECIFIED",e["NEGLIGIBLE"]="NEGLIGIBLE",e["LOW"]="LOW",e["MEDIUM"]="MEDIUM",e["HIGH"]="HIGH"}(c||(c={})),function(e){e["BLOCKED_REASON_UNSPECIFIED"]="BLOCKED_REASON_UNSPECIFIED",e["SAFETY"]="SAFETY",e["OTHER"]="OTHER"}(u||(u={})),function(e){e["FINISH_REASON_UNSPECIFIED"]="FINISH_REASON_UNSPECIFIED",e["STOP"]="STOP",e["MAX_TOKENS"]="MAX_TOKENS",e["SAFETY"]="SAFETY",e["RECITATION"]="RECITATION",e["LANGUAGE"]="LANGUAGE",e["BLOCKLIST"]="BLOCKLIST",e["PROHIBITED_CONTENT"]="PROHIBITED_CONTENT",e["SPII"]="SPII",e["MALFORMED_FUNCTION_CALL"]="MALFORMED_FUNCTION_CALL",e["OTHER"]="OTHER"}(d||(d={})),function(e){e["TASK_TYPE_UNSPECIFIED"]="TASK_TYPE_UNSPECIFIED",e["RETRIEVAL_QUERY"]="RETRIEVAL_QUERY",e["RETRIEVAL_DOCUMENT"]="RETRIEVAL_DOCUMENT",e["SEMANTIC_SIMILARITY"]="SEMANTIC_SIMILARITY",e["CLASSIFICATION"]="CLASSIFICATION",e["CLUSTERING"]="CLUSTERING"}(p||(p={})),function(e){e["MODE_UNSPECIFIED"]="MODE_UNSPECIFIED",e["AUTO"]="AUTO",e["ANY"]="ANY",e["NONE"]="NONE"}(h||(h={})),function(e){e["MODE_UNSPECIFIED"]="MODE_UNSPECIFIED",e["MODE_DYNAMIC"]="MODE_DYNAMIC"}(f||(f={}));
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class m extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class g extends m{constructor(e,t){super(e),this.response=t}}class v extends m{constructor(e,t,n,r){super(e),this.status=t,this.statusText=n,this.errorDetails=r}}class y extends m{}class b extends m{}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const w="https://generativelanguage.googleapis.com",x="v1beta",E="0.24.1",S="genai-js";var C;(function(e){e["GENERATE_CONTENT"]="generateContent",e["STREAM_GENERATE_CONTENT"]="streamGenerateContent",e["COUNT_TOKENS"]="countTokens",e["EMBED_CONTENT"]="embedContent",e["BATCH_EMBED_CONTENTS"]="batchEmbedContents"})(C||(C={}));class T{constructor(e,t,n,r,i){this.model=e,this.task=t,this.apiKey=n,this.stream=r,this.requestOptions=i}toString(){var e,t;const n=(null===(e=this.requestOptions)||void 0===e?void 0:e.apiVersion)||x,r=(null===(t=this.requestOptions)||void 0===t?void 0:t.baseUrl)||w;let i=`${r}/${n}/${this.model}:${this.task}`;return this.stream&&(i+="?alt=sse"),i}}function k(e){const t=[];return(null===e||void 0===e?void 0:e.apiClient)&&t.push(e.apiClient),t.push(`${S}/${E}`),t.join(" ")}async function _(e){var t;const n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",k(e.requestOptions)),n.append("x-goog-api-key",e.apiKey);let r=null===(t=e.requestOptions)||void 0===t?void 0:t.customHeaders;if(r){if(!(r instanceof Headers))try{r=new Headers(r)}catch(i){throw new y(`unable to convert customHeaders value ${JSON.stringify(r)} to Headers: ${i.message}`)}for(const[e,t]of r.entries()){if("x-goog-api-key"===e)throw new y(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new y(`Header name ${e} can only be set using the apiClient field`);n.append(e,t)}}return n}async function O(e,t,n,r,i,s){const o=new T(e,t,n,r,s);return{url:o.toString(),fetchOptions:Object.assign(Object.assign({},I(s)),{method:"POST",headers:await _(o),body:i})}}async function $(e,t,n,r,i,s={},o=fetch){const{url:a,fetchOptions:l}=await O(e,t,n,r,i,s);return R(a,l,o)}async function R(e,t,n=fetch){let r;try{r=await n(e,t)}catch(i){P(i,e)}return r.ok||await A(r,e),r}function P(e,t){let n=e;throw"AbortError"===n.name?(n=new b(`Request aborted when fetching ${t.toString()}: ${e.message}`),n.stack=e.stack):e instanceof v||e instanceof y||(n=new m(`Error fetching from ${t.toString()}: ${e.message}`),n.stack=e.stack),n}async function A(e,t){let n,r="";try{const t=await e.json();r=t.error.message,t.error.details&&(r+=` ${JSON.stringify(t.error.details)}`,n=t.error.details)}catch(i){}throw new v(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${r}`,e.status,e.statusText,n)}function I(e){const t={};if(void 0!==(null===e||void 0===e?void 0:e.signal)||(null===e||void 0===e?void 0:e.timeout)>=0){const n=new AbortController;(null===e||void 0===e?void 0:e.timeout)>=0&&setTimeout(()=>n.abort(),e.timeout),(null===e||void 0===e?void 0:e.signal)&&e.signal.addEventListener("abort",()=>{n.abort()}),t.signal=n.signal}return t}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function L(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),D(e.candidates[0]))throw new g(`${z(e)}`,e);return M(e)}if(e.promptFeedback)throw new g(`Text not available. ${z(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),D(e.candidates[0]))throw new g(`${z(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),N(e)[0]}if(e.promptFeedback)throw new g(`Function call not available. ${z(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),D(e.candidates[0]))throw new g(`${z(e)}`,e);return N(e)}if(e.promptFeedback)throw new g(`Function call not available. ${z(e)}`,e)},e}function M(e){var t,n,r,i;const s=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const o of null===(i=null===(r=e.candidates)||void 0===r?void 0:r[0].content)||void 0===i?void 0:i.parts)o.text&&s.push(o.text),o.executableCode&&s.push("\n```"+o.executableCode.language+"\n"+o.executableCode.code+"\n```\n"),o.codeExecutionResult&&s.push("\n```\n"+o.codeExecutionResult.output+"\n```\n");return s.length>0?s.join(""):""}function N(e){var t,n,r,i;const s=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const o of null===(i=null===(r=e.candidates)||void 0===r?void 0:r[0].content)||void 0===i?void 0:i.parts)o.functionCall&&s.push(o.functionCall);return s.length>0?s:void 0}const j=[d.RECITATION,d.SAFETY,d.LANGUAGE];function D(e){return!!e.finishReason&&j.includes(e.finishReason)}function z(e){var t,n,r;let i="";if(e.candidates&&0!==e.candidates.length||!e.promptFeedback){if(null===(r=e.candidates)||void 0===r?void 0:r[0]){const t=e.candidates[0];D(t)&&(i+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(i+=`: ${t.finishMessage}`))}}else i+="Response was blocked",(null===(t=e.promptFeedback)||void 0===t?void 0:t.blockReason)&&(i+=` due to ${e.promptFeedback.blockReason}`),(null===(n=e.promptFeedback)||void 0===n?void 0:n.blockReasonMessage)&&(i+=`: ${e.promptFeedback.blockReasonMessage}`);return i}function F(e){return this instanceof F?(this.v=e,this):new F(e)}function B(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),s=[];return r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r;function o(e){i[e]&&(r[e]=function(t){return new Promise(function(n,r){s.push([e,t,n,r])>1||a(e,t)})})}function a(e,t){try{l(i[e](t))}catch(n){d(s[0][3],n)}}function l(e){e.value instanceof F?Promise.resolve(e.value.v).then(c,u):d(s[0][2],e)}function c(e){a("next",e)}function u(e){a("throw",e)}function d(e,t){e(t),s.shift(),s.length&&a(s[0][0],s[0][1])}}"function"===typeof SuppressedError&&SuppressedError;
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const U=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function q(e){const t=e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0})),n=V(t),[r,i]=n.tee();return{stream:G(r),response:H(i)}}async function H(e){const t=[],n=e.getReader();while(1){const{done:e,value:r}=await n.read();if(e)return L(W(t));t.push(r)}}function G(e){return B(this,arguments,function*(){const t=e.getReader();while(1){const{value:e,done:n}=yield F(t.read());if(n)break;yield yield F(L(e))}})}function V(e){const t=e.getReader(),n=new ReadableStream({start(e){let n="";return r();function r(){return t.read().then(({value:t,done:i})=>{if(i)return n.trim()?void e.error(new m("Failed to parse stream")):void e.close();n+=t;let s,o=n.match(U);while(o){try{s=JSON.parse(o[1])}catch(a){return void e.error(new m(`Error parsing JSON response: "${o[1]}"`))}e.enqueue(s),n=n.substring(o[0].length),o=n.match(U)}return r()}).catch(e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new b("Request aborted when reading from the stream"):new m("Error reading from the stream"),t})}}});return n}function W(e){const t=e[e.length-1],n={promptFeedback:null===t||void 0===t?void 0:t.promptFeedback};for(const r of e){if(r.candidates){let e=0;for(const t of r.candidates)if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:e}),n.candidates[e].citationMetadata=t.citationMetadata,n.candidates[e].groundingMetadata=t.groundingMetadata,n.candidates[e].finishReason=t.finishReason,n.candidates[e].finishMessage=t.finishMessage,n.candidates[e].safetyRatings=t.safetyRatings,t.content&&t.content.parts){n.candidates[e].content||(n.candidates[e].content={role:t.content.role||"user",parts:[]});const r={};for(const i of t.content.parts)i.text&&(r.text=i.text),i.functionCall&&(r.functionCall=i.functionCall),i.executableCode&&(r.executableCode=i.executableCode),i.codeExecutionResult&&(r.codeExecutionResult=i.codeExecutionResult),0===Object.keys(r).length&&(r.text=""),n.candidates[e].content.parts.push(r)}e++}r.usageMetadata&&(n.usageMetadata=r.usageMetadata)}return n}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Y(e,t,n,r){const i=await $(t,C.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),r);return q(i)}async function X(e,t,n,r){const i=await $(t,C.GENERATE_CONTENT,e,!1,JSON.stringify(n),r),s=await i.json(),o=L(s);return{response:o}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function K(e){if(null!=e)return"string"===typeof e?{role:"system",parts:[{text:e}]}:e.text?{role:"system",parts:[e]}:e.parts?e.role?e:{role:"system",parts:e.parts}:void 0}function J(e){let t=[];if("string"===typeof e)t=[{text:e}];else for(const n of e)"string"===typeof n?t.push({text:n}):t.push(n);return Z(t)}function Z(e){const t={role:"user",parts:[]},n={role:"function",parts:[]};let r=!1,i=!1;for(const s of e)"functionResponse"in s?(n.parts.push(s),i=!0):(t.parts.push(s),r=!0);if(r&&i)throw new m("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!r&&!i)throw new m("No content is provided for sending chat message.");return r?t:n}function Q(e,t){var n;let r={model:null===t||void 0===t?void 0:t.model,generationConfig:null===t||void 0===t?void 0:t.generationConfig,safetySettings:null===t||void 0===t?void 0:t.safetySettings,tools:null===t||void 0===t?void 0:t.tools,toolConfig:null===t||void 0===t?void 0:t.toolConfig,systemInstruction:null===t||void 0===t?void 0:t.systemInstruction,cachedContent:null===(n=null===t||void 0===t?void 0:t.cachedContent)||void 0===n?void 0:n.name,contents:[]};const i=null!=e.generateContentRequest;if(e.contents){if(i)throw new y("CountTokensRequest must have one of contents or generateContentRequest, not both.");r.contents=e.contents}else if(i)r=Object.assign(Object.assign({},r),e.generateContentRequest);else{const t=J(e);r.contents=[t]}return{generateContentRequest:r}}function ee(e){let t;if(e.contents)t=e;else{const n=J(e);t={contents:[n]}}return e.systemInstruction&&(t.systemInstruction=K(e.systemInstruction)),t}function te(e){if("string"===typeof e||Array.isArray(e)){const t=J(e);return{content:t}}return e}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ne=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],re={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function ie(e){let t=!1;for(const n of e){const{role:e,parts:r}=n;if(!t&&"user"!==e)throw new m(`First content should be with role 'user', got ${e}`);if(!o.includes(e))throw new m(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(o)}`);if(!Array.isArray(r))throw new m("Content should have 'parts' property with an array of Parts");if(0===r.length)throw new m("Each Content should have at least one part");const i={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const t of r)for(const e of ne)e in t&&(i[e]+=1);const s=re[e];for(const t of ne)if(!s.includes(t)&&i[t]>0)throw new m(`Content with role '${e}' can't contain '${t}' part`);t=!0}}function se(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;const n=null===(t=e.candidates[0])||void 0===t?void 0:t.content;if(void 0===n)return!1;if(void 0===n.parts||0===n.parts.length)return!1;for(const r of n.parts){if(void 0===r||0===Object.keys(r).length)return!1;if(void 0!==r.text&&""===r.text)return!1}return!0}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const oe="SILENT_ERROR";class ae{constructor(e,t,n,r={}){this.model=t,this.params=n,this._requestOptions=r,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null===n||void 0===n?void 0:n.history)&&(ie(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var n,r,i,s,o,a;await this._sendPromise;const l=J(e),c={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(r=this.params)||void 0===r?void 0:r.generationConfig,tools:null===(i=this.params)||void 0===i?void 0:i.tools,toolConfig:null===(s=this.params)||void 0===s?void 0:s.toolConfig,systemInstruction:null===(o=this.params)||void 0===o?void 0:o.systemInstruction,cachedContent:null===(a=this.params)||void 0===a?void 0:a.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t);let d;return this._sendPromise=this._sendPromise.then(()=>X(this._apiKey,this.model,c,u)).then(e=>{var t;if(se(e.response)){this._history.push(l);const n=Object.assign({parts:[],role:"model"},null===(t=e.response.candidates)||void 0===t?void 0:t[0].content);this._history.push(n)}else{const t=z(e.response);t&&console.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}d=e}).catch(e=>{throw this._sendPromise=Promise.resolve(),e}),await this._sendPromise,d}async sendMessageStream(e,t={}){var n,r,i,s,o,a;await this._sendPromise;const l=J(e),c={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(r=this.params)||void 0===r?void 0:r.generationConfig,tools:null===(i=this.params)||void 0===i?void 0:i.tools,toolConfig:null===(s=this.params)||void 0===s?void 0:s.toolConfig,systemInstruction:null===(o=this.params)||void 0===o?void 0:o.systemInstruction,cachedContent:null===(a=this.params)||void 0===a?void 0:a.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t),d=Y(this._apiKey,this.model,c,u);return this._sendPromise=this._sendPromise.then(()=>d).catch(e=>{throw new Error(oe)}).then(e=>e.response).then(e=>{if(se(e)){this._history.push(l);const t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{const t=z(e);t&&console.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}}).catch(e=>{e.message!==oe&&console.error(e)}),d}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function le(e,t,n,r){const i=await $(t,C.COUNT_TOKENS,e,!1,JSON.stringify(n),r);return i.json()}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function ce(e,t,n,r){const i=await $(t,C.EMBED_CONTENT,e,!1,JSON.stringify(n),r);return i.json()}async function ue(e,t,n,r){const i=n.requests.map(e=>Object.assign(Object.assign({},e),{model:t})),s=await $(t,C.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:i}),r);return s.json()}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class de{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=K(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;const r=ee(e),i=Object.assign(Object.assign({},this._requestOptions),t);return X(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},r),i)}async generateContentStream(e,t={}){var n;const r=ee(e),i=Object.assign(Object.assign({},this._requestOptions),t);return Y(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},r),i)}startChat(e){var t;return new ae(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(t=this.cachedContent)||void 0===t?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){const n=Q(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),r=Object.assign(Object.assign({},this._requestOptions),t);return le(this.apiKey,this.model,n,r)}async embedContent(e,t={}){const n=te(e),r=Object.assign(Object.assign({},this._requestOptions),t);return ce(this.apiKey,this.model,n,r)}async batchEmbedContents(e,t={}){const n=Object.assign(Object.assign({},this._requestOptions),t);return ue(this.apiKey,this.model,e,n)}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class pe{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new m("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new de(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new y("Cached content must contain a `name` field.");if(!e.model)throw new y("Cached content must contain a `model` field.");const r=["model","systemInstruction"];for(const s of r)if((null===t||void 0===t?void 0:t[s])&&e[s]&&(null===t||void 0===t?void 0:t[s])!==e[s]){if("model"===s){const n=t.model.startsWith("models/")?t.model.replace("models/",""):t.model,r=e.model.startsWith("models/")?e.model.replace("models/",""):e.model;if(n===r)continue}throw new y(`Different value for "${s}" specified in modelParams (${t[s]}) and cachedContent (${e[s]})`)}const i=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new de(this.apiKey,i,n)}}},5992:(e,t,n)=>{"use strict";n.d(t,{k:()=>r});const r={RECORDING:"RECORDING",PAUSED:"PAUSED",NONE:"NONE"}},6546:(e,t,n)=>{"use strict";
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var r;n.d(t,{EA:()=>v,E_:()=>u,F3:()=>c,Ii:()=>l}),function(e){e["Unimplemented"]="UNIMPLEMENTED",e["Unavailable"]="UNAVAILABLE"}(r||(r={}));class i extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const s=e=>{var t,n;return(null===e||void 0===e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null===e||void 0===e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"},o=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},o=n.Plugins=n.Plugins||{},a=()=>null!==t?t.name:s(e),l=()=>"web"!==a(),c=e=>{const t=p.get(e);return!!(null===t||void 0===t?void 0:t.platforms.has(a()))||!!u(e)},u=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},d=t=>e.console.error(t),p=new Map,h=(e,s={})=>{const l=p.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=a(),d=u(e);let h;const f=async()=>(!h&&c in s?h=h="function"===typeof s[c]?await s[c]():s[c]:null!==t&&!h&&"web"in s&&(h=h="function"===typeof s["web"]?await s["web"]():s["web"]),h),m=(t,s)=>{var o,a;if(!d){if(t)return null===(a=t[s])||void 0===a?void 0:a.bind(t);throw new i(`"${e}" plugin is not implemented on ${c}`,r.Unimplemented)}{const r=null===d||void 0===d?void 0:d.methods.find(e=>s===e.name);if(r)return"promise"===r.rtype?t=>n.nativePromise(e,s.toString(),t):(t,r)=>n.nativeCallback(e,s.toString(),t,r);if(t)return null===(o=t[s])||void 0===o?void 0:o.bind(t)}},g=t=>{let n;const s=(...s)=>{const o=f().then(o=>{const a=m(o,t);if(a){const e=a(...s);return n=null===e||void 0===e?void 0:e.remove,e}throw new i(`"${e}.${t}()" is not implemented on ${c}`,r.Unimplemented)});return"addListener"===t&&(o.remove=async()=>n()),o};return s.toString=()=>`${t.toString()}() { [capacitor code] }`,Object.defineProperty(s,"name",{value:t,writable:!1,configurable:!1}),s},v=g("addListener"),y=g("removeListener"),b=(e,t)=>{const n=v({eventName:e},t),r=async()=>{const r=await n;y({eventName:e,callbackId:r},t)},i=new Promise(e=>n.then(()=>e({remove:r})));return i.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await r()},i},w=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?b:v;case"removeListener":return y;default:return g(t)}}});return o[e]=w,p.set(e,{name:e,proxy:w,platforms:new Set([...Object.keys(s),...d?[c]:[]])}),w};return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=a,n.handleError=d,n.isNativePlatform=l,n.isPluginAvailable=c,n.registerPlugin=h,n.Exception=i,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},a=e=>e.Capacitor=o(e),l=a("undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{}),c=l.registerPlugin;class u{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;const r=this.listeners[e];r||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),n&&this.sendRetainedArgumentsForEvent(e);const s=async()=>this.removeListener(e,t),o=Promise.resolve({remove:s});return o}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const r=this.listeners[e];if(r)r.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new l.Exception(e,r.Unimplemented)}unavailable(e="not available"){return new l.Exception(e,r.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const r=n.indexOf(t);this.listeners[e].splice(r,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const d=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),p=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class h extends u{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,r]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=p(n).trim(),r=p(r).trim(),t[n]=r}),t}async setCookie(e){try{const t=d(e.key),n=d(e.value),r=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),s=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${r}; path=${i}; ${s};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}c("CapacitorCookies",{web:()=>new h});const f=async e=>new Promise((t,n)=>{const r=new FileReader;r.onload=()=>{const e=r.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},r.onerror=e=>n(e),r.readAsDataURL(e)}),m=(e={})=>{const t=Object.keys(e),n=Object.keys(e).map(e=>e.toLocaleLowerCase()),r=n.reduce((n,r,i)=>(n[r]=e[t[i]],n),{});return r},g=(e,t=!0)=>{if(!e)return null;const n=Object.entries(e).reduce((e,n)=>{const[r,i]=n;let s,o;return Array.isArray(i)?(o="",i.forEach(e=>{s=t?encodeURIComponent(e):e,o+=`${r}=${s}&`}),o.slice(0,-1)):(s=t?encodeURIComponent(i):i,o=`${r}=${s}`),`${e}&${o}`},"");return n.substr(1)},v=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),r=m(e.headers),i=r["content-type"]||"";if("string"===typeof e.data)n.body=e.data;else if(i.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,r]of Object.entries(e.data||{}))t.set(n,r);n.body=t.toString()}else if(i.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const r=new Headers(n.headers);r.delete("content-type"),n.headers=r}else(i.includes("application/json")||"object"===typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class y extends u{async request(e){const t=v(e,e.webFetchExtra),n=g(e.params,e.shouldEncodeUrlParams),r=n?`${e.url}?${n}`:e.url,i=await fetch(r,t),s=i.headers.get("content-type")||"";let o,a,{responseType:l="text"}=i.ok?e:{};switch(s.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":a=await i.blob(),o=await f(a);break;case"json":o=await i.json();break;case"document":case"text":default:o=await i.text()}const c={};return i.headers.forEach((e,t)=>{c[t]=e}),{data:o,headers:c,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}c("CapacitorHttp",{web:()=>new y})},6982:(e,t,n)=>{"use strict";var r=n(5709);t.A=o;var i=r(n(2207)),s=r(n(9092));function o(e){return a.apply(this,arguments)}function a(){return(a=(0,s.default)(i.default.mark(function e(t){var n,r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=document.createElement("video"),r=new Promise(function(e,t){n.addEventListener("loadedmetadata",function(){n.duration===1/0?(n.currentTime=Number.MAX_SAFE_INTEGER,n.ontimeupdate=function(){n.ontimeupdate=null,e(n.duration),n.currentTime=0}):e(n.duration)}),n.onerror=function(e){return t(e.target.error)}}),n.src="string"==typeof t||t instanceof String?t:window.URL.createObjectURL(t),e.abrupt("return",r);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}},7411:(e,t,n)=>{"use strict";n.r(t),n.d(t,{SplashScreenWeb:()=>i});var r=n(6546);class i extends r.E_{async show(e){}async hide(e){}}},7448:(e,t,n)=>{"use strict";var r,i;n.d(t,{Wi:()=>i}),function(e){e["Documents"]="DOCUMENTS",e["Data"]="DATA",e["Library"]="LIBRARY",e["Cache"]="CACHE",e["External"]="EXTERNAL",e["ExternalStorage"]="EXTERNAL_STORAGE",e["ExternalCache"]="EXTERNAL_CACHE",e["LibraryNoCloud"]="LIBRARY_NO_CLOUD",e["Temporary"]="TEMPORARY"}(r||(r={})),function(e){e["UTF8"]="utf8",e["ASCII"]="ascii",e["UTF16"]="utf16"}(i||(i={}))},7452:e=>{var t=function(e){"use strict";var t,n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"===typeof Symbol?Symbol:{},o=s.iterator||"@@iterator",a=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(I){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var s=t&&t.prototype instanceof v?t:v,o=Object.create(s.prototype),a=new R(r||[]);return i(o,"_invoke",{value:k(e,n,a)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(I){return{type:"throw",arg:I}}}e.wrap=u;var p="suspendedStart",h="suspendedYield",f="executing",m="completed",g={};function v(){}function y(){}function b(){}var w={};c(w,o,function(){return this});var x=Object.getPrototypeOf,E=x&&x(x(P([])));E&&E!==n&&r.call(E,o)&&(w=E);var S=b.prototype=v.prototype=Object.create(w);function C(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}function T(e,t){function n(i,s,o,a){var l=d(e[i],e,s);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"===typeof u&&r.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,o,a)},function(e){n("throw",e,o,a)}):t.resolve(u).then(function(e){c.value=e,o(c)},function(e){return n("throw",e,o,a)})}a(l.arg)}var s;function o(e,r){function i(){return new t(function(t,i){n(e,r,t,i)})}return s=s?s.then(i,i):i()}i(this,"_invoke",{value:o})}function k(e,t,n){var r=p;return function(i,s){if(r===f)throw new Error("Generator is already running");if(r===m){if("throw"===i)throw s;return A()}n.method=i,n.arg=s;while(1){var o=n.delegate;if(o){var a=_(o,n);if(a){if(a===g)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===p)throw r=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=f;var l=d(e,t,n);if("normal"===l.type){if(r=n.done?m:h,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r=m,n.method="throw",n.arg=l.arg)}}}function _(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator["return"]&&(n.method="return",n.arg=t,_(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var s=d(i,e.iterator,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,g;var o=s.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function P(e){if(e){var n=e[o];if(n)return n.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var i=-1,s=function n(){while(++i<e.length)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return s.next=s}}return{next:A}}function A(){return{value:t,done:!0}}return y.prototype=b,i(S,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},e.awrap=function(e){return{__await:e}},C(T.prototype),c(T.prototype,a,function(){return this}),e.AsyncIterator=T,e.async=function(t,n,r,i,s){void 0===s&&(s=Promise);var o=new T(u(t,n,r,i),s);return e.isGeneratorFunction(n)?o:o.next().then(function(e){return e.done?e.value:o.next()})},C(S),c(S,l,"Generator"),c(S,o,function(){return this}),c(S,"toString",function(){return"[object Generator]"}),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){while(n.length){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=P,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return a.type="throw",a.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var o=this.tryEntries[s],a=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var o=s?s.completion:{};return o.type=e,o.arg=t,s?(this.method="next",this.next=s.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),$(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;$(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:P(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=t}catch(n){"object"===typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}},7666:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>B});class r extends Error{constructor(e){super("ClientResponseError"),this.url="",this.status=0,this.response={},this.isAbort=!1,this.originalError=null,Object.setPrototypeOf(this,r.prototype),null!==e&&"object"==typeof e&&(this.url="string"==typeof e.url?e.url:"",this.status="number"==typeof e.status?e.status:0,this.isAbort=!!e.isAbort,this.originalError=e.originalError,null!==e.response&&"object"==typeof e.response?this.response=e.response:null!==e.data&&"object"==typeof e.data?this.response=e.data:this.response={}),this.originalError||e instanceof r||(this.originalError=e),"undefined"!=typeof DOMException&&e instanceof DOMException&&(this.isAbort=!0),this.name="ClientResponseError "+this.status,this.message=this.response?.message,this.message||(this.isAbort?this.message="The request was autocancelled. You can find more info in https://github.com/pocketbase/js-sdk#auto-cancellation.":this.originalError?.cause?.message?.includes("ECONNREFUSED ::1")?this.message="Failed to connect to the PocketBase server. Try changing the SDK URL from localhost to 127.0.0.1 (https://github.com/pocketbase/js-sdk/issues/21).":this.message="Something went wrong."),this.cause=this.originalError}get data(){return this.response}toJSON(){return{...this}}}const i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function s(e,t){const n={};if("string"!=typeof e)return n;const r=Object.assign({},t||{}).decode||a;let i=0;for(;i<e.length;){const t=e.indexOf("=",i);if(-1===t)break;let s=e.indexOf(";",i);if(-1===s)s=e.length;else if(s<t){i=e.lastIndexOf(";",t-1)+1;continue}const o=e.slice(i,t).trim();if(void 0===n[o]){let i=e.slice(t+1,s).trim();34===i.charCodeAt(0)&&(i=i.slice(1,-1));try{n[o]=r(i)}catch(e){n[o]=i}}i=s+1}return n}function o(e,t,n){const r=Object.assign({},n||{}),s=r.encode||l;if(!i.test(e))throw new TypeError("argument name is invalid");const o=s(t);if(o&&!i.test(o))throw new TypeError("argument val is invalid");let a=e+"="+o;if(null!=r.maxAge){const e=r.maxAge-0;if(isNaN(e)||!isFinite(e))throw new TypeError("option maxAge is invalid");a+="; Max-Age="+Math.floor(e)}if(r.domain){if(!i.test(r.domain))throw new TypeError("option domain is invalid");a+="; Domain="+r.domain}if(r.path){if(!i.test(r.path))throw new TypeError("option path is invalid");a+="; Path="+r.path}if(r.expires){if(!function(e){return"[object Date]"===Object.prototype.toString.call(e)||e instanceof Date}(r.expires)||isNaN(r.expires.valueOf()))throw new TypeError("option expires is invalid");a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():r.priority){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return a}function a(e){return-1!==e.indexOf("%")?decodeURIComponent(e):e}function l(e){return encodeURIComponent(e)}const c="undefined"!=typeof navigator&&"ReactNative"===navigator.product||"undefined"!=typeof global&&global.HermesInternal;let u;function d(e){if(e)try{const t=decodeURIComponent(u(e.split(".")[1]).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)||{}}catch(e){}return{}}function p(e,t=0){let n=d(e);return!(Object.keys(n).length>0&&(!n.exp||n.exp-t>Date.now()/1e3))}u="function"!=typeof atob||c?e=>{let t=String(e).replace(/=+$/,"");if(t.length%4==1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,r,i=0,s=0,o="";r=t.charAt(s++);~r&&(n=i%4?64*n+r:r,i++%4)?o+=String.fromCharCode(255&n>>(-2*i&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r);return o}:atob;const h="pb_auth";class f{constructor(){this.baseToken="",this.baseModel=null,this._onChangeCallbacks=[]}get token(){return this.baseToken}get record(){return this.baseModel}get model(){return this.baseModel}get isValid(){return!p(this.token)}get isSuperuser(){let e=d(this.token);return"auth"==e.type&&("_superusers"==this.record?.collectionName||!this.record?.collectionName&&"pbc_3142635823"==e.collectionId)}get isAdmin(){return console.warn("Please replace pb.authStore.isAdmin with pb.authStore.isSuperuser OR simply check the value of pb.authStore.record?.collectionName"),this.isSuperuser}get isAuthRecord(){return console.warn("Please replace pb.authStore.isAuthRecord with !pb.authStore.isSuperuser OR simply check the value of pb.authStore.record?.collectionName"),"auth"==d(this.token).type&&!this.isSuperuser}save(e,t){this.baseToken=e||"",this.baseModel=t||null,this.triggerChange()}clear(){this.baseToken="",this.baseModel=null,this.triggerChange()}loadFromCookie(e,t=h){const n=s(e||"")[t]||"";let r={};try{r=JSON.parse(n),(null===typeof r||"object"!=typeof r||Array.isArray(r))&&(r={})}catch(e){}this.save(r.token||"",r.record||r.model||null)}exportToCookie(e,t=h){const n={secure:!0,sameSite:!0,httpOnly:!0,path:"/"},r=d(this.token);n.expires=r?.exp?new Date(1e3*r.exp):new Date("1970-01-01"),e=Object.assign({},n,e);const i={token:this.token,record:this.record?JSON.parse(JSON.stringify(this.record)):null};let s=o(t,JSON.stringify(i),e);const a="undefined"!=typeof Blob?new Blob([s]).size:s.length;if(i.record&&a>4096){i.record={id:i.record?.id,email:i.record?.email};const n=["collectionId","collectionName","verified"];for(const e in this.record)n.includes(e)&&(i.record[e]=this.record[e]);s=o(t,JSON.stringify(i),e)}return s}onChange(e,t=!1){return this._onChangeCallbacks.push(e),t&&e(this.token,this.record),()=>{for(let t=this._onChangeCallbacks.length-1;t>=0;t--)if(this._onChangeCallbacks[t]==e)return delete this._onChangeCallbacks[t],void this._onChangeCallbacks.splice(t,1)}}triggerChange(){for(const e of this._onChangeCallbacks)e&&e(this.token,this.record)}}class m extends f{constructor(e="pocketbase_auth"){super(),this.storageFallback={},this.storageKey=e,this._bindStorageEvent()}get token(){return(this._storageGet(this.storageKey)||{}).token||""}get record(){const e=this._storageGet(this.storageKey)||{};return e.record||e.model||null}get model(){return this.record}save(e,t){this._storageSet(this.storageKey,{token:e,record:t}),super.save(e,t)}clear(){this._storageRemove(this.storageKey),super.clear()}_storageGet(e){if("undefined"!=typeof window&&window?.localStorage){const t=window.localStorage.getItem(e)||"";try{return JSON.parse(t)}catch(e){return t}}return this.storageFallback[e]}_storageSet(e,t){if("undefined"!=typeof window&&window?.localStorage){let n=t;"string"!=typeof t&&(n=JSON.stringify(t)),window.localStorage.setItem(e,n)}else this.storageFallback[e]=t}_storageRemove(e){"undefined"!=typeof window&&window?.localStorage&&window.localStorage?.removeItem(e),delete this.storageFallback[e]}_bindStorageEvent(){"undefined"!=typeof window&&window?.localStorage&&window.addEventListener&&window.addEventListener("storage",e=>{if(e.key!=this.storageKey)return;const t=this._storageGet(this.storageKey)||{};super.save(t.token||"",t.record||t.model||null)})}}class g{constructor(e){this.client=e}}class v extends g{async getAll(e){return e=Object.assign({method:"GET"},e),this.client.send("/api/settings",e)}async update(e,t){return t=Object.assign({method:"PATCH",body:e},t),this.client.send("/api/settings",t)}async testS3(e="storage",t){return t=Object.assign({method:"POST",body:{filesystem:e}},t),this.client.send("/api/settings/test/s3",t).then(()=>!0)}async testEmail(e,t,n,r){return r=Object.assign({method:"POST",body:{email:t,template:n,collection:e}},r),this.client.send("/api/settings/test/email",r).then(()=>!0)}async generateAppleClientSecret(e,t,n,r,i,s){return s=Object.assign({method:"POST",body:{clientId:e,teamId:t,keyId:n,privateKey:r,duration:i}},s),this.client.send("/api/settings/apple/generate-client-secret",s)}}const y=["requestKey","$cancelKey","$autoCancel","fetch","headers","body","query","params","cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","signal","window"];function b(e){if(e){e.query=e.query||{};for(let t in e)y.includes(t)||(e.query[t]=e[t],delete e[t])}}function w(e){const t=[];for(const n in e){const r=encodeURIComponent(n),i=Array.isArray(e[n])?e[n]:[e[n]];for(let e of i)e=x(e),null!==e&&t.push(r+"="+e)}return t.join("&")}function x(e){return null==e?null:e instanceof Date?encodeURIComponent(e.toISOString().replace("T"," ")):"object"==typeof e?encodeURIComponent(JSON.stringify(e)):encodeURIComponent(e)}class E extends g{constructor(){super(...arguments),this.clientId="",this.eventSource=null,this.subscriptions={},this.lastSentSubscriptions=[],this.maxConnectTimeout=15e3,this.reconnectAttempts=0,this.maxReconnectAttempts=1/0,this.predefinedReconnectIntervals=[200,300,500,1e3,1200,1500,2e3],this.pendingConnects=[]}get isConnected(){return!!this.eventSource&&!!this.clientId&&!this.pendingConnects.length}async subscribe(e,t,n){if(!e)throw new Error("topic must be set.");let r=e;if(n){b(n=Object.assign({},n));const e="options="+encodeURIComponent(JSON.stringify({query:n.query,headers:n.headers}));r+=(r.includes("?")?"&":"?")+e}const i=function(e){const n=e;let r;try{r=JSON.parse(n?.data)}catch{}t(r||{})};return this.subscriptions[r]||(this.subscriptions[r]=[]),this.subscriptions[r].push(i),this.isConnected?1===this.subscriptions[r].length?await this.submitSubscriptions():this.eventSource?.addEventListener(r,i):await this.connect(),async()=>this.unsubscribeByTopicAndListener(e,i)}async unsubscribe(e){let t=!1;if(e){const n=this.getSubscriptionsByTopic(e);for(let e in n)if(this.hasSubscriptionListeners(e)){for(let t of this.subscriptions[e])this.eventSource?.removeEventListener(e,t);delete this.subscriptions[e],t||(t=!0)}}else this.subscriptions={};this.hasSubscriptionListeners()?t&&await this.submitSubscriptions():this.disconnect()}async unsubscribeByPrefix(e){let t=!1;for(let n in this.subscriptions)if((n+"?").startsWith(e)){t=!0;for(let e of this.subscriptions[n])this.eventSource?.removeEventListener(n,e);delete this.subscriptions[n]}t&&(this.hasSubscriptionListeners()?await this.submitSubscriptions():this.disconnect())}async unsubscribeByTopicAndListener(e,t){let n=!1;const r=this.getSubscriptionsByTopic(e);for(let i in r){if(!Array.isArray(this.subscriptions[i])||!this.subscriptions[i].length)continue;let e=!1;for(let n=this.subscriptions[i].length-1;n>=0;n--)this.subscriptions[i][n]===t&&(e=!0,delete this.subscriptions[i][n],this.subscriptions[i].splice(n,1),this.eventSource?.removeEventListener(i,t));e&&(this.subscriptions[i].length||delete this.subscriptions[i],n||this.hasSubscriptionListeners(i)||(n=!0))}this.hasSubscriptionListeners()?n&&await this.submitSubscriptions():this.disconnect()}hasSubscriptionListeners(e){if(this.subscriptions=this.subscriptions||{},e)return!!this.subscriptions[e]?.length;for(let t in this.subscriptions)if(this.subscriptions[t]?.length)return!0;return!1}async submitSubscriptions(){if(this.clientId)return this.addAllSubscriptionListeners(),this.lastSentSubscriptions=this.getNonEmptySubscriptionKeys(),this.client.send("/api/realtime",{method:"POST",body:{clientId:this.clientId,subscriptions:this.lastSentSubscriptions},requestKey:this.getSubscriptionsCancelKey()}).catch(e=>{if(!e?.isAbort)throw e})}getSubscriptionsCancelKey(){return"realtime_"+this.clientId}getSubscriptionsByTopic(e){const t={};e=e.includes("?")?e:e+"?";for(let n in this.subscriptions)(n+"?").startsWith(e)&&(t[n]=this.subscriptions[n]);return t}getNonEmptySubscriptionKeys(){const e=[];for(let t in this.subscriptions)this.subscriptions[t].length&&e.push(t);return e}addAllSubscriptionListeners(){if(this.eventSource){this.removeAllSubscriptionListeners();for(let e in this.subscriptions)for(let t of this.subscriptions[e])this.eventSource.addEventListener(e,t)}}removeAllSubscriptionListeners(){if(this.eventSource)for(let e in this.subscriptions)for(let t of this.subscriptions[e])this.eventSource.removeEventListener(e,t)}async connect(){if(!(this.reconnectAttempts>0))return new Promise((e,t)=>{this.pendingConnects.push({resolve:e,reject:t}),this.pendingConnects.length>1||this.initConnect()})}initConnect(){this.disconnect(!0),clearTimeout(this.connectTimeoutId),this.connectTimeoutId=setTimeout(()=>{this.connectErrorHandler(new Error("EventSource connect took too long."))},this.maxConnectTimeout),this.eventSource=new EventSource(this.client.buildURL("/api/realtime")),this.eventSource.onerror=e=>{this.connectErrorHandler(new Error("Failed to establish realtime connection."))},this.eventSource.addEventListener("PB_CONNECT",e=>{const t=e;this.clientId=t?.lastEventId,this.submitSubscriptions().then(async()=>{let e=3;for(;this.hasUnsentSubscriptions()&&e>0;)e--,await this.submitSubscriptions()}).then(()=>{for(let e of this.pendingConnects)e.resolve();this.pendingConnects=[],this.reconnectAttempts=0,clearTimeout(this.reconnectTimeoutId),clearTimeout(this.connectTimeoutId);const t=this.getSubscriptionsByTopic("PB_CONNECT");for(let n in t)for(let r of t[n])r(e)}).catch(e=>{this.clientId="",this.connectErrorHandler(e)})})}hasUnsentSubscriptions(){const e=this.getNonEmptySubscriptionKeys();if(e.length!=this.lastSentSubscriptions.length)return!0;for(const t of e)if(!this.lastSentSubscriptions.includes(t))return!0;return!1}connectErrorHandler(e){if(clearTimeout(this.connectTimeoutId),clearTimeout(this.reconnectTimeoutId),!this.clientId&&!this.reconnectAttempts||this.reconnectAttempts>this.maxReconnectAttempts){for(let t of this.pendingConnects)t.reject(new r(e));return this.pendingConnects=[],void this.disconnect()}this.disconnect(!0);const t=this.predefinedReconnectIntervals[this.reconnectAttempts]||this.predefinedReconnectIntervals[this.predefinedReconnectIntervals.length-1];this.reconnectAttempts++,this.reconnectTimeoutId=setTimeout(()=>{this.initConnect()},t)}disconnect(e=!1){if(this.clientId&&this.onDisconnect&&this.onDisconnect(Object.keys(this.subscriptions)),clearTimeout(this.connectTimeoutId),clearTimeout(this.reconnectTimeoutId),this.removeAllSubscriptionListeners(),this.client.cancelRequest(this.getSubscriptionsCancelKey()),this.eventSource?.close(),this.eventSource=null,this.clientId="",!e){this.reconnectAttempts=0;for(let e of this.pendingConnects)e.resolve();this.pendingConnects=[]}}}class S extends g{decode(e){return e}async getFullList(e,t){if("number"==typeof e)return this._getFullList(e,t);let n=500;return(t=Object.assign({},e,t)).batch&&(n=t.batch,delete t.batch),this._getFullList(n,t)}async getList(e=1,t=30,n){return(n=Object.assign({method:"GET"},n)).query=Object.assign({page:e,perPage:t},n.query),this.client.send(this.baseCrudPath,n).then(e=>(e.items=e.items?.map(e=>this.decode(e))||[],e))}async getFirstListItem(e,t){return(t=Object.assign({requestKey:"one_by_filter_"+this.baseCrudPath+"_"+e},t)).query=Object.assign({filter:e,skipTotal:1},t.query),this.getList(1,1,t).then(e=>{if(!e?.items?.length)throw new r({status:404,response:{code:404,message:"The requested resource wasn't found.",data:{}}});return e.items[0]})}async getOne(e,t){if(!e)throw new r({url:this.client.buildURL(this.baseCrudPath+"/"),status:404,response:{code:404,message:"Missing required record id.",data:{}}});return t=Object.assign({method:"GET"},t),this.client.send(this.baseCrudPath+"/"+encodeURIComponent(e),t).then(e=>this.decode(e))}async create(e,t){return t=Object.assign({method:"POST",body:e},t),this.client.send(this.baseCrudPath,t).then(e=>this.decode(e))}async update(e,t,n){return n=Object.assign({method:"PATCH",body:t},n),this.client.send(this.baseCrudPath+"/"+encodeURIComponent(e),n).then(e=>this.decode(e))}async delete(e,t){return t=Object.assign({method:"DELETE"},t),this.client.send(this.baseCrudPath+"/"+encodeURIComponent(e),t).then(()=>!0)}_getFullList(e=500,t){(t=t||{}).query=Object.assign({skipTotal:1},t.query);let n=[],r=async i=>this.getList(i,e||500,t).then(e=>{const t=e.items;return n=n.concat(t),t.length==e.perPage?r(i+1):n});return r(1)}}function C(e,t,n,r){const i=void 0!==r;return i||void 0!==n?i?(console.warn(e),t.body=Object.assign({},t.body,n),t.query=Object.assign({},t.query,r),t):Object.assign(t,n):t}function T(e){e._resetAutoRefresh?.()}class k extends S{constructor(e,t){super(e),this.collectionIdOrName=t}get baseCrudPath(){return this.baseCollectionPath+"/records"}get baseCollectionPath(){return"/api/collections/"+encodeURIComponent(this.collectionIdOrName)}get isSuperusers(){return"_superusers"==this.collectionIdOrName||"_pbc_2773867675"==this.collectionIdOrName}async subscribe(e,t,n){if(!e)throw new Error("Missing topic.");if(!t)throw new Error("Missing subscription callback.");return this.client.realtime.subscribe(this.collectionIdOrName+"/"+e,t,n)}async unsubscribe(e){return e?this.client.realtime.unsubscribe(this.collectionIdOrName+"/"+e):this.client.realtime.unsubscribeByPrefix(this.collectionIdOrName)}async getFullList(e,t){if("number"==typeof e)return super.getFullList(e,t);const n=Object.assign({},e,t);return super.getFullList(n)}async getList(e=1,t=30,n){return super.getList(e,t,n)}async getFirstListItem(e,t){return super.getFirstListItem(e,t)}async getOne(e,t){return super.getOne(e,t)}async create(e,t){return super.create(e,t)}async update(e,t,n){return super.update(e,t,n).then(e=>{if(this.client.authStore.record?.id===e?.id&&(this.client.authStore.record?.collectionId===this.collectionIdOrName||this.client.authStore.record?.collectionName===this.collectionIdOrName)){let t=Object.assign({},this.client.authStore.record.expand),n=Object.assign({},this.client.authStore.record,e);t&&(n.expand=Object.assign(t,e.expand)),this.client.authStore.save(this.client.authStore.token,n)}return e})}async delete(e,t){return super.delete(e,t).then(t=>(!t||this.client.authStore.record?.id!==e||this.client.authStore.record?.collectionId!==this.collectionIdOrName&&this.client.authStore.record?.collectionName!==this.collectionIdOrName||this.client.authStore.clear(),t))}authResponse(e){const t=this.decode(e?.record||{});return this.client.authStore.save(e?.token,t),Object.assign({},e,{token:e?.token||"",record:t})}async listAuthMethods(e){return e=Object.assign({method:"GET",fields:"mfa,otp,password,oauth2"},e),this.client.send(this.baseCollectionPath+"/auth-methods",e)}async authWithPassword(e,t,n){let r;n=Object.assign({method:"POST",body:{identity:e,password:t}},n),this.isSuperusers&&(r=n.autoRefreshThreshold,delete n.autoRefreshThreshold,n.autoRefresh||T(this.client));let i=await this.client.send(this.baseCollectionPath+"/auth-with-password",n);return i=this.authResponse(i),r&&this.isSuperusers&&function(e,t,n,r){T(e);const i=e.beforeSend,s=e.authStore.record,o=e.authStore.onChange((t,n)=>{(!t||n?.id!=s?.id||(n?.collectionId||s?.collectionId)&&n?.collectionId!=s?.collectionId)&&T(e)});e._resetAutoRefresh=function(){o(),e.beforeSend=i,delete e._resetAutoRefresh},e.beforeSend=async(s,o)=>{const a=e.authStore.token;if(o.query?.autoRefresh)return i?i(s,o):{url:s,sendOptions:o};let l=e.authStore.isValid;if(l&&p(e.authStore.token,t))try{await n()}catch(e){l=!1}l||await r();const c=o.headers||{};for(let t in c)if("authorization"==t.toLowerCase()&&a==c[t]&&e.authStore.token){c[t]=e.authStore.token;break}return o.headers=c,i?i(s,o):{url:s,sendOptions:o}}}(this.client,r,()=>this.authRefresh({autoRefresh:!0}),()=>this.authWithPassword(e,t,Object.assign({autoRefresh:!0},n))),i}async authWithOAuth2Code(e,t,n,r,i,s,o){let a={method:"POST",body:{provider:e,code:t,codeVerifier:n,redirectURL:r,createData:i}};return a=C("This form of authWithOAuth2Code(provider, code, codeVerifier, redirectURL, createData?, body?, query?) is deprecated. Consider replacing it with authWithOAuth2Code(provider, code, codeVerifier, redirectURL, createData?, options?).",a,s,o),this.client.send(this.baseCollectionPath+"/auth-with-oauth2",a).then(e=>this.authResponse(e))}authWithOAuth2(...e){if(e.length>1||"string"==typeof e?.[0])return console.warn("PocketBase: This form of authWithOAuth2() is deprecated and may get removed in the future. Please replace with authWithOAuth2Code() OR use the authWithOAuth2() realtime form as shown in https://pocketbase.io/docs/authentication/#oauth2-integration."),this.authWithOAuth2Code(e?.[0]||"",e?.[1]||"",e?.[2]||"",e?.[3]||"",e?.[4]||{},e?.[5]||{},e?.[6]||{});const t=e?.[0]||{};let n=null;t.urlCallback||(n=_(void 0));const i=new E(this.client);function s(){n?.close(),i.unsubscribe()}const o={},a=t.requestKey;return a&&(o.requestKey=a),this.listAuthMethods(o).then(e=>{const o=e.oauth2.providers.find(e=>e.name===t.provider);if(!o)throw new r(new Error(`Missing or invalid provider "${t.provider}".`));const l=this.client.buildURL("/api/oauth2-redirect"),c=a?this.client.cancelControllers?.[a]:void 0;return c&&(c.signal.onabort=()=>{s()}),new Promise(async(e,a)=>{try{await i.subscribe("@oauth2",async n=>{const u=i.clientId;try{if(!n.state||u!==n.state)throw new Error("State parameters don't match.");if(n.error||!n.code)throw new Error("OAuth2 redirect error or missing code: "+n.error);const r=Object.assign({},t);delete r.provider,delete r.scopes,delete r.createData,delete r.urlCallback,c?.signal?.onabort&&(c.signal.onabort=null);const i=await this.authWithOAuth2Code(o.name,n.code,o.codeVerifier,l,t.createData,r);e(i)}catch(e){a(new r(e))}s()});const u={state:i.clientId};t.scopes?.length&&(u.scope=t.scopes.join(" "));const d=this._replaceQueryParams(o.authURL+l,u);let p=t.urlCallback||function(e){n?n.location.href=e:n=_(e)};await p(d)}catch(e){s(),a(new r(e))}})}).catch(e=>{throw s(),e})}async authRefresh(e,t){let n={method:"POST"};return n=C("This form of authRefresh(body?, query?) is deprecated. Consider replacing it with authRefresh(options?).",n,e,t),this.client.send(this.baseCollectionPath+"/auth-refresh",n).then(e=>this.authResponse(e))}async requestPasswordReset(e,t,n){let r={method:"POST",body:{email:e}};return r=C("This form of requestPasswordReset(email, body?, query?) is deprecated. Consider replacing it with requestPasswordReset(email, options?).",r,t,n),this.client.send(this.baseCollectionPath+"/request-password-reset",r).then(()=>!0)}async confirmPasswordReset(e,t,n,r,i){let s={method:"POST",body:{token:e,password:t,passwordConfirm:n}};return s=C("This form of confirmPasswordReset(token, password, passwordConfirm, body?, query?) is deprecated. Consider replacing it with confirmPasswordReset(token, password, passwordConfirm, options?).",s,r,i),this.client.send(this.baseCollectionPath+"/confirm-password-reset",s).then(()=>!0)}async requestVerification(e,t,n){let r={method:"POST",body:{email:e}};return r=C("This form of requestVerification(email, body?, query?) is deprecated. Consider replacing it with requestVerification(email, options?).",r,t,n),this.client.send(this.baseCollectionPath+"/request-verification",r).then(()=>!0)}async confirmVerification(e,t,n){let r={method:"POST",body:{token:e}};return r=C("This form of confirmVerification(token, body?, query?) is deprecated. Consider replacing it with confirmVerification(token, options?).",r,t,n),this.client.send(this.baseCollectionPath+"/confirm-verification",r).then(()=>{const t=d(e),n=this.client.authStore.record;return n&&!n.verified&&n.id===t.id&&n.collectionId===t.collectionId&&(n.verified=!0,this.client.authStore.save(this.client.authStore.token,n)),!0})}async requestEmailChange(e,t,n){let r={method:"POST",body:{newEmail:e}};return r=C("This form of requestEmailChange(newEmail, body?, query?) is deprecated. Consider replacing it with requestEmailChange(newEmail, options?).",r,t,n),this.client.send(this.baseCollectionPath+"/request-email-change",r).then(()=>!0)}async confirmEmailChange(e,t,n,r){let i={method:"POST",body:{token:e,password:t}};return i=C("This form of confirmEmailChange(token, password, body?, query?) is deprecated. Consider replacing it with confirmEmailChange(token, password, options?).",i,n,r),this.client.send(this.baseCollectionPath+"/confirm-email-change",i).then(()=>{const t=d(e),n=this.client.authStore.record;return n&&n.id===t.id&&n.collectionId===t.collectionId&&this.client.authStore.clear(),!0})}async listExternalAuths(e,t){return this.client.collection("_externalAuths").getFullList(Object.assign({},t,{filter:this.client.filter("recordRef = {:id}",{id:e})}))}async unlinkExternalAuth(e,t,n){const r=await this.client.collection("_externalAuths").getFirstListItem(this.client.filter("recordRef = {:recordId} && provider = {:provider}",{recordId:e,provider:t}));return this.client.collection("_externalAuths").delete(r.id,n).then(()=>!0)}async requestOTP(e,t){return t=Object.assign({method:"POST",body:{email:e}},t),this.client.send(this.baseCollectionPath+"/request-otp",t)}async authWithOTP(e,t,n){return n=Object.assign({method:"POST",body:{otpId:e,password:t}},n),this.client.send(this.baseCollectionPath+"/auth-with-otp",n).then(e=>this.authResponse(e))}async impersonate(e,t,n){(n=Object.assign({method:"POST",body:{duration:t}},n)).headers=n.headers||{},n.headers.Authorization||(n.headers.Authorization=this.client.authStore.token);const r=new B(this.client.baseURL,new f,this.client.lang),i=await r.send(this.baseCollectionPath+"/impersonate/"+encodeURIComponent(e),n);return r.authStore.save(i?.token,this.decode(i?.record||{})),r}_replaceQueryParams(e,t={}){let n=e,r="";e.indexOf("?")>=0&&(n=e.substring(0,e.indexOf("?")),r=e.substring(e.indexOf("?")+1));const i={},s=r.split("&");for(const o of s){if(""==o)continue;const e=o.split("=");i[decodeURIComponent(e[0].replace(/\+/g," "))]=decodeURIComponent((e[1]||"").replace(/\+/g," "))}for(let o in t)t.hasOwnProperty(o)&&(null==t[o]?delete i[o]:i[o]=t[o]);r="";for(let o in i)i.hasOwnProperty(o)&&(""!=r&&(r+="&"),r+=encodeURIComponent(o.replace(/%20/g,"+"))+"="+encodeURIComponent(i[o].replace(/%20/g,"+")));return""!=r?n+"?"+r:n}}function _(e){if("undefined"==typeof window||!window?.open)throw new r(new Error("Not in a browser context - please pass a custom urlCallback function."));let t=1024,n=768,i=window.innerWidth,s=window.innerHeight;t=t>i?i:t,n=n>s?s:n;let o=i/2-t/2,a=s/2-n/2;return window.open(e,"popup_window","width="+t+",height="+n+",top="+a+",left="+o+",resizable,menubar=no")}class O extends S{get baseCrudPath(){return"/api/collections"}async import(e,t=!1,n){return n=Object.assign({method:"PUT",body:{collections:e,deleteMissing:t}},n),this.client.send(this.baseCrudPath+"/import",n).then(()=>!0)}async getScaffolds(e){return e=Object.assign({method:"GET"},e),this.client.send(this.baseCrudPath+"/meta/scaffolds",e)}async truncate(e,t){return t=Object.assign({method:"DELETE"},t),this.client.send(this.baseCrudPath+"/"+encodeURIComponent(e)+"/truncate",t).then(()=>!0)}}class $ extends g{async getList(e=1,t=30,n){return(n=Object.assign({method:"GET"},n)).query=Object.assign({page:e,perPage:t},n.query),this.client.send("/api/logs",n)}async getOne(e,t){if(!e)throw new r({url:this.client.buildURL("/api/logs/"),status:404,response:{code:404,message:"Missing required log id.",data:{}}});return t=Object.assign({method:"GET"},t),this.client.send("/api/logs/"+encodeURIComponent(e),t)}async getStats(e){return e=Object.assign({method:"GET"},e),this.client.send("/api/logs/stats",e)}}class R extends g{async check(e){return e=Object.assign({method:"GET"},e),this.client.send("/api/health",e)}}class P extends g{getUrl(e,t,n={}){return console.warn("Please replace pb.files.getUrl() with pb.files.getURL()"),this.getURL(e,t,n)}getURL(e,t,n={}){if(!t||!e?.id||!e?.collectionId&&!e?.collectionName)return"";const r=[];r.push("api"),r.push("files"),r.push(encodeURIComponent(e.collectionId||e.collectionName)),r.push(encodeURIComponent(e.id)),r.push(encodeURIComponent(t));let i=this.client.buildURL(r.join("/"));if(Object.keys(n).length){!1===n.download&&delete n.download;const e=new URLSearchParams(n);i+=(i.includes("?")?"&":"?")+e}return i}async getToken(e){return e=Object.assign({method:"POST"},e),this.client.send("/api/files/token",e).then(e=>e?.token||"")}}class A extends g{async getFullList(e){return e=Object.assign({method:"GET"},e),this.client.send("/api/backups",e)}async create(e,t){return t=Object.assign({method:"POST",body:{name:e}},t),this.client.send("/api/backups",t).then(()=>!0)}async upload(e,t){return t=Object.assign({method:"POST",body:e},t),this.client.send("/api/backups/upload",t).then(()=>!0)}async delete(e,t){return t=Object.assign({method:"DELETE"},t),this.client.send(`/api/backups/${encodeURIComponent(e)}`,t).then(()=>!0)}async restore(e,t){return t=Object.assign({method:"POST"},t),this.client.send(`/api/backups/${encodeURIComponent(e)}/restore`,t).then(()=>!0)}getDownloadUrl(e,t){return console.warn("Please replace pb.backups.getDownloadUrl() with pb.backups.getDownloadURL()"),this.getDownloadURL(e,t)}getDownloadURL(e,t){return this.client.buildURL(`/api/backups/${encodeURIComponent(t)}?token=${encodeURIComponent(e)}`)}}class I extends g{async getFullList(e){return e=Object.assign({method:"GET"},e),this.client.send("/api/crons",e)}async run(e,t){return t=Object.assign({method:"POST"},t),this.client.send(`/api/crons/${encodeURIComponent(e)}`,t).then(()=>!0)}}function L(e){return"undefined"!=typeof Blob&&e instanceof Blob||"undefined"!=typeof File&&e instanceof File||null!==e&&"object"==typeof e&&e.uri&&("undefined"!=typeof navigator&&"ReactNative"===navigator.product||"undefined"!=typeof global&&global.HermesInternal)}function M(e){return e&&("FormData"===e.constructor.name||"undefined"!=typeof FormData&&e instanceof FormData)}function N(e){for(const t in e){const n=Array.isArray(e[t])?e[t]:[e[t]];for(const e of n)if(L(e))return!0}return!1}const j=/^[\-\.\d]+$/;function D(e){if("string"!=typeof e)return e;if("true"==e)return!0;if("false"==e)return!1;if(("-"===e[0]||e[0]>="0"&&e[0]<="9")&&j.test(e)){let t=+e;if(""+t===e)return t}return e}class z extends g{constructor(){super(...arguments),this.requests=[],this.subs={}}collection(e){return this.subs[e]||(this.subs[e]=new F(this.requests,e)),this.subs[e]}async send(e){const t=new FormData,n=[];for(let r=0;r<this.requests.length;r++){const e=this.requests[r];if(n.push({method:e.method,url:e.url,headers:e.headers,body:e.json}),e.files)for(let n in e.files){const i=e.files[n]||[];for(let e of i)t.append("requests."+r+"."+n,e)}}return t.append("@jsonPayload",JSON.stringify({requests:n})),e=Object.assign({method:"POST",body:t},e),this.client.send("/api/batch",e)}}class F{constructor(e,t){this.requests=[],this.requests=e,this.collectionIdOrName=t}upsert(e,t){t=Object.assign({body:e||{}},t);const n={method:"PUT",url:"/api/collections/"+encodeURIComponent(this.collectionIdOrName)+"/records"};this.prepareRequest(n,t),this.requests.push(n)}create(e,t){t=Object.assign({body:e||{}},t);const n={method:"POST",url:"/api/collections/"+encodeURIComponent(this.collectionIdOrName)+"/records"};this.prepareRequest(n,t),this.requests.push(n)}update(e,t,n){n=Object.assign({body:t||{}},n);const r={method:"PATCH",url:"/api/collections/"+encodeURIComponent(this.collectionIdOrName)+"/records/"+encodeURIComponent(e)};this.prepareRequest(r,n),this.requests.push(r)}delete(e,t){t=Object.assign({},t);const n={method:"DELETE",url:"/api/collections/"+encodeURIComponent(this.collectionIdOrName)+"/records/"+encodeURIComponent(e)};this.prepareRequest(n,t),this.requests.push(n)}prepareRequest(e,t){if(b(t),e.headers=t.headers,e.json={},e.files={},void 0!==t.query){const n=w(t.query);n&&(e.url+=(e.url.includes("?")?"&":"?")+n)}let n=t.body;M(n)&&(n=function(e){let t={};return e.forEach((e,n)=>{if("@jsonPayload"===n&&"string"==typeof e)try{let n=JSON.parse(e);Object.assign(t,n)}catch(e){console.warn("@jsonPayload error:",e)}else void 0!==t[n]?(Array.isArray(t[n])||(t[n]=[t[n]]),t[n].push(D(e))):t[n]=D(e)}),t}(n));for(const r in n){const t=n[r];if(L(t))e.files[r]=e.files[r]||[],e.files[r].push(t);else if(Array.isArray(t)){const n=[],i=[];for(const e of t)L(e)?n.push(e):i.push(e);if(n.length>0&&n.length==t.length){e.files[r]=e.files[r]||[];for(let t of n)e.files[r].push(t)}else if(e.json[r]=i,n.length>0){let t=r;r.startsWith("+")||r.endsWith("+")||(t+="+"),e.files[t]=e.files[t]||[];for(let r of n)e.files[t].push(r)}}else e.json[r]=t}}}class B{get baseUrl(){return this.baseURL}set baseUrl(e){this.baseURL=e}constructor(e="/",t,n="en-US"){this.cancelControllers={},this.recordServices={},this.enableAutoCancellation=!0,this.baseURL=e,this.lang=n,t?this.authStore=t:"undefined"!=typeof window&&window.Deno?this.authStore=new f:this.authStore=new m,this.collections=new O(this),this.files=new P(this),this.logs=new $(this),this.settings=new v(this),this.realtime=new E(this),this.health=new R(this),this.backups=new A(this),this.crons=new I(this)}get admins(){return this.collection("_superusers")}createBatch(){return new z(this)}collection(e){return this.recordServices[e]||(this.recordServices[e]=new k(this,e)),this.recordServices[e]}autoCancellation(e){return this.enableAutoCancellation=!!e,this}cancelRequest(e){return this.cancelControllers[e]&&(this.cancelControllers[e].abort(),delete this.cancelControllers[e]),this}cancelAllRequests(){for(let e in this.cancelControllers)this.cancelControllers[e].abort();return this.cancelControllers={},this}filter(e,t){if(!t)return e;for(let n in t){let r=t[n];switch(typeof r){case"boolean":case"number":r=""+r;break;case"string":r="'"+r.replace(/'/g,"\\'")+"'";break;default:r=null===r?"null":r instanceof Date?"'"+r.toISOString().replace("T"," ")+"'":"'"+JSON.stringify(r).replace(/'/g,"\\'")+"'"}e=e.replaceAll("{:"+n+"}",r)}return e}getFileUrl(e,t,n={}){return console.warn("Please replace pb.getFileUrl() with pb.files.getURL()"),this.files.getURL(e,t,n)}buildUrl(e){return console.warn("Please replace pb.buildUrl() with pb.buildURL()"),this.buildURL(e)}buildURL(e){let t=this.baseURL;return"undefined"==typeof window||!window.location||t.startsWith("https://")||t.startsWith("http://")||(t=window.location.origin?.endsWith("/")?window.location.origin.substring(0,window.location.origin.length-1):window.location.origin||"",this.baseURL.startsWith("/")||(t+=window.location.pathname||"/",t+=t.endsWith("/")?"":"/"),t+=this.baseURL),e&&(t+=t.endsWith("/")?"":"/",t+=e.startsWith("/")?e.substring(1):e),t}async send(e,t){t=this.initSendOptions(e,t);let n=this.buildURL(e);if(this.beforeSend){const e=Object.assign({},await this.beforeSend(n,t));void 0!==e.url||void 0!==e.options?(n=e.url||n,t=e.options||t):Object.keys(e).length&&(t=e,console?.warn&&console.warn("Deprecated format of beforeSend return: please use `return { url, options }`, instead of `return options`."))}if(void 0!==t.query){const e=w(t.query);e&&(n+=(n.includes("?")?"&":"?")+e),delete t.query}return"application/json"==this.getHeader(t.headers,"Content-Type")&&t.body&&"string"!=typeof t.body&&(t.body=JSON.stringify(t.body)),(t.fetch||fetch)(n,t).then(async e=>{let n={};try{n=await e.json()}catch(e){}if(this.afterSend&&(n=await this.afterSend(e,n,t)),e.status>=400)throw new r({url:e.url,status:e.status,data:n});return n}).catch(e=>{throw new r(e)})}initSendOptions(e,t){if((t=Object.assign({method:"GET"},t)).body=function(e){if("undefined"==typeof FormData||void 0===e||"object"!=typeof e||null===e||M(e)||!N(e))return e;const t=new FormData;for(const n in e){const r=e[n];if(void 0!==r)if("object"!=typeof r||N({data:r})){const e=Array.isArray(r)?r:[r];for(let r of e)t.append(n,r)}else{let e={};e[n]=r,t.append("@jsonPayload",JSON.stringify(e))}}return t}(t.body),b(t),t.query=Object.assign({},t.params,t.query),void 0===t.requestKey&&(!1===t.$autoCancel||!1===t.query.$autoCancel?t.requestKey=null:(t.$cancelKey||t.query.$cancelKey)&&(t.requestKey=t.$cancelKey||t.query.$cancelKey)),delete t.$autoCancel,delete t.query.$autoCancel,delete t.$cancelKey,delete t.query.$cancelKey,null!==this.getHeader(t.headers,"Content-Type")||M(t.body)||(t.headers=Object.assign({},t.headers,{"Content-Type":"application/json"})),null===this.getHeader(t.headers,"Accept-Language")&&(t.headers=Object.assign({},t.headers,{"Accept-Language":this.lang})),this.authStore.token&&null===this.getHeader(t.headers,"Authorization")&&(t.headers=Object.assign({},t.headers,{Authorization:this.authStore.token})),this.enableAutoCancellation&&null!==t.requestKey){const n=t.requestKey||(t.method||"GET")+e;delete t.requestKey,this.cancelRequest(n);const r=new AbortController;this.cancelControllers[n]=r,t.signal=r.signal}return t}getHeader(e,t){e=e||{},t=t.toLowerCase();for(let n in e)if(n.toLowerCase()==t)return e[n];return null}}},9092:e=>{function t(e,t,n,r,i,s,o){try{var a=e[s](o),l=a.value}catch(c){return void n(c)}a.done?t(l):Promise.resolve(l).then(r,i)}function n(e){return function(){var n=this,r=arguments;return new Promise(function(i,s){var o=e.apply(n,r);function a(e){t(o,i,s,a,l,"next",e)}function l(e){t(o,i,s,a,l,"throw",e)}a(void 0)})}}e.exports=n},9396:(e,t,n)=>{"use strict";n.r(t),n.d(t,{VoiceRecorderWeb:()=>$});var r=n(6546);function i(e){e.CapacitorUtils.Synapse=new Proxy({},{get(t,n){return new Proxy({},{get(t,r){return(t,i,s)=>{const o=e.Capacitor.Plugins[n];void 0!==o?"function"==typeof o[r]?(async()=>{try{const e=await o[r](t);i(e)}catch(e){s(e)}})():s(new Error(`Method ${r} not found in Capacitor plugin ${n}`)):s(new Error(`Capacitor plugin ${n} not found`))}}})}})}function s(e){e.CapacitorUtils.Synapse=new Proxy({},{get(t,n){return e.cordova.plugins[n]}})}function o(e=!1){typeof window>"u"||(window.CapacitorUtils=window.CapacitorUtils||{},void 0===window.Capacitor||e?void 0!==window.cordova&&s(window):i(window))}n(7448);const a=(0,r.F3)("Filesystem",{web:()=>n.e(121).then(n.bind(n,9956)).then(e=>new e.FilesystemWeb)});o();const l=(0,r.F3)("BlobWriter");function c(e){return window.btoa(Array.from(new Uint8Array(e)).map(function(e){return String.fromCharCode(e)}).join(""))}function u({path:e,directory:t,blob:n,recursive:r}){return a.writeFile({directory:t,path:e,recursive:r,data:""}).then(function(){return new Promise(function(r,i){function s(e){i(e.target.error)}const o=window.indexedDB.open("Disc");o.onerror=s,o.onsuccess=function(){const i=o.result,a=i.transaction("FileStorage","readwrite");a.onerror=s;const l=a.objectStore("FileStorage"),c=`/${t}/${e.replace(/^\//,"")}`,u=l.get(c);u.onsuccess=function(){u.result.size=n.size,u.result.content=n;const e=l.put(u.result);e.onsuccess=function(){r(void 0)}}}})})}function d({path:e,directory:t,blob:n,recursive:r}){return a.writeFile({directory:t,path:e,recursive:r,data:""}).then(function r(){if(0===n.size)return Promise.resolve();const i=393216,s=n.slice(0,i);return n=n.slice(i),new Response(s).arrayBuffer().then(function(n){return a.appendFile({directory:t,path:e,data:c(n)})}).then(r)})}function p(e){const{path:t,directory:n,blob:i,fast_mode:s=!1,recursive:o,on_fallback:c}=e;return i&&Number.isSafeInteger(i.size)&&"string"===typeof i.type?"web"===r.Ii.getPlatform()?s?u(e):d(e):Promise.all([l.get_config(),a.getUri({path:t,directory:n})]).then(function([e,t]){const n=t.uri.replace("file://",""),r=window.CapacitorWebFetch||window.fetch;return r(e.base_url+n+(o?"?recursive=true":""),{headers:{authorization:e.auth_token},method:"put",body:i}).then(function(e){if(204!==e.status)throw new Error("Bad HTTP status: "+e.status);return t.uri})}).catch(function(t){return void 0!==c&&c(t),d(e)}):Promise.reject(new Error("Not a Blob."))}const h=Object.freeze(p);var f=n(6982),m=n(5992);const g=()=>({value:!0}),v=()=>({value:!1}),y=()=>new Error("MISSING_PERMISSION"),b=()=>new Error("ALREADY_RECORDING"),w=()=>new Error("DEVICE_CANNOT_VOICE_RECORD"),x=()=>new Error("FAILED_TO_RECORD"),E=()=>new Error("EMPTY_RECORDING"),S=()=>new Error("RECORDING_HAS_NOT_STARTED"),C=()=>new Error("FAILED_TO_FETCH_RECORDING"),T=()=>new Error("COULD_NOT_QUERY_PERMISSION_STATUS"),k={"audio/aac":".aac","audio/mp4":".mp3","audio/webm;codecs=opus":".ogg","audio/webm":".ogg","audio/ogg;codecs=opus":".ogg"},_=()=>new Promise(()=>{});class O{constructor(){this.mediaRecorder=null,this.chunks=[],this.pendingResult=_()}static async canDeviceVoiceRecord(){var e;return null==(null===(e=null===navigator||void 0===navigator?void 0:navigator.mediaDevices)||void 0===e?void 0:e.getUserMedia)||null==O.getSupportedMimeType()?v():g()}async startRecording(e){if(null!=this.mediaRecorder)throw b();const t=await O.canDeviceVoiceRecord();if(!t.value)throw w();const n=await O.hasAudioRecordingPermission().catch(()=>g());if(!n.value)throw y();return navigator.mediaDevices.getUserMedia({audio:!0}).then(t=>this.onSuccessfullyStartedRecording(t,e)).catch(this.onFailedToStartRecording.bind(this))}async stopRecording(){if(null==this.mediaRecorder)throw S();try{return this.mediaRecorder.stop(),this.mediaRecorder.stream.getTracks().forEach(e=>e.stop()),this.pendingResult}catch(e){throw C()}finally{this.prepareInstanceForNextOperation()}}static async hasAudioRecordingPermission(){return null==navigator.permissions.query?null==navigator.mediaDevices?Promise.reject(T()):navigator.mediaDevices.getUserMedia({audio:!0}).then(()=>g()).catch(()=>{throw T()}):navigator.permissions.query({name:"microphone"}).then(e=>({value:"granted"===e.state})).catch(()=>{throw T()})}static async requestAudioRecordingPermission(){const e=await O.hasAudioRecordingPermission().catch(()=>v());return e.value?g():navigator.mediaDevices.getUserMedia({audio:!0}).then(()=>g()).catch(()=>v())}pauseRecording(){if(null==this.mediaRecorder)throw S();return"recording"===this.mediaRecorder.state?(this.mediaRecorder.pause(),Promise.resolve(g())):Promise.resolve(v())}resumeRecording(){if(null==this.mediaRecorder)throw S();return"paused"===this.mediaRecorder.state?(this.mediaRecorder.resume(),Promise.resolve(g())):Promise.resolve(v())}getCurrentStatus(){return null==this.mediaRecorder?Promise.resolve({status:m.k.NONE}):"recording"===this.mediaRecorder.state?Promise.resolve({status:m.k.RECORDING}):"paused"===this.mediaRecorder.state?Promise.resolve({status:m.k.PAUSED}):Promise.resolve({status:m.k.NONE})}static getSupportedMimeType(){if(null==(null===MediaRecorder||void 0===MediaRecorder?void 0:MediaRecorder.isTypeSupported))return null;const e=Object.keys(k).find(e=>MediaRecorder.isTypeSupported(e));return null!==e&&void 0!==e?e:null}onSuccessfullyStartedRecording(e,t){return this.pendingResult=new Promise((n,r)=>{this.mediaRecorder=new MediaRecorder(e),this.mediaRecorder.onerror=()=>{this.prepareInstanceForNextOperation(),r(x())},this.mediaRecorder.onstop=async()=>{var e,i,s;const o=O.getSupportedMimeType();if(null==o)return this.prepareInstanceForNextOperation(),void r(C());const a=new Blob(this.chunks,{type:o});if(a.size<=0)return this.prepareInstanceForNextOperation(),void r(E());let l,c;if(null!=t){const n=null!==(s=null===(i=null===(e=t.subDirectory)||void 0===e?void 0:e.match(/^\/?(.+[^/])\/?$/))||void 0===i?void 0:i[1])&&void 0!==s?s:"";l=`${n}/recording-${(new Date).getTime()}${k[o]}`,await h({blob:a,directory:t.directory,fast_mode:!0,path:l,recursive:!0})}else c=await O.blobToBase64(a);const u=await(0,f.A)(a);this.prepareInstanceForNextOperation(),n({value:{recordDataBase64:c,mimeType:o,msDuration:1e3*u,path:l}})},this.mediaRecorder.ondataavailable=e=>this.chunks.push(e.data),this.mediaRecorder.start()}),g()}onFailedToStartRecording(){throw this.prepareInstanceForNextOperation(),x()}static blobToBase64(e){return new Promise(t=>{const n=new FileReader;n.onloadend=()=>{const e=String(n.result),r=e.split("base64,"),i=r.length>1?r[1]:e;t(i.trim())},n.readAsDataURL(e)})}prepareInstanceForNextOperation(){if(null!=this.mediaRecorder&&"recording"===this.mediaRecorder.state)try{this.mediaRecorder.stop()}catch(e){console.warn("While trying to stop a media recorder, an error was thrown",e)}this.pendingResult=_(),this.mediaRecorder=null,this.chunks=[]}}class $ extends r.E_{constructor(){super(...arguments),this.voiceRecorderInstance=new O}canDeviceVoiceRecord(){return O.canDeviceVoiceRecord()}hasAudioRecordingPermission(){return O.hasAudioRecordingPermission()}requestAudioRecordingPermission(){return O.requestAudioRecordingPermission()}startRecording(e){return this.voiceRecorderInstance.startRecording(e)}stopRecording(){return this.voiceRecorderInstance.stopRecording()}pauseRecording(){return this.voiceRecorderInstance.pauseRecording()}resumeRecording(){return this.voiceRecorderInstance.resumeRecording()}getCurrentStatus(){return this.voiceRecorderInstance.getCurrentStatus()}}},9417:(e,t,n)=>{"use strict";n.d(t,{eA:()=>o});var r,i,s=n(6546);(function(e){e["Dark"]="DARK",e["Light"]="LIGHT",e["Default"]="DEFAULT"})(r||(r={})),function(e){e["None"]="NONE",e["Slide"]="SLIDE",e["Fade"]="FADE"}(i||(i={}));const o=(0,s.F3)("StatusBar")},9603:(e,t,n)=>{"use strict";n.d(t,{uA:()=>C,Xe:()=>g,kv:()=>_,lD:()=>r["default"],ox:()=>O});var r=n(5471);
/**
  * vue-class-component v7.2.6
  * (c) 2015-present Evan You
  * @license MIT
  */
function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e){return a(e)||l(e)||c()}function a(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}function l(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function u(){return"undefined"!==typeof Reflect&&Reflect.defineMetadata&&Reflect.getOwnMetadataKeys}function d(e,t){p(e,t),Object.getOwnPropertyNames(t.prototype).forEach(function(n){p(e.prototype,t.prototype,n)}),Object.getOwnPropertyNames(t).forEach(function(n){p(e,t,n)})}function p(e,t,n){var r=n?Reflect.getOwnMetadataKeys(t,n):Reflect.getOwnMetadataKeys(t);r.forEach(function(r){var i=n?Reflect.getOwnMetadata(r,t,n):Reflect.getOwnMetadata(r,t);n?Reflect.defineMetadata(r,i,e,n):Reflect.defineMetadata(r,i,e)})}var h={__proto__:[]},f=h instanceof Array;function m(e){return function(t,n,r){var i="function"===typeof t?t:t.constructor;i.__decorators__||(i.__decorators__=[]),"number"!==typeof r&&(r=void 0),i.__decorators__.push(function(t){return e(t,n,r)})}}function g(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r["default"].extend({mixins:t})}function v(e){var t=i(e);return null==e||"object"!==t&&"function"!==t}function y(e,t){var n=t.prototype._init;t.prototype._init=function(){var t=this,n=Object.getOwnPropertyNames(e);if(e.$options.props)for(var r in e.$options.props)e.hasOwnProperty(r)||n.push(r);n.forEach(function(n){Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){e[n]=t},configurable:!0})})};var r=new t;t.prototype._init=n;var i={};return Object.keys(r).forEach(function(e){void 0!==r[e]&&(i[e]=r[e])}),i}var b=["data","beforeCreate","created","beforeMount","mounted","beforeDestroy","destroyed","beforeUpdate","updated","activated","deactivated","render","errorCaptured","serverPrefetch"];function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.name=t.name||e._componentTag||e.name;var n=e.prototype;Object.getOwnPropertyNames(n).forEach(function(e){if("constructor"!==e)if(b.indexOf(e)>-1)t[e]=n[e];else{var r=Object.getOwnPropertyDescriptor(n,e);void 0!==r.value?"function"===typeof r.value?(t.methods||(t.methods={}))[e]=r.value:(t.mixins||(t.mixins=[])).push({data:function(){return s({},e,r.value)}}):(r.get||r.set)&&((t.computed||(t.computed={}))[e]={get:r.get,set:r.set})}}),(t.mixins||(t.mixins=[])).push({data:function(){return y(this,e)}});var i=e.__decorators__;i&&(i.forEach(function(e){return e(t)}),delete e.__decorators__);var o=Object.getPrototypeOf(e.prototype),a=o instanceof r["default"]?o.constructor:r["default"],l=a.extend(t);return E(l,e,a),u()&&d(l,e),l}var x={prototype:!0,arguments:!0,callee:!0,caller:!0};function E(e,t,n){Object.getOwnPropertyNames(t).forEach(function(r){if(!x[r]){var i=Object.getOwnPropertyDescriptor(e,r);if(!i||i.configurable){var s=Object.getOwnPropertyDescriptor(t,r);if(!f){if("cid"===r)return;var o=Object.getOwnPropertyDescriptor(n,r);if(!v(s.value)&&o&&o.value===s.value)return}0,Object.defineProperty(e,r,s)}}})}function S(e){return"function"===typeof e?w(e):function(t){return w(t,e)}}S.registerHooks=function(e){b.push.apply(b,o(e))};const C=S;var T="undefined"!==typeof Reflect&&"undefined"!==typeof Reflect.getMetadata;function k(e,t,n){if(T&&!Array.isArray(e)&&"function"!==typeof e&&!e.hasOwnProperty("type")&&"undefined"===typeof e.type){var r=Reflect.getMetadata("design:type",t,n);r!==Object&&(e.type=r)}}function _(e){return void 0===e&&(e={}),function(t,n){k(e,t,n),m(function(t,n){(t.props||(t.props={}))[n]=e})(t,n)}}function O(e,t){void 0===t&&(t={});var n=t.deep,r=void 0!==n&&n,i=t.immediate,s=void 0!==i&&i;return m(function(t,n){"object"!==typeof t.watch&&(t.watch=Object.create(null));var i=t.watch;"object"!==typeof i[e]||Array.isArray(i[e])?"undefined"===typeof i[e]&&(i[e]=[]):i[e]=[i[e]],i[e].push({handler:n,deep:r,immediate:s})})}},9956:(e,t,n)=>{"use strict";n.r(t),n.d(t,{FilesystemWeb:()=>a});var r=n(6546),i=n(7448);function s(e){const t=e.split("/").filter(e=>"."!==e),n=[];return t.forEach(e=>{".."===e&&n.length>0&&".."!==n[n.length-1]?n.pop():n.push(e)}),n.join("/")}function o(e,t){e=s(e),t=s(t);const n=e.split("/"),r=t.split("/");return e!==t&&n.every((e,t)=>e===r[t])}class a extends r.E_{constructor(){super(...arguments),this.DB_VERSION=1,this.DB_NAME="Disc",this._writeCmds=["add","put","delete"],this.downloadFile=async e=>{var t,n;const i=(0,r.EA)(e,e.webFetchExtra),s=await fetch(e.url,i);let o;if(e.progress)if(null===s||void 0===s?void 0:s.body){const t=s.body.getReader();let n=0;const r=[],i=s.headers.get("content-type"),a=parseInt(s.headers.get("content-length")||"0",10);while(1){const{done:i,value:s}=await t.read();if(i)break;r.push(s),n+=(null===s||void 0===s?void 0:s.length)||0;const o={url:e.url,bytes:n,contentLength:a};this.notifyListeners("progress",o)}const l=new Uint8Array(n);let c=0;for(const e of r)"undefined"!==typeof e&&(l.set(e,c),c+=e.length);o=new Blob([l.buffer],{type:i||void 0})}else o=new Blob;else o=await s.blob();const a=await this.writeFile({path:e.path,directory:null!==(t=e.directory)&&void 0!==t?t:void 0,recursive:null!==(n=e.recursive)&&void 0!==n&&n,data:o});return{path:a.uri,blob:o}}}readFileInChunks(e,t){throw this.unavailable("Method not implemented.")}async initDb(){if(void 0!==this._db)return this._db;if(!("indexedDB"in window))throw this.unavailable("This browser doesn't support IndexedDB");return new Promise((e,t)=>{const n=indexedDB.open(this.DB_NAME,this.DB_VERSION);n.onupgradeneeded=a.doUpgrade,n.onsuccess=()=>{this._db=n.result,e(n.result)},n.onerror=()=>t(n.error),n.onblocked=()=>{console.warn("db blocked")}})}static doUpgrade(e){const t=e.target,n=t.result;switch(e.oldVersion){case 0:case 1:default:{n.objectStoreNames.contains("FileStorage")&&n.deleteObjectStore("FileStorage");const e=n.createObjectStore("FileStorage",{keyPath:"path"});e.createIndex("by_folder","folder")}}}async dbRequest(e,t){const n=-1!==this._writeCmds.indexOf(e)?"readwrite":"readonly";return this.initDb().then(r=>new Promise((i,s)=>{const o=r.transaction(["FileStorage"],n),a=o.objectStore("FileStorage"),l=a[e](...t);l.onsuccess=()=>i(l.result),l.onerror=()=>s(l.error)}))}async dbIndexRequest(e,t,n){const r=-1!==this._writeCmds.indexOf(t)?"readwrite":"readonly";return this.initDb().then(i=>new Promise((s,o)=>{const a=i.transaction(["FileStorage"],r),l=a.objectStore("FileStorage"),c=l.index(e),u=c[t](...n);u.onsuccess=()=>s(u.result),u.onerror=()=>o(u.error)}))}getPath(e,t){const n=void 0!==t?t.replace(/^[/]+|[/]+$/g,""):"";let r="";return void 0!==e&&(r+="/"+e),""!==t&&(r+="/"+n),r}async clear(){const e=await this.initDb(),t=e.transaction(["FileStorage"],"readwrite"),n=t.objectStore("FileStorage");n.clear()}async readFile(e){const t=this.getPath(e.directory,e.path),n=await this.dbRequest("get",[t]);if(void 0===n)throw Error("File does not exist.");return{data:n.content?n.content:""}}async writeFile(e){const t=this.getPath(e.directory,e.path);let n=e.data;const r=e.encoding,i=e.recursive,s=await this.dbRequest("get",[t]);if(s&&"directory"===s.type)throw Error("The supplied path is a directory.");const o=t.substr(0,t.lastIndexOf("/")),a=await this.dbRequest("get",[o]);if(void 0===a){const t=o.indexOf("/",1);if(-1!==t){const n=o.substr(t);await this.mkdir({path:n,directory:e.directory,recursive:i})}}if(!r&&!(n instanceof Blob)&&(n=n.indexOf(",")>=0?n.split(",")[1]:n,!this.isBase64String(n)))throw Error("The supplied data is not valid base64 content.");const l=Date.now(),c={path:t,folder:o,type:"file",size:n instanceof Blob?n.size:n.length,ctime:l,mtime:l,content:n};return await this.dbRequest("put",[c]),{uri:c.path}}async appendFile(e){const t=this.getPath(e.directory,e.path);let n=e.data;const r=e.encoding,i=t.substr(0,t.lastIndexOf("/")),s=Date.now();let o=s;const a=await this.dbRequest("get",[t]);if(a&&"directory"===a.type)throw Error("The supplied path is a directory.");const l=await this.dbRequest("get",[i]);if(void 0===l){const t=i.indexOf("/",1);if(-1!==t){const n=i.substr(t);await this.mkdir({path:n,directory:e.directory,recursive:!0})}}if(!r&&!this.isBase64String(n))throw Error("The supplied data is not valid base64 content.");if(void 0!==a){if(a.content instanceof Blob)throw Error("The occupied entry contains a Blob object which cannot be appended to.");n=void 0===a.content||r?a.content+n:btoa(atob(a.content)+atob(n)),o=a.ctime}const c={path:t,folder:i,type:"file",size:n.length,ctime:o,mtime:s,content:n};await this.dbRequest("put",[c])}async deleteFile(e){const t=this.getPath(e.directory,e.path),n=await this.dbRequest("get",[t]);if(void 0===n)throw Error("File does not exist.");const r=await this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(t)]);if(0!==r.length)throw Error("Folder is not empty.");await this.dbRequest("delete",[t])}async mkdir(e){const t=this.getPath(e.directory,e.path),n=e.recursive,r=t.substr(0,t.lastIndexOf("/")),i=(t.match(/\//g)||[]).length,s=await this.dbRequest("get",[r]),o=await this.dbRequest("get",[t]);if(1===i)throw Error("Cannot create Root directory");if(void 0!==o)throw Error("Current directory does already exist.");if(!n&&2!==i&&void 0===s)throw Error("Parent directory must exist");if(n&&2!==i&&void 0===s){const t=r.substr(r.indexOf("/",1));await this.mkdir({path:t,directory:e.directory,recursive:n})}const a=Date.now(),l={path:t,folder:r,type:"directory",size:0,ctime:a,mtime:a};await this.dbRequest("put",[l])}async rmdir(e){const{path:t,directory:n,recursive:r}=e,i=this.getPath(n,t),s=await this.dbRequest("get",[i]);if(void 0===s)throw Error("Folder does not exist.");if("directory"!==s.type)throw Error("Requested path is not a directory");const o=await this.readdir({path:t,directory:n});if(0!==o.files.length&&!r)throw Error("Folder is not empty");for(const a of o.files){const e=`${t}/${a.name}`,i=await this.stat({path:e,directory:n});"file"===i.type?await this.deleteFile({path:e,directory:n}):await this.rmdir({path:e,directory:n,recursive:r})}await this.dbRequest("delete",[i])}async readdir(e){const t=this.getPath(e.directory,e.path),n=await this.dbRequest("get",[t]);if(""!==e.path&&void 0===n)throw Error("Folder does not exist.");const r=await this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(t)]),i=await Promise.all(r.map(async e=>{let n=await this.dbRequest("get",[e]);return void 0===n&&(n=await this.dbRequest("get",[e+"/"])),{name:e.substring(t.length+1),type:n.type,size:n.size,ctime:n.ctime,mtime:n.mtime,uri:n.path}}));return{files:i}}async getUri(e){const t=this.getPath(e.directory,e.path);let n=await this.dbRequest("get",[t]);return void 0===n&&(n=await this.dbRequest("get",[t+"/"])),{uri:(null===n||void 0===n?void 0:n.path)||t}}async stat(e){const t=this.getPath(e.directory,e.path);let n=await this.dbRequest("get",[t]);if(void 0===n&&(n=await this.dbRequest("get",[t+"/"])),void 0===n)throw Error("Entry does not exist.");return{name:n.path.substring(t.length+1),type:n.type,size:n.size,ctime:n.ctime,mtime:n.mtime,uri:n.path}}async rename(e){await this._copy(e,!0)}async copy(e){return this._copy(e,!1)}async requestPermissions(){return{publicStorage:"granted"}}async checkPermissions(){return{publicStorage:"granted"}}async _copy(e,t=!1){let{toDirectory:n}=e;const{to:r,from:s,directory:a}=e;if(!r||!s)throw Error("Both to and from must be provided");n||(n=a);const l=this.getPath(a,s),c=this.getPath(n,r);if(l===c)return{uri:c};if(o(l,c))throw Error("To path cannot contain the from path");let u;try{u=await this.stat({path:r,directory:n})}catch(f){const e=r.split("/");e.pop();const t=e.join("/");if(e.length>0){const e=await this.stat({path:t,directory:n});if("directory"!==e.type)throw new Error("Parent directory of the to path is a file")}}if(u&&"directory"===u.type)throw new Error("Cannot overwrite a directory with a file");const d=await this.stat({path:s,directory:a}),p=async(e,t,r)=>{const i=this.getPath(n,e),s=await this.dbRequest("get",[i]);s.ctime=t,s.mtime=r,await this.dbRequest("put",[s])},h=d.ctime?d.ctime:Date.now();switch(d.type){case"file":{const e=await this.readFile({path:s,directory:a});let o;t&&await this.deleteFile({path:s,directory:a}),e.data instanceof Blob||this.isBase64String(e.data)||(o=i.Wi.UTF8);const l=await this.writeFile({path:r,directory:n,data:e.data,encoding:o});return t&&await p(r,h,d.mtime),l}case"directory":{if(u)throw Error("Cannot move a directory over an existing object");try{await this.mkdir({path:r,directory:n,recursive:!1}),t&&await p(r,h,d.mtime)}catch(f){}const e=(await this.readdir({path:s,directory:a})).files;for(const i of e)await this._copy({from:`${s}/${i.name}`,to:`${r}/${i.name}`,directory:a,toDirectory:n},t);t&&await this.rmdir({path:s,directory:a})}}return{uri:c}}isBase64String(e){try{return btoa(atob(e))==e}catch(t){return!1}}}a._debug=!0}}]);