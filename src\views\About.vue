<template>
  <div class="about-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- About主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--about">
      <h1 class="app-title">{{ $t('pageTitle.about') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 内容区域 -->
        <div class="about-content">
          <div class="about-card">
            <h2 class="content-title">{{ $t('about.title') }}</h2>
            <div class="tech-stack">
              <h3 class="section-title">{{ $t('about.techStack') }}</h3>
              <ul class="tech-list">
                <li>{{ $t('aboutDetail.techStack.vue') }}</li>
                <li>{{ $t('aboutDetail.techStack.typescript') }}</li>
                <li>{{ $t('aboutDetail.techStack.tailwind') }}</li>
                <li>{{ $t('aboutDetail.techStack.capacitor') }}</li>
              </ul>
            </div>
            <div class="features">
              <h3 class="section-title">{{ $t('about.features') }}</h3>
              <ul class="feature-list">
                <li>{{ $t('aboutDetail.features.smartNavigation') }}</li>
                <li>{{ $t('aboutDetail.features.realtimeLocation') }}</li>
                <li>{{ $t('aboutDetail.features.multiLanguage') }}</li>
                <li>{{ $t('aboutDetail.features.crossPlatform') }}</li>
              </ul>
            </div>
            <!-- 临时添加更多内容来测试滚动 -->
            <div class="extra-sections">
              <div class="version-info">
                <h3 class="section-title">{{ $t('about.version') }}</h3>
                <ul class="version-list">
                  <li>{{ $t('aboutDetail.version.current') }}</li>
                  <li>{{ $t('aboutDetail.version.releaseDate') }}</li>
                  <li>{{ $t('aboutDetail.version.updateFrequency') }}</li>
                  <li>{{ $t('aboutDetail.version.supportedPlatforms') }}</li>
                </ul>
              </div>
              <div class="team-info">
                <h3 class="section-title">{{ $t('about.team') }}</h3>
                <ul class="team-list">
                  <li>{{ $t('aboutDetail.team.frontend') }}</li>
                  <li>{{ $t('aboutDetail.team.mobile') }}</li>
                  <li>{{ $t('aboutDetail.team.design') }}</li>
                  <li>{{ $t('aboutDetail.team.data') }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

interface DeveloperInfo {
  name: string
  role: string
  photo?: string
  github?: string
  email?: string
}

interface ProjectInfo {
  name: string
  version: string
  description: string
  buildDate: string
  technologies: string[]
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class About extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#34495E' // About页面的深灰色背景
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
.about-content {
  width: 1800px;
  margin: 0 auto;
}

.about-card {
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  backdrop-filter: blur(20px);
  padding: 80px;
  
  /* 毛玻璃效果 */
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
}

.content-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 96px;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 48px;
  color: #FFFFFF;
  margin-bottom: 30px;
  margin-top: 50px;
}

.section-title:first-of-type {
  margin-top: 0;
}

.tech-list, .feature-list, .version-list, .team-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-list li, .feature-list li, .version-list li, .team-list li {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 1.6;
  margin-bottom: 20px;
  padding-left: 40px;
  position: relative;
}

.tech-list li::before, .feature-list li::before, .version-list li::before, .team-list li::before {
  content: '•';
  color: #00EEFF;
  font-size: 48px;
  position: absolute;
  left: 0;
  top: -8px;
}

.extra-sections {
  margin-top: 60px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

@media (max-width: 1200px) {
  .extra-sections {
    grid-template-columns: 1fr;
  }
}
</style> 