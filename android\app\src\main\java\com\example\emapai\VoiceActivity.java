package com.example.emapai;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.MediaRecorder;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.io.IOException;

public class VoiceActivity extends AppCompatActivity {

    private static final int REQUEST_MICROPHONE = 200;
    private MediaRecorder mediaRecorder;
    private Button recordButton;
    private TextView statusText;
    private boolean isRecording = false;
    private String audioFilePath;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建布局
        createLayout();
        
        // 检查麦克风权限
        checkMicrophonePermission();
        
        // 设置录音文件路径
        audioFilePath = getExternalCacheDir().getAbsolutePath() + "/voice_record.3gp";
    }

    private void createLayout() {
        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(Color.parseColor("#016513")); // 绿色背景
        mainLayout.setPadding(100, 200, 100, 200);
        
        // 创建标题
        TextView titleText = new TextView(this);
        titleText.setText("AI 对话");
        titleText.setTextColor(Color.WHITE);
        titleText.setTextSize(48);
        titleText.setGravity(android.view.Gravity.CENTER);
        titleText.setPadding(0, 0, 0, 100);
        
        // 创建状态文本
        statusText = new TextView(this);
        statusText.setText("点击按钮开始录音");
        statusText.setTextColor(Color.WHITE);
        statusText.setTextSize(32);
        statusText.setGravity(android.view.Gravity.CENTER);
        statusText.setPadding(0, 50, 0, 50);
        
        // 创建录音按钮
        recordButton = new Button(this);
        recordButton.setText("开始录音");
        recordButton.setTextSize(24);
        recordButton.setPadding(80, 40, 80, 40);
        recordButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isRecording) {
                    stopRecording();
                } else {
                    startRecording();
                }
            }
        });
        
        // 创建返回按钮
        Button backButton = new Button(this);
        backButton.setText("返回");
        backButton.setTextSize(20);
        backButton.setPadding(60, 30, 60, 30);
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish(); // 关闭当前Activity
            }
        });
        
        // 添加到布局
        mainLayout.addView(titleText);
        mainLayout.addView(statusText);
        mainLayout.addView(recordButton);
        mainLayout.addView(backButton);
        
        // 设置为内容视图
        setContentView(mainLayout);
    }

    private void startRecording() {
        try {
            // 初始化MediaRecorder
            mediaRecorder = new MediaRecorder();
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP);
            mediaRecorder.setOutputFile(audioFilePath);
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB);
            
            // 准备并开始录音
            mediaRecorder.prepare();
            mediaRecorder.start();
            
            // 更新UI
            isRecording = true;
            recordButton.setText("停止录音");
            statusText.setText("正在录音...");
            recordButton.setBackgroundColor(Color.RED);
            
            Toast.makeText(this, "开始录音", Toast.LENGTH_SHORT).show();
            
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "录音启动失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void stopRecording() {
        if (mediaRecorder != null && isRecording) {
            try {
                mediaRecorder.stop();
                mediaRecorder.release();
                mediaRecorder = null;
                
                // 更新UI
                isRecording = false;
                recordButton.setText("开始录音");
                statusText.setText("录音已停止，可以重新开始");
                recordButton.setBackgroundColor(Color.LTGRAY);
                
                Toast.makeText(this, "录音已停止", Toast.LENGTH_SHORT).show();
                
            } catch (RuntimeException e) {
                e.printStackTrace();
                Toast.makeText(this, "停止录音失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            }
        }
    }

    private void checkMicrophonePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.RECORD_AUDIO},
                    REQUEST_MICROPHONE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_MICROPHONE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "麦克风权限已授予", Toast.LENGTH_SHORT).show();
                statusText.setText("麦克风权限已获得，点击按钮开始录音");
            } else {
                Toast.makeText(this, "麦克风权限被拒绝", Toast.LENGTH_SHORT).show();
                statusText.setText("需要麦克风权限才能使用语音功能");
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mediaRecorder != null) {
            mediaRecorder.release();
            mediaRecorder = null;
        }
    }
}
