export interface WebViewPlugin {
  /**
   * 在当前页面嵌入显示WebView
   */
  showEmbedded(options: WebViewOptions): Promise<WebViewResult>;
  
  /**
   * 关闭嵌入的WebView
   */
  hideEmbedded(): Promise<void>;
  
  /**
   * 监听WebView事件
   */
  addListener(
    eventName: 'pageStarted' | 'pageFinished' | 'pageError' | 'shouldOverrideUrl',
    listenerFunc: (event: WebViewEvent) => void,
  ): Promise<{ remove: () => void }>;
}

export interface WebViewOptions {
  url: string;
  javascript?: boolean;
  showNavigationBar?: boolean;
  userAgent?: string;
  // 指定WebView在页面中的位置（相对于content区域）
  position?: {
    top?: number;
    left?: number;
    width?: string;
    height?: string;
  };
}

export interface WebViewResult {
  success: boolean;
  message?: string;
}

export interface WebViewEvent {
  type: string;
  url?: string;
  error?: string;
  title?: string;
}