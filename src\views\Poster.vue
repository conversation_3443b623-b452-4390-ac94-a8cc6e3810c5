<template>
  <div class="poster-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Poster主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--poster">
      <h1 class="app-title">{{ $t('pageTitle.poster') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 海报轮播器 -->
        <div class="poster-swiper-container">
          <swiper 
            ref="posterSwiper"
            :options="swiperOptions"
            class="poster-swiper"
          >
            <swiper-slide 
              v-for="(poster, index) in displayPosters" 
              :key="poster.id || index"
              class="poster-slide"
            >
              <div class="poster-item">
                <img 
                  :src="getPosterImageUrl(poster)" 
                  :alt="poster.title"
                  class="poster-image"
                  @error="onImageError"
                />
                
                <!-- 海报信息覆盖层 -->
                <div class="poster-overlay">
                  <div class="poster-info">
                    <h3 class="poster-name">{{ poster.title }}</h3>
                    <p class="poster-description">{{ poster.description }}</p>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          
          <!-- 分页指示器 -->
          <div class="swiper-pagination"></div>
        </div>

        <!-- 海报控制区域 -->
        <div class="poster-controls">
          <div class="control-buttons">
            <button @click="previousPoster" class="control-btn" :disabled="!canGoPrevious">
              <span class="control-icon">◀</span>
              <span class="control-text">{{ $t('poster.previous') }}</span>
            </button>
            
            <button @click="pauseAutoplay" class="control-btn" :class="{ active: isPaused }">
              <span class="control-icon">{{ isPaused ? '▶' : '⏸' }}</span>
              <span class="control-text">{{ isPaused ? $t('poster.play') : $t('poster.pause') }}</span>
            </button>
            
            <button @click="nextPoster" class="control-btn" :disabled="!canGoNext">
              <span class="control-icon">▶</span>
              <span class="control-text">{{ $t('poster.next') }}</span>
            </button>
          </div>
          
          <div class="poster-counter">
            <span class="counter-text">{{ currentPosterIndex + 1 }} / {{ displayPosters.length }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import { mapGetters } from 'vuex'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import { ImageConfig } from '@/services/api'
import apiService from '@/services/api'

interface PosterItem {
  id: string
  title: string
  description: string
  url: string
  type: 'image' | 'video'
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage,
    Swiper,
    SwiperSlide
  },
  computed: {
    ...mapGetters('config', ['posters', 'hasConfig'])
  },
  watch: {
    displayPosters: {
      handler(newVal: any) {
        if (newVal && newVal.length > 0) {
          console.log('海报数据更新，重新初始化 Swiper');
          (this as any).$nextTick(() => {
            setTimeout(() => {
              (this as any).initializeSwiper()
            }, 200)
          })
        }
      },
      immediate: false
    }
  }
})
export default class Poster extends Mixins(ResponsiveMixin, I18nMixin) {
  // 从store获取的计算属性
  posters!: ImageConfig[]
  hasConfig!: boolean

  // 组件状态
  currentPosterIndex: number = 0
  isPaused: boolean = false

  getBackgroundColor(): string {
    return '#FF6B6B' // Poster页面的粉红色背景
  }

  // 本地默认海报数据（作为回退）
  get defaultPosters(): PosterItem[] {
    return [
      {
        id: '1',
        title: this.$t('posterContent.splus.title'),
        description: this.$t('posterContent.splus.description'),
        url: 'img/posters/poster02.jpg',
        type: 'image'
      },
      {
        id: '2', 
        title: this.$t('posterContent.ikea.title'),
        description: this.$t('posterContent.ikea.description'),
        url: 'img/posters/poster01.jpg',
        type: 'image'
      },
      {
        id: '3',
        title: this.$t('posterContent.more.title'),
        description: this.$t('posterContent.more.description'),
        url: 'img/posters/poster02.jpg',
        type: 'image'
      }
    ]
  }

  /**
   * 获取要显示的海报数据
   * 优先级：远程配置 > 本地默认
   */
  get displayPosters(): PosterItem[] {
    if (this.hasConfig && this.posters && this.posters.length > 0) {
      console.log('使用远程海报配置')
      return this.posters.map((poster, index) => ({
        id: String(poster.id),
        title: poster.alternativeText || `${this.$t('poster.defaultTitle')} ${index + 1}`,
        description: poster.caption || `${this.$t('poster.defaultDescription')} ${index + 1}`,
        url: apiService.buildImageUrl(poster.url),
        type: 'image' as const
      }))
    }
    console.log('使用本地默认海报')
    return this.defaultPosters
  }

  /**
   * 获取海报图片URL
   */
  getPosterImageUrl(poster: PosterItem): string {
    // 如果是远程配置的海报，URL已经在displayPosters中处理
    // 如果是本地默认海报，需要添加路径前缀
    if (poster.url.startsWith('http://') || poster.url.startsWith('https://')) {
      return poster.url
    }
    // 确保路径正确
    return poster.url.startsWith('/') ? poster.url : `/${poster.url}`
  }
  
  // 监听数据变化，重新初始化 swiper
  get displayPostersWatch() {
    return this.displayPosters.length
  }

  swiperOptions = {
    // 滑动设置
    direction: 'horizontal',
    slidesPerView: 1,
    spaceBetween: 0,
    centeredSlides: true,
    loop: true,
    initialSlide: 0,
    
    // 自动播放设置 - 3秒切换
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    },
    
    // 切换效果
    effect: 'slide',
    speed: 800,
    
    // 分页指示器
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
      dynamicBullets: true
    },
    
    // 触摸滑动
    touchRatio: 1,
    touchAngle: 45,
    simulateTouch: true,
    allowTouchMove: true,
    touchStartPreventDefault: false,
    touchMoveStopPropagation: false,
    touchReleaseOnEdges: false,
    
    // 鼠标滑动
    mousewheel: {
      invert: false,
      sensitivity: 1,
      thresholdDelta: 50
    },
    
    // 键盘控制
    keyboard: {
      enabled: true,
      onlyInViewport: true
    },
    
    // 回调函数
    on: {
      init: () => {
        this.$nextTick(() => {
          this.updateCurrentIndex()
        })
      },
      slideChange: () => {
        this.updateCurrentIndex()
      }
    }
  }

  // 计算属性：是否可以向前
  get canGoPrevious(): boolean {
    return this.currentPosterIndex > 0 || this.displayPosters.length > 1
  }

  // 计算属性：是否可以向后
  get canGoNext(): boolean {
    return this.currentPosterIndex < this.displayPosters.length - 1 || this.displayPosters.length > 1
  }

  mounted() {
    console.log('Poster组件已挂载，海报数量:', this.displayPosters.length)
    
    // 确保 swiper 正确初始化
    this.$nextTick(() => {
      setTimeout(() => {
        this.initializeSwiper()
      }, 100)
    })
  }
  
  initializeSwiper() {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper) {
      console.log('Swiper 初始化成功')
      // 确保从第一张开始显示
      swiper.slideTo(0, 0)
      this.updateCurrentIndex()
      
      // 开始自动播放
      if (swiper.autoplay && !this.isPaused) {
        swiper.autoplay.start()
      }
    } else {
      console.error('Swiper 初始化失败')
    }
  }

  // 更新当前索引
  updateCurrentIndex() {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper) {
      this.currentPosterIndex = swiper.realIndex
    }
  }

  // 上一张海报
  previousPoster() {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper) {
      swiper.slidePrev()
    }
  }

  // 下一张海报
  nextPoster() {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper) {
      swiper.slideNext()
    }
  }

  // 暂停/恢复自动播放
  pauseAutoplay() {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper) {
      if (this.isPaused) {
        swiper.autoplay.start()
        this.isPaused = false
      } else {
        swiper.autoplay.stop()
        this.isPaused = true
      }
    }
  }

  // 跳转到指定海报
  goToPoster(index: number) {
    const swiper = (this.$refs.posterSwiper as any)?.$swiper
    if (swiper && index >= 0 && index < this.displayPosters.length) {
      swiper.slideTo(index)
    }
  }

  // 图片加载错误处理
  onImageError(event: Event) {
    const img = event.target as HTMLImageElement
    if (img) {
      // 使用占位图片
      img.src = '/img/placeholder-poster.png'
      console.warn('海报图片加载失败，使用占位图片')
    }
  }
}
</script>

<style scoped>
/* 导入Swiper样式 */
@import 'swiper/css/swiper.css';
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
.poster-swiper-container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  /* height: 250vw; */
  position: relative;
}

.poster-swiper {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

.poster-slide {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  position: relative !important;
}

.poster-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  
  /* 毛玻璃阴影效果 */
  filter: drop-shadow(0px 12px 12px rgba(0, 0, 0, 0.25));
}

.poster-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 20px;
}

.poster-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    transparent 0%, 
    rgba(0, 0, 0, 0.3) 50%, 
    rgba(0, 0, 0, 0.7) 100%
  );
  padding: 60px 40px 40px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.poster-slide:hover .poster-overlay {
  transform: translateY(0);
}

.poster-info {
  text-align: center;
  color: white;
}

.poster-name {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.poster-description {
  font-size: 32px;
  font-weight: 300;
  margin: 0;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 分页指示器样式 */
.poster-container :deep(.swiper-pagination) {
  bottom: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  display: flex !important;
  justify-content: center !important;
  gap: 12px !important;
}

.poster-container :deep(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.4);
  width: 16px;
  height: 16px;
  opacity: 1;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.poster-container :deep(.swiper-pagination-bullet-active) {
  background: #00EEFF;
  border-color: #00EEFF;
  transform: scale(1.2);
  box-shadow: 0px 0px 10px rgba(0, 238, 255, 0.5);
}

.poster-container :deep(.swiper-pagination-bullet:hover) {
  background: rgba(255, 255, 255, 0.7);
  transform: scale(1.1);
}

/* Swiper 容器和包装器 */
.poster-container :deep(.swiper-wrapper) {
  display: flex !important;
  flex-direction: row !important;
  align-items: stretch !important;
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 滑动动画效果 */
.poster-container :deep(.swiper-slide) {
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  flex-shrink: 0 !important;
}

.poster-container :deep(.swiper-slide-active) {
  transform: scale(1);
}

.poster-container :deep(.swiper-slide-prev),
.poster-container :deep(.swiper-slide-next) {
  transform: scale(0.9);
  opacity: 0.7;
}

/* 触摸反馈和事件恢复 */
.poster-swiper {
  cursor: grab;
  pointer-events: auto;
  touch-action: pan-y pinch-zoom;
}

.poster-swiper:active {
  cursor: grabbing;
}

/* 确保 Swiper 内部元素可以响应触摸事件 */
.poster-swiper .swiper-wrapper,
.poster-swiper .swiper-slide,
.poster-swiper .poster-item,
.poster-swiper .poster-image {
  pointer-events: auto;
  user-select: none;
}

/* 分页指示器必须可点击 */
.poster-container :deep(.swiper-pagination),
.poster-container :deep(.swiper-pagination-bullet) {
  pointer-events: auto;
  user-select: none;
}

/* 海报控制区域样式 */
.poster-controls {
  margin-top: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.control-buttons {
  display: flex;
  gap: 30px;
  align-items: center;
  justify-content: center;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 20px 40px;
  background: rgba(255, 255, 255, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  backdrop-filter: blur(20px);
  cursor: pointer;
  transition: all 0.3s ease;
  
  /* 毛玻璃效果 */
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  
  box-shadow: 
    6px 6px 12px 0px rgba(0, 0, 0, 0.2),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.15);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.active {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
}

.control-btn.active:hover {
  background: rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 107, 107, 0.5);
}

.control-icon {
  font-size: 36px;
  line-height: 1;
}

.control-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 1.2;
}

.poster-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  backdrop-filter: blur(15px);
  
  /* 毛玻璃效果 */
  box-shadow: 
    2px 2px 6px 0px rgba(0, 0, 0, 0.1),
    inset 0px 1px 3px 0px rgba(255, 255, 255, 0.1);
}

.counter-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 32px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.2;
}
</style> 