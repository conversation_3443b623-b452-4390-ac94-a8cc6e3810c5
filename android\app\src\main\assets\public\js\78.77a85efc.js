"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[78],{78:(t,e,i)=>{i.r(e),i.d(e,{default:()=>y});var o=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"video-page responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--video"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.video")))])]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--standard"},[e("div",{staticClass:"dual-video-section"},[e("div",{staticClass:"video-display-box"},[e("div",{staticClass:"video-container"},[e("iframe",{key:`video1-${t.video1Muted}`,staticClass:"youtube-player",attrs:{src:t.getVideoEmbedUrl(t.displayVideo1.url,t.video1Muted),frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""}})]),e("div",{staticClass:"video-info"},[e("h3",{staticClass:"video-title-text"},[t._v(t._s(t.displayVideo1.title))]),e("p",{staticClass:"video-description-text"},[t._v(t._s(t.displayVideo1.description))])]),e("div",{staticClass:"video-controls"},[e("button",{staticClass:"mute-control-button",class:{unmuted:!t.video1Muted},on:{click:t.toggleVideo1Mute}},[e("span",{staticClass:"button-icon"},[t._v(t._s(t.video1Muted?"🔇":"🔊"))]),e("span",{staticClass:"button-text"},[t._v(t._s(t.video1Muted?t.$t("videoContent.sound.on"):t.$t("videoContent.sound.off")))])])])]),e("div",{staticClass:"video-display-box"},[e("div",{staticClass:"video-container"},[e("iframe",{key:`video2-${t.video2Muted}`,staticClass:"youtube-player",attrs:{src:t.getVideoEmbedUrl(t.displayVideo2.url,t.video2Muted),frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""}})]),e("div",{staticClass:"video-info"},[e("h3",{staticClass:"video-title-text"},[t._v(t._s(t.displayVideo2.title))]),e("p",{staticClass:"video-description-text"},[t._v(t._s(t.displayVideo2.description))])]),e("div",{staticClass:"video-controls"},[e("button",{staticClass:"mute-control-button",class:{unmuted:!t.video2Muted},on:{click:t.toggleVideo2Mute}},[e("span",{staticClass:"button-icon"},[t._v(t._s(t.video2Muted?"🔇":"🔊"))]),e("span",{staticClass:"button-text"},[t._v(t._s(t.video2Muted?t.$t("videoContent.sound.on"):t.$t("videoContent.sound.off")))])])])])]),e("div",{staticClass:"volume-notice"},[e("div",{staticClass:"notice-content"},[e("div",{staticClass:"notice-icon"},[t._v("🔇")]),e("div",{staticClass:"notice-text"},[e("h3",{staticClass:"notice-title"},[t._v(t._s(t.$t("video.mutedNotice")))]),e("p",{staticClass:"notice-description"},[t._v(t._s(t.$t("videoContent.sound.notice")))])])])])])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},s=[],d=i(635),n=i(233),a=i(353),c=i(14),l=i(958),r=i(256),u=i(185),v=i(959);let h=class extends((0,n.Xe)(u.A,v.A)){video1Muted=!0;video2Muted=!0;firstVideo;secondVideo;hasConfig;getBackgroundColor(){return"#9B59B6"}get defaultVideoDisplay1(){return{id:1,title:this.$t("videoContent.videos.basketball.title"),description:this.$t("videoContent.videos.basketball.description"),url:"https://www.youtube.com/watch?v=Ufhfsx0PfTY",duration:"5:32",category:this.$t("videoContent.videos.basketball.category")}}get defaultVideoDisplay2(){return{id:2,title:this.$t("videoContent.videos.swimming.title"),description:this.$t("videoContent.videos.swimming.description"),url:"https://www.youtube.com/watch?v=dQw4w9WgXcQ",duration:"8:15",category:this.$t("videoContent.videos.swimming.category")}}get displayVideo1(){return this.hasConfig&&this.firstVideo?(console.log("使用远程第一个视频配置"),this.firstVideo):(console.log("使用本地默认第一个视频"),this.defaultVideoDisplay1)}get displayVideo2(){return this.hasConfig&&this.secondVideo?(console.log("使用远程第二个视频配置"),this.secondVideo):(console.log("使用本地默认第二个视频"),this.defaultVideoDisplay2)}toggleVideo1Mute(){this.video1Muted=!this.video1Muted}toggleVideo2Mute(){this.video2Muted=!this.video2Muted}getVideoEmbedUrl(t,e){const i=this.getVideoId(t);if(!i)return"";const o=e?1:0;return`https://www.youtube.com/embed/${i}?autoplay=1&mute=${o}&loop=1&playlist=${i}&rel=0&modestbranding=1&controls=1`}get videoList(){return[{id:1,title:this.$t("videoContent.videos.basketball.title"),description:this.$t("videoContent.videos.basketball.description"),url:"https://www.youtube.com/watch?v=Ufhfsx0PfTY",duration:"5:32",category:this.$t("videoContent.videos.basketball.category")},{id:2,title:this.$t("videoContent.videos.swimming.title"),description:this.$t("videoContent.videos.swimming.description"),url:"https://www.youtube.com/watch?v=dQw4w9WgXcQ",duration:"8:15",category:this.$t("videoContent.videos.swimming.category")},{id:3,title:this.$t("videoContent.videos.tennis.title"),description:this.$t("videoContent.videos.tennis.description"),url:"https://www.youtube.com/watch?v=oHg5SJYRHA0",duration:"12:45",category:this.$t("videoContent.videos.tennis.category")}]}currentVideo=null;mounted(){this.videoList.length>0&&(this.currentVideo=this.videoList[0])}selectVideo(t){this.currentVideo=t}previousVideo(){if(!this.currentVideo)return;const t=this.videoList.findIndex(t=>t.id===this.currentVideo.id);t>0&&(this.currentVideo=this.videoList[t-1])}nextVideo(){if(!this.currentVideo)return;const t=this.videoList.findIndex(t=>t.id===this.currentVideo.id);t<this.videoList.length-1&&(this.currentVideo=this.videoList[t+1])}get hasPrevious(){if(!this.currentVideo)return!1;const t=this.videoList.findIndex(t=>t.id===this.currentVideo.id);return t>0}get hasNext(){if(!this.currentVideo)return!1;const t=this.videoList.findIndex(t=>t.id===this.currentVideo.id);return t<this.videoList.length-1}getVideoId(t){const e=/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/,i=t.match(e);return i&&11===i[2].length?i[2]:""}getVideoThumbnail(t){const e=this.getVideoId(t);return`https://img.youtube.com/vi/${e}/mqdefault.jpg`}toggleFullscreen(){const t=document.querySelector(".youtube-player");t&&t.requestFullscreen&&t.requestFullscreen()}};h=(0,d.Cg)([(0,n.uA)({components:{TopBar:c.A,BottomBar:l.A,BackgroundImage:r.A},computed:{...(0,a.L8)("config",["firstVideo","secondVideo","hasConfig"])}})],h);const p=h,g=p;var C=i(656),m=(0,C.A)(g,o,s,!1,null,"6761f50c",null);const y=m.exports}}]);