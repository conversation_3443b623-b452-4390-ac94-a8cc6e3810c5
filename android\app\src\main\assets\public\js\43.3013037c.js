"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[43],{5059:(s,a,t)=>{t.r(a),t.d(a,{default:()=>k});var e=function(){var s=this,a=s._self._c;s._self._setupProxy;return a("div",{staticClass:"shop-container responsive-page-container",style:s.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed"},[a("h1",{staticClass:"app-title"},[s._v(s._s(s.$t("pageTitle.shop")))])]),a("div",{staticClass:"content-area",style:s.contentAreaStyle},[a("div",{staticClass:"main-content"},[s.loading?a("div",{staticClass:"loading-container"},[a("div",{staticClass:"loading-spinner"}),a("div",{staticClass:"loading-text"},[s._v(s._s(s.$t("common.loading")||"加载中..."))])]):s.error?a("div",{staticClass:"error-container"},[a("div",{staticClass:"error-icon"},[s._v("⚠️")]),a("div",{staticClass:"error-text"},[s._v(s._s(s.errorMessage))]),a("button",{staticClass:"retry-button",on:{click:s.retryLoadShops}},[s._v(" "+s._s(s.$t("common.retry")||"重试")+" ")])]):a("div",{staticClass:"shop-grid"},s._l(s.shops,function(t){return a("ShopCard",{key:t.id,attrs:{"shop-name":s.getShopDisplayName(t),"logo-src":t.logo},on:{click:function(a){return s.navigateToShop(t.id)}}})}),1)])]),a("BottomBar",{on:{"home-clicked":s.onHomeClick,"language-changed":s.onLanguageChange,"ai-clicked":s.onAIClick}}),a("BottomMarquee")],1)},o=[],r=t(1635),n=t(9603),i=t(3961),l=t(3452),c=t(3205),h=t(4184),d=t(256),g=t(5185),p=t(7959),u=t(8091);let m=class extends((0,n.Xe)(g.A,p.A)){getBackgroundColor(){return"#0F04A9"}shops=[];loading=!0;error=!1;errorMessage="";getShopDisplayName(s){const a=this.$i18n?.locale||"en";switch(a){case"zh-TW":case"zh-CN":return s.name_tc||s.name_zh||s.name;case"en":default:return s.name}}async mounted(){await this.loadShops()}async loadShops(){this.loading=!0,this.error=!1,this.errorMessage="";try{console.log("开始加载商店数据..."),this.shops=await(0,u.aF)(),console.log(`成功加载${this.shops.length}个商店`)}catch(s){console.error("加载商店数据失败:",s),this.error=!0,this.errorMessage=s instanceof Error?`加载失败: ${s.message}`:"网络连接错误，请检查网络后重试"}finally{this.loading=!1}}async retryLoadShops(){await this.loadShops()}navigateToShop(s){console.log("导航到商店:",s),this.$router.push(`/shop/${s}`)}};m=(0,r.Cg)([(0,n.uA)({components:{ShopCard:i.A,TopBar:l.A,BottomBar:c.A,BottomMarquee:h.A,BackgroundImage:d.A}})],m);const C=m,v=C;var _=t(1656),y=(0,_.A)(v,e,o,!1,null,"d62091ea",null);const k=y.exports}}]);