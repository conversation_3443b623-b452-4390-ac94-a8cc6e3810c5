// 音效Mixin - 为所有组件提供音效功能（优化版）
import { Vue, Component } from 'vue-property-decorator'
import { soundService } from '@/services/audioManager'

@Component
export default class SoundMixin extends Vue {
  /**
   * 播放按钮点击音效（优化版）
   * @param volume 音量 (0-1) - 现在由audioManager统一管理
   * @param soundName 音效名称 - 简化为统一的点击音效
   */
  playClickSound(volume: number = 0.4, soundName: string = 'buttonClick'): void {
    try {
      // 使用优化的音频管理器
      if (volume !== 0.4) {
        soundService.setVolume(volume)
      }
      soundService.playButtonClick()
    } catch (error) {
      // 静默处理错误，避免控制台污染
    }
  }

  /**
   * 创建带音效的点击处理器
   * @param originalHandler 原始点击处理器
   * @param soundEnabled 是否启用音效
   * @param volume 音量
   * @returns 带音效的点击处理器
   */
  withClickSound(
    originalHandler?: Function,
    soundEnabled: boolean = true,
    volume: number = 0.4
  ): (event: Event) => void {
    return (event: Event) => {
      // 播放音效
      if (soundEnabled) {
        this.playClickSound(volume)
      }
      
      // 执行原始处理器
      if (originalHandler) {
        originalHandler.call(this, event)
      }
    }
  }

  /**
   * 启用/禁用全局音效
   */
  setSoundEnabled(enabled: boolean): void {
    soundService.setEnabled(enabled)
  }

  /**
   * 检查音效是否启用
   */
  get isSoundEnabled(): boolean {
    return soundService.isEnabled()
  }

  /**
   * 获取音频上下文状态
   */
  get audioState(): string {
    return soundService.getAudioState()
  }
}