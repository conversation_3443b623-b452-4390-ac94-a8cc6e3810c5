// 音效Mixin - 为所有组件提供音效功能
import { Vue, Component } from 'vue-property-decorator'
import { soundService } from '@/services/soundService'

@Component
export default class SoundMixin extends Vue {
  /**
   * 播放按钮点击音效
   * @param volume 音量 (0-1)
   * @param soundName 音效名称，默认使用buttonClick
   */
  playClickSound(volume: number = 0.4, soundName: string = 'buttonClick'): void {
    try {
      // 优先使用预加载的音频文件，如果没有则使用合成音效
      const played = soundService.playSound(soundName, volume)
      if (!played) {
        soundService.playButtonClick(volume * 0.5) // 合成音效音量稍小
      }
    } catch (error) {
      console.warn('播放音效失败:', error)
    }
  }

  /**
   * 创建带音效的点击处理器
   * @param originalHandler 原始点击处理器
   * @param soundEnabled 是否启用音效
   * @param volume 音量
   * @returns 带音效的点击处理器
   */
  withClickSound(
    originalHandler?: Function,
    soundEnabled: boolean = true,
    volume: number = 0.4
  ): (event: Event) => void {
    return (event: Event) => {
      // 播放音效
      if (soundEnabled) {
        this.playClickSound(volume)
      }
      
      // 执行原始处理器
      if (originalHandler) {
        originalHandler.call(this, event)
      }
    }
  }

  /**
   * 启用/禁用全局音效
   */
  setSoundEnabled(enabled: boolean): void {
    soundService.setEnabled(enabled)
  }

  /**
   * 检查音效是否启用
   */
  get isSoundEnabled(): boolean {
    return soundService.isEnabled()
  }

  /**
   * 获取音频上下文状态
   */
  get audioState(): string {
    return soundService.getAudioState()
  }
}