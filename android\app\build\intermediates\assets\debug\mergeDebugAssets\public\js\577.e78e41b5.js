"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[577],{7893:(t,e,s)=>{s.r(e),s.d(e,{default:()=>f});var a=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"ai-search-page responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--ai"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.aiSearch")))])]),e("div",{staticClass:"search-input-container"},[e("div",{staticClass:"search-icon"},[e("svg",{attrs:{width:"116",height:"116",viewBox:"0 0 116 116",fill:"none"}},[e("path",{attrs:{d:"M51.6 90.2c21.3 0 38.6-17.3 38.6-38.6S72.9 13 51.6 13 13 30.3 13 51.6 30.3 90.2 51.6 90.2zM96.8 103L71.4 77.6",stroke:"white","stroke-width":"6","stroke-linecap":"round","stroke-linejoin":"round"}})])]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQuery,expression:"searchQuery"}],staticClass:"search-input",attrs:{type:"text",placeholder:t.$t("aiSearch.placeholder")},domProps:{value:t.searchQuery},on:{input:[function(e){e.target.composing||(t.searchQuery=e.target.value)},t.onSearchInput]}})]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--shop"},[t.searchQuery.trim()?0===t.filteredShops.length?e("div",{staticClass:"no-results"},[e("div",{staticClass:"no-results-icon"},[t._v("😔")]),e("h2",{staticClass:"no-results-title"},[t._v(t._s(t.$t("shop.noResults")))]),e("p",{staticClass:"no-results-description"},[t._v(t._s(t.$t("shop.tryOtherKeywords")))])]):e("div",{staticClass:"shop-grid"},t._l(t.filteredShops,function(s){return e("ShopCard",{key:s.id,attrs:{"shop-name":s.name,"logo-src":s.logo},on:{click:function(e){return t.navigateToShop(s.id)}}})}),1):e("div",{staticClass:"search-prompt"},[e("div",{staticClass:"prompt-icon"},[t._v("🔍")]),e("h2",{staticClass:"prompt-title"},[t._v(t._s(t.$t("shop.startSearch")))]),e("p",{staticClass:"prompt-description"},[t._v(t._s(t.$t("shop.searchPrompt")))])])])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),e("BottomMarquee")],1)},o=[],r=s(1635),i=s(9603),n=s(3452),c=s(3205),l=s(4184),h=s(256),p=s(3961),u=s(5185),d=s(7959),g=s(8091);let v=class extends((0,i.Xe)(u.A,d.A)){getBackgroundColor(){return"#FF69B4"}searchQuery="";shops=[];shopsLoaded=!1;async mounted(){try{this.shops=await(0,g.aF)(),this.shopsLoaded=!0}catch(t){console.error("加载商店数据失败:",t)}}get filteredShops(){if(!this.searchQuery.trim())return[];const t=this.searchQuery.toLowerCase().trim();return this.shops.filter(e=>e.name.toLowerCase().includes(t))}onSearchInput(t){const e=t.target;this.searchQuery=e.value}navigateToShop(t){console.log("导航到商店:",t),this.$router.push(`/shop/${t}`)}};v=(0,r.Cg)([(0,i.uA)({components:{TopBar:n.A,BottomBar:c.A,BottomMarquee:l.A,BackgroundImage:h.A,ShopCard:p.A}})],v);const m=v,C=m;var y=s(1656),_=(0,y.A)(C,a,o,!1,null,"7c98766a",null);const f=_.exports}}]);