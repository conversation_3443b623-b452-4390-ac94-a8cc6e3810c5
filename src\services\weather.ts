// 天气服务
export interface WeatherData {
  temperature: number
  weatherMain: string
  weatherDescription: string
  icon: string
  humidity: number
  feels_like: number
  city: string
}

export interface WeatherResponse {
  main: {
    temp: number
    feels_like: number
    humidity: number
  }
  weather: Array<{
    main: string
    description: string
    icon: string
  }>
  name: string
}

class WeatherService {
  // 使用免费的wttr.in天气API（无需API key）
  private readonly baseUrl = 'https://wttr.in'
  
  private readonly cacheKey = 'hk_weather_cache'
  private readonly cacheTimestampKey = 'hk_weather_timestamp'
  private readonly cacheValidTime = 10 * 60 * 1000 // 10分钟缓存

  /**
   * 获取香港天气数据
   */
  async getHongKongWeather(): Promise<WeatherData | null> {
    try {
      // 先检查缓存
      const cachedWeather = this.getCachedWeather()
      if (cachedWeather) {
        return cachedWeather
      }

      // 获取远程数据
      const weatherData = await this.fetchWeatherData()
      if (weatherData) {
        this.saveToCache(weatherData)
        return weatherData
      }

      // 如果都失败，返回默认数据
      return this.getDefaultWeather()
    } catch (error) {
      // 静默处理错误，使用默认数据
      return this.getDefaultWeather()
    }
  }

  /**
   * 从API获取天气数据
   */
  private async fetchWeatherData(): Promise<WeatherData | null> {
    try {
      // 使用wttr.in API获取香港天气，JSON格式
      const url = `${this.baseUrl}/Hong+Kong?format=j1`
      
      // 创建超时控制器
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, 15000) // 增加到15秒
      
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; eMap App)'
        }
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      // 解析wttr.in的响应格式
      const current = data.current_condition[0]
      const weatherDesc = current.weatherDesc[0].value
      const temp = parseInt(current.temp_C)
      const humidity = parseInt(current.humidity)
      const feelsLike = parseInt(current.FeelsLikeC)
      
      // 映射天气代码到图标
      const weatherCode = current.weatherCode
      const iconCode = this.mapWeatherCodeToIcon(weatherCode)
      
      return {
        temperature: temp,
        weatherMain: this.getWeatherMain(weatherCode),
        weatherDescription: weatherDesc,
        icon: iconCode,
        humidity: humidity,
        feels_like: feelsLike,
        city: '香港'
      }
    } catch (error) {
      // 静默处理错误，不显示任何提醒
      return null
    }
  }

  /**
   * 获取缓存的天气数据
   */
  private getCachedWeather(): WeatherData | null {
    try {
      const cachedData = localStorage.getItem(this.cacheKey)
      const timestamp = localStorage.getItem(this.cacheTimestampKey)
      
      if (!cachedData || !timestamp) {
        return null
      }

      const cacheAge = Date.now() - parseInt(timestamp)
      if (cacheAge > this.cacheValidTime) {
        this.clearCache()
        return null
      }

      return JSON.parse(cachedData)
    } catch (error) {
      // 静默处理缓存读取错误
      this.clearCache()
      return null
    }
  }

  /**
   * 保存天气数据到缓存
   */
  private saveToCache(weatherData: WeatherData): void {
    try {
      localStorage.setItem(this.cacheKey, JSON.stringify(weatherData))
      localStorage.setItem(this.cacheTimestampKey, Date.now().toString())
    } catch (error) {
      // 静默处理缓存保存错误
    }
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    try {
      localStorage.removeItem(this.cacheKey)
      localStorage.removeItem(this.cacheTimestampKey)
    } catch (error) {
      // 静默处理缓存清除错误
    }
  }

  /**
   * 获取默认天气数据（当API失败时使用）
   */
  private getDefaultWeather(): WeatherData {
    return {
      temperature: 25,
      weatherMain: 'Clouds',
      weatherDescription: '多雲',
      icon: '02d',
      humidity: 70,
      feels_like: 28,
      city: '香港'
    }
  }

  /**
   * 将wttr.in天气代码映射到图标代码
   */
  private mapWeatherCodeToIcon(weatherCode: string): string {
    const codeMap: { [key: string]: string } = {
      '113': '01d', // 晴天
      '116': '02d', // 少云
      '119': '03d', // 多云
      '122': '04d', // 阴天
      '143': '50d', // 雾
      '176': '10d', // 小雨
      '179': '13d', // 小雪
      '182': '13d', // 雨夹雪
      '185': '13d', // 雨夹雪
      '200': '11d', // 雷雨
      '227': '13d', // 暴雪
      '230': '13d', // 暴雪
      '248': '50d', // 雾
      '260': '50d', // 雾
      '263': '09d', // 小雨
      '266': '09d', // 小雨
      '281': '13d', // 雨夹雪
      '284': '13d', // 重雨夹雪
      '293': '10d', // 小雨
      '296': '10d', // 小雨
      '299': '09d', // 中雨
      '302': '09d', // 中雨
      '305': '09d', // 大雨
      '308': '09d', // 暴雨
      '311': '13d', // 冻雨
      '314': '13d', // 重冻雨
      '317': '13d', // 雨夹雪
      '320': '13d', // 雨夹雪
      '323': '13d', // 小雪
      '326': '13d', // 小雪
      '329': '13d', // 中雪
      '332': '13d', // 中雪
      '335': '13d', // 大雪
      '338': '13d', // 大雪
      '350': '13d', // 冰雹
      '353': '10d', // 小阵雨
      '356': '09d', // 中阵雨
      '359': '09d', // 大阵雨
      '362': '13d', // 雨夹雪
      '365': '13d', // 雨夹雪
      '368': '13d', // 小雪
      '371': '13d', // 中雪
      '374': '13d', // 冰雹
      '377': '13d', // 冰雹
      '386': '11d', // 雷雨
      '389': '11d', // 雷雨
      '392': '11d', // 雷雪
      '395': '11d'  // 雷雪
    }
    return codeMap[weatherCode] || '02d'
  }

  /**
   * 将天气代码映射到主要天气类型
   */
  private getWeatherMain(weatherCode: string): string {
    const code = parseInt(weatherCode)
    if (code === 113) return 'Clear'
    if (code >= 116 && code <= 119) return 'Clouds'
    if (code >= 122 && code <= 122) return 'Clouds'
    if (code >= 143 && code <= 260) return 'Mist'
    if (code >= 263 && code <= 320) return 'Rain'
    if (code >= 323 && code <= 395) return 'Snow'
    if (code >= 386 && code <= 395) return 'Thunderstorm'
    return 'Clouds'
  }

  /**
   * 根据天气图标代码获取对应的SVG图标
   */
  getWeatherIcon(iconCode: string): string {
    const iconMap: { [key: string]: string } = {
      // 晴天
      '01d': '☀️', // 白天晴天
      '01n': '🌙', // 夜晚晴天
      
      // 少云
      '02d': '⛅', // 白天少云
      '02n': '☁️', // 夜晚少云
      
      // 多云
      '03d': '☁️', // 白天多云
      '03n': '☁️', // 夜晚多云
      '04d': '☁️', // 白天阴天
      '04n': '☁️', // 夜晚阴天
      
      // 雨天
      '09d': '🌧️', // 阵雨
      '09n': '🌧️',
      '10d': '🌦️', // 小雨
      '10n': '🌦️',
      
      // 雷雨
      '11d': '⛈️', // 雷雨
      '11n': '⛈️',
      
      // 雪天
      '13d': '❄️', // 雪
      '13n': '❄️',
      
      // 雾
      '50d': '🌫️', // 雾
      '50n': '🌫️'
    }

    return iconMap[iconCode] || '🌤️'
  }

  /**
   * 强制刷新天气数据
   */
  async forceRefresh(): Promise<WeatherData | null> {
    this.clearCache()
    return await this.getHongKongWeather()
  }
}

// 导出单例
const weatherService = new WeatherService()
export default weatherService 