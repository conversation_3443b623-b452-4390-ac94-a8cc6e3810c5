import { Vue, Component } from 'vue-property-decorator'

@Component
export default class ScalingMixin extends Vue {
  // 4K竖屏分辨率基准 (移动端4K)
  baseWidth = 2160
  baseHeight = 3840
  scaleRatio = 1

  // 双击退出相关变量
  private backPressCount = 0
  private backPressTimer: number | null = null
  private readonly BACK_PRESS_INTERVAL = 2000 // 2秒内需要双击

  mounted() {
    console.log('[ScalingMixin] Mounted - 开始初始化缩放和返回按钮功能')
    this.calculateScale()
    window.addEventListener('resize', this.calculateScale)
    
    // 添加Android返回按钮支持
    this.setupAndroidBackButton()
  }

  beforeDestroy() {
    console.log('[ScalingMixin] BeforeDestroy - 清理事件监听器')
    window.removeEventListener('resize', this.calculateScale)
    
    // 清理定时器
    if (this.backPressTimer) {
      clearTimeout(this.backPressTimer)
      this.backPressTimer = null
    }
    
    // 清理返回按钮相关的事件监听器（注意：由于使用了箭头函数，这些可能无法完全清理）
    // 但为了避免内存泄漏，我们仍然尝试清理
    try {
      document.removeEventListener('backbutton', this.handleBackButton)
      window.removeEventListener('popstate', this.handleBackButton)
      document.removeEventListener('keydown', this.handleBackButton)
    } catch (error) {
      console.log('[ScalingMixin] 清理事件监听器时出错:', error)
    }
  }

  calculateScale() {
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    
    // 改为宽度占满，高度自适应的缩放策略
    const widthRatio = screenWidth / this.baseWidth
    
    // 使用宽度比例作为缩放比例，设置合理的最小和最大限制
    this.scaleRatio = Math.min(Math.max(widthRatio, 0.1), 3)
    
    console.log(`[ScalingMixin] 宽度优先缩放计算: 屏幕${screenWidth}x${screenHeight}, 基准${this.baseWidth}x${this.baseHeight}, 宽度比例${widthRatio}, 最终缩放比例${this.scaleRatio}`)
    
    // 计算缩放后的实际高度
    const scaledHeight = this.baseHeight * this.scaleRatio
    console.log(`[ScalingMixin] 缩放后高度: ${scaledHeight}, 屏幕高度: ${screenHeight}`)
    
    // 设置body的样式以适应缩放
    document.body.style.width = `${screenWidth}px`
    document.body.style.height = `${Math.max(screenHeight, scaledHeight)}px` // 确保足够的高度
    document.body.style.overflow = scaledHeight > screenHeight ? 'auto' : 'hidden' // 如果内容超出屏幕高度，允许滚动
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    
    // 强制刷新Vue组件的缩放属性
    this.$nextTick(() => {
      this.$forceUpdate()
    })
    
    // 宽度居中计算（通常不需要，因为宽度占满）
    const scaledWidth = this.baseWidth * this.scaleRatio
    const offsetX = (screenWidth - scaledWidth) / 2
    const offsetY = 0 // 高度不居中，从顶部开始显示
    
    console.log(`[ScalingMixin] 居中计算: 缩放后尺寸${scaledWidth}x${scaledHeight}, 水平偏移量${offsetX}`)
    
    // 应用偏移量（主要是水平居中）
    const containers = [
      '.home-container',
      '.shop-container', 
      '.about-page',
      '.video-page',
      '.food-page',
      '.transport-page',
      '.facility-container',
      '.poster-container',
      '.office-container',
      '.web-page',
      '.worldtime-page'
    ]
    
    for (const containerClass of containers) {
      const container = document.querySelector(containerClass) as HTMLElement
      if (container) {
        // 只应用水平偏移，垂直从顶部开始
        container.style.marginLeft = `${Math.max(0, offsetX)}px`
        container.style.marginTop = '0px'
        console.log(`[ScalingMixin] 找到容器 ${containerClass}，应用水平偏移量: ${offsetX}px`)
        
        // 强制触发重绘
        container.style.transform = `scale(${this.scaleRatio})`
        container.style.transformOrigin = 'top left'
        
        break
      }
    }
    
    // 额外的调试信息
    console.log(`[ScalingMixin] DOM元素检查 - body宽高: ${document.body.offsetWidth}x${document.body.offsetHeight}`)
    console.log(`[ScalingMixin] window尺寸: ${window.innerWidth}x${window.innerHeight}`)
    console.log(`[ScalingMixin] 当前scaleRatio值: ${this.scaleRatio}`)
    console.log(`[ScalingMixin] 是否需要滚动: ${scaledHeight > screenHeight ? '是' : '否'}`)
  }

  setupAndroidBackButton() {
    // 检查是否是Android环境
    const isAndroid = /Android/i.test(navigator.userAgent)
    const isCapacitor = !!(window as any).Capacitor
    
    console.log(`[ScalingMixin] 设备检测 - Android: ${isAndroid}, Capacitor: ${isCapacitor}`)
    console.log(`[ScalingMixin] User Agent: ${navigator.userAgent}`)
    
    if (isAndroid || isCapacitor) {
      console.log('[ScalingMixin] 设置Android返回按钮监听')
      
      // 方案1: 原生Capacitor返回按钮监听（新版本Capacitor）
      if ((window as any).Capacitor && (window as any).Capacitor.Plugins && (window as any).Capacitor.Plugins.App) {
        console.log('[ScalingMixin] 使用Capacitor App插件监听返回按钮')
        try {
          const App = (window as any).Capacitor.Plugins.App
          if (App && App.addListener) {
            App.addListener('backButton', (data: any) => {
              console.log('[ScalingMixin] Capacitor backButton事件触发:', data)
              this.handleBackButtonWithPrevent()
            })
          }
        } catch (error) {
          console.log('[ScalingMixin] Capacitor App插件设置失败:', error)
        }
      }
      
      // 方案2: 传统的backbutton事件监听
      document.addEventListener('backbutton', (event) => {
        console.log('[ScalingMixin] 传统backbutton事件触发')
        this.handleBackButtonWithPrevent(event)
      }, false)
      
      // 方案3: 浏览器popstate事件（备用方案）
      window.addEventListener('popstate', (event) => {
        console.log('[ScalingMixin] popstate事件触发')
        this.handleBackButtonWithPrevent(event)
      })
      
      // 方案4: 键盘事件监听（Android返回键是keycode 4）
      document.addEventListener('keydown', (event) => {
        if (event.keyCode === 4) { // Android返回键
          console.log('[ScalingMixin] 键盘返回键(keycode 4)事件触发')
          this.handleBackButtonWithPrevent(event)
        }
      })
      
      console.log('[ScalingMixin] 已设置多种返回按钮监听方案')
    } else {
      console.log('[ScalingMixin] 非Android环境，跳过返回按钮设置')
    }
  }

  handleBackButtonWithPrevent(event?: Event) {
    console.log('[ScalingMixin] handleBackButtonWithPrevent被调用 - 当前路径:', this.$route.path)
    
    // 阻止默认行为
    if (event) {
      event.preventDefault()
      event.stopPropagation()
      console.log('[ScalingMixin] 已阻止默认返回行为')
    }
    
    // 如果不在首页，则返回上一页或首页
    if (this.$route.path !== '/') {
      console.log('[ScalingMixin] 非首页，尝试返回上一页')
      
      // 重置退出计数器
      this.resetBackPressCount()
      
      // 先尝试router.back()
      if (window.history.length > 1) {
        console.log('[ScalingMixin] 执行 router.back()，历史长度:', window.history.length)
        this.$router.back()
      } else {
        console.log('[ScalingMixin] 历史记录为空，返回首页')
        this.$router.push('/')
      }
    } else {
      console.log('[ScalingMixin] 当前在首页，执行双击退出逻辑')
      this.handleDoubleBackPress()
    }
  }

  handleDoubleBackPress() {
    this.backPressCount++
    console.log(`[ScalingMixin] 返回键按下次数: ${this.backPressCount}`)
    
    if (this.backPressCount === 1) {
      // 第一次按下，显示提示并设置定时器
      console.log('[ScalingMixin] 第一次按下返回键，显示提示')
      this.showExitHint()
      
      // 设置定时器，2秒后重置计数器
      this.backPressTimer = window.setTimeout(() => {
        console.log('[ScalingMixin] 定时器到期，重置返回键计数器')
        this.resetBackPressCount()
      }, this.BACK_PRESS_INTERVAL)
      
    } else if (this.backPressCount >= 2) {
      // 第二次按下，退出应用
      console.log('[ScalingMixin] 第二次按下返回键，退出应用')
      this.resetBackPressCount()
      this.exitApp()
    }
  }

  resetBackPressCount() {
    this.backPressCount = 0
    if (this.backPressTimer) {
      clearTimeout(this.backPressTimer)
      this.backPressTimer = null
    }
    console.log('[ScalingMixin] 返回键计数器已重置')
  }

  showExitHint() {
    // 显示退出提示
    const message = '再按一次返回键退出应用'
    console.log(`[ScalingMixin] 显示提示: ${message}`)
    
    // 尝试多种提示方式
    try {
      // 方式1: 使用现代浏览器API（如果可用）
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(message, { 
          icon: '/img/logo.png',
          tag: 'exit-hint',
          requireInteraction: false
        })
      }
      
      // 方式2: 使用Toast（如果Capacitor可用）
      if ((window as any).Capacitor && (window as any).Capacitor.Plugins && (window as any).Capacitor.Plugins.Toast) {
        (window as any).Capacitor.Plugins.Toast.show({
          text: message,
          duration: 'short',
          position: 'bottom'
        })
      }
      
      // 方式3: 创建临时DOM提示元素
      this.showDOMHint(message)
      
    } catch (error) {
      console.log('[ScalingMixin] 显示退出提示失败:', error)
      // 降级方案：使用alert（通常在移动端体验不好，但确保能显示）
      // alert(message)
    }
  }

  showDOMHint(message: string) {
    // 创建提示元素
    const hintElement = document.createElement('div')
    hintElement.textContent = message
    hintElement.style.cssText = `
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 20px;
      font-size: 16px;
      z-index: 10000;
      font-family: 'Inter', sans-serif;
      pointer-events: none;
      opacity: 1;
      transition: opacity 0.3s ease;
    `
    
    // 添加到页面
    document.body.appendChild(hintElement)
    
    // 2秒后淡出并移除
    setTimeout(() => {
      hintElement.style.opacity = '0'
      setTimeout(() => {
        if (hintElement.parentNode) {
          document.body.removeChild(hintElement)
        }
      }, 300)
    }, 1500)
    
    console.log('[ScalingMixin] 已创建DOM提示元素')
  }

  exitApp() {
    // 尝试多种退出方式
    if ((window as any).Capacitor && (window as any).Capacitor.Plugins && (window as any).Capacitor.Plugins.App) {
      console.log('[ScalingMixin] 使用Capacitor退出应用')
      try {
        const App = (window as any).Capacitor.Plugins.App
        if (App && App.exitApp) {
          App.exitApp()
          return
        }
      } catch (error) {
        console.log('[ScalingMixin] Capacitor退出失败:', error)
      }
    }
    
    // 备用退出方案
    if ((window as any).navigator && (window as any).navigator.app && typeof (window as any).navigator.app.exitApp === 'function') {
      console.log('[ScalingMixin] 使用navigator.app退出')
      ;(window as any).navigator.app.exitApp()
    } else if ((window as any).device && typeof (window as any).device.exitApp === 'function') {
      console.log('[ScalingMixin] 使用device退出')
      ;(window as any).device.exitApp()
    } else {
      console.log('[ScalingMixin] 无法退出应用，使用window.close()')
      window.close()
    }
  }

  // 保留原来的方法作为备用
  handleBackButton() {
    console.log('[ScalingMixin] 旧版handleBackButton被调用，转发到新方法')
    this.handleBackButtonWithPrevent()
  }
} 