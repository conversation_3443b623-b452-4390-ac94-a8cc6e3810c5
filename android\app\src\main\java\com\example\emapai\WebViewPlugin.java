package com.example.emapai;

import android.content.Intent;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "WebView")
public class WebViewPlugin extends Plugin {
    private static final String TAG = "WebViewPlugin";

    @Override
    public void load() {
        Log.d(TAG, "WebViewPlugin loaded");
    }

    @PluginMethod()
    public void showEmbedded(PluginCall call) {
        Log.d(TAG, "showEmbedded method called");
        String url = call.getString("url");
        
        if (url == null) {
            Log.e(TAG, "URL is null");
            call.reject("URL is required");
            return;
        }

        Log.d(TAG, "URL: " + url);

        try {
            // 创建 Intent 启动 WebViewActivity
            Log.d(TAG, "Creating intent for WebViewActivity");
            Intent intent = new Intent(getActivity(), WebViewActivity.class);
            intent.putExtra("url", url);
            
            // 启动 Activity
            Log.d(TAG, "Starting WebViewActivity");
            getActivity().startActivity(intent);
            
            JSObject ret = new JSObject();
            ret.put("success", true);
            call.resolve(ret);
            Log.d(TAG, "WebViewActivity started successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error launching WebViewActivity", e);
            call.reject("Error showing WebView: " + e.getMessage());
        }
    }

    @PluginMethod
    public void hideEmbedded(PluginCall call) {
        // 这个方法在使用 Activity 方式时不需要实现
        // 用户可以通过返回按钮或关闭按钮关闭 WebViewActivity
        call.resolve();
    }
}
