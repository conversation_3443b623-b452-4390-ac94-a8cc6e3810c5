import { GoogleGenerativeAI } from '@google/generative-ai'

// Gemini API 密钥 - 从环境变量获取
const GEMINI_API_KEY = process.env.VUE_APP_GEMINI_API_KEY || 'AIzaSyBptBAXOzf42Q-jMg4ezWBam8ScrNlBBwQ'

// 初始化 Gemini AI
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY)

// 系统提示词 - 设置AI的角色和行为
const SYSTEM_PROMPT = `
你是Winnie，一个位于商场的智能AI客服助手。你的主要职责是：

1. 友好地欢迎顾客，提供热情周到的服务
2. 帮助顾客了解商场内的商店、设施、服务等信息
3. 回答关于商场导航、营业时间、促销活动等问题
4. 提供实用的购物建议和推荐
5. 用简洁明了的语言与顾客交流

请始终保持专业、友好和乐于助人的态度。如果遇到不确定的问题，请礼貌地表示会为顾客寻找相关信息。

对话语言：请根据用户的输入语言或语音的语种进行回复。
`

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: Date
  updatedAt: Date
}

/**
 * 调用Gemini API生成回复
 * @param message 用户消息（文本或音频）
 * @param chatHistory 聊天历史（可选）
 * @param audioData 音频数据（可选）
 * @returns AI生成的回复
 */
export async function callGemini(
  message: string | { audioData: ArrayBuffer, mimeType: string },
  chatHistory: ChatMessage[] = []
): Promise<string> {
  try {
    // 获取生成式模型
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' })

    // 构建消息内容
    let userContent: any[]
    if (typeof message === 'string') {
      // 文本输入
      userContent = [{ text: message }]
    } else {
      // 音频输入
      // 在浏览器环境中转换 ArrayBuffer 为 base64
      const uint8Array = new Uint8Array(message.audioData)
      let binary = ''
      for (let i = 0; i < uint8Array.byteLength; i++) {
        binary += String.fromCharCode(uint8Array[i])
      }
      const base64Data = btoa(binary)
      
      // 添加音频数据和文本指令，确保 Gemini 理解这是一个需要回复的对话
      userContent = [
        {
          text: "请听取以下音频内容。首先，请在你的回复开头用这样的格式标注语种：[LANGUAGE:语种代码]（例如：[LANGUAGE:zh-CN]表示中文，[LANGUAGE:en]表示英文，[LANGUAGE:es]表示西班牙语）。然后换行，给出对音频内容的恰当回复："
        },
        {
          inlineData: {
            mimeType: message.mimeType,
            data: base64Data
          }
        }
      ]
    }

    // 构建聊天历史
    const history: ChatMessage[] = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...chatHistory
    ]

    // 将消息历史转换为Gemini格式
    const geminiMessages = history
      .filter(msg => msg.role !== 'system') // Gemini不直接支持system角色
      .map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      }))

    // 添加当前用户输入
    geminiMessages.push({
      role: 'user',
      parts: userContent
    })

    // 将系统提示加入到第一条消息中
    if (geminiMessages.length > 0) {
      // 如果是音频输入，系统提示需要特殊处理
      if (typeof message !== 'string' && geminiMessages[0].parts.length > 1) {
        // 在音频消息前插入系统提示
        geminiMessages[0].parts.unshift({ text: SYSTEM_PROMPT + '\n\n' })
      } else if (geminiMessages[0].parts[0].text) {
        // 文本消息直接添加系统提示
        geminiMessages[0].parts[0].text = SYSTEM_PROMPT + '\n\n' + geminiMessages[0].parts[0].text
      }
    }

    // 生成回复
    const result = await model.generateContent({
      contents: geminiMessages,
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    })

    const response = result.response
    const text = response.text()
    
    if (!text) {
      throw new Error('没有收到AI回复')
    }

    return text.trim()
  } catch (error) {
    console.error('Gemini API 调用失败:', error)
    
    // 返回友好的错误消息
    return '抱歉，我现在无法回复您的消息。请稍后再试，或者联系商场工作人员寻求帮助。'
  }
}

/**
 * 生成对话会话标题
 * @param firstMessage 第一条消息
 * @returns 会话标题
 */
export function generateSessionTitle(firstMessage: string): string {
  // 简单的标题生成逻辑
  const title = firstMessage.length > 20 
    ? firstMessage.substring(0, 20) + '...'
    : firstMessage
  
  return title || '新对话'
}

/**
 * 创建新的聊天会话
 * @param firstMessage 第一条消息
 * @returns 新的会话对象
 */
export function createNewSession(firstMessage: string): ChatSession {
  return {
    id: Date.now().toString(),
    title: generateSessionTitle(firstMessage),
    messages: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
}
