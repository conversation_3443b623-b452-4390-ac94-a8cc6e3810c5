<template>
  <BaseCard
    :title="shopName"
    :icon-src="logoSrc"
    size="large"
    variant="shop"
    layout="vertical"
    :enable-marquee="true"
    :marquee-speed="4"
    :marquee-delay="1.5"
    marquee-type="bounce"
    @click="$emit('click')"
  />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import BaseCard from './BaseCard.vue'

@Component({
  components: {
    BaseCard
  }
})
export default class ShopCard extends Vue {
  @Prop({ required: true }) shopName!: string
  @Prop({ default: '' }) logoSrc!: string
}
</script> 