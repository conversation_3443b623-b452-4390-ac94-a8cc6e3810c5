<template>
  <div 
    class="shop-card-optimized"
    @click="handleClick"
  >
    <!-- 图标区域 -->
    <div class="icon-container">
      <img 
        v-if="logoSrc" 
        :src="logoSrc" 
        :alt="shopName"
        class="shop-icon"
        @error="onImageError"
      />
      <div v-else class="icon-placeholder">
        {{ placeholderText }}
      </div>
    </div>
    
    <!-- 标题区域 - 保留走马灯 -->
    <div class="title-container">
      <div 
        v-if="enableMarquee && shouldShowMarquee"
        class="marquee-container"
      >
        <div 
          class="marquee-text"
          :style="marqueeStyle"
        >
          {{ shopName }}
        </div>
      </div>
      <div v-else class="static-title">
        {{ shopName }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component
export default class ShopCardOptimized extends Vue {
  @Prop({ required: true }) shopName!: string
  @Prop({ default: '' }) logoSrc!: string
  @Prop({ default: true }) enableMarquee!: boolean
  @Prop({ default: 4 }) marqueeSpeed!: number
  @Prop({ default: 1.5 }) marqueeDelay!: number

  private shouldShowMarquee = false
  private marqueeAnimationId: number | null = null

  get placeholderText(): string {
    return this.shopName?.charAt(0) || '?'
  }

  get marqueeStyle(): any {
    if (!this.shouldShowMarquee) return {}
    
    return {
      animation: `marquee-scroll ${this.marqueeSpeed}s linear infinite`,
      animationDelay: `${this.marqueeDelay}s`
    }
  }

  mounted() {
    if (this.enableMarquee) {
      this.checkMarqueeNeed()
    }
  }

  beforeDestroy() {
    if (this.marqueeAnimationId) {
      cancelAnimationFrame(this.marqueeAnimationId)
    }
  }

  private checkMarqueeNeed() {
    this.$nextTick(() => {
      const container = this.$el.querySelector('.title-container') as HTMLElement
      const title = this.$el.querySelector('.static-title') as HTMLElement
      
      if (container && title) {
        this.shouldShowMarquee = title.scrollWidth > container.clientWidth
      }
    })
  }

  private handleClick(event: Event): void {
    // 简化的点击处理，不播放音效
    this.$emit('click', event)
  }

  private onImageError(event: Event): void {
    const target = event.target as HTMLImageElement
    if (target) {
      target.style.display = 'none'
    }
  }
}
</script>

<style scoped>
.shop-card-optimized {
  /* 强制卡片尺寸和布局 */
  width: 428px !important;
  height: 428px !important;
  min-width: 428px !important;
  min-height: 428px !important;
  max-width: 428px !important;
  max-height: 428px !important;

  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;

  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;

  cursor: pointer;

  /* 强制禁用所有动效和优化 */
  transition: none !important;
  transform: none !important;
  will-change: auto !important;
  contain: none !important;
  backdrop-filter: none !important;
  box-shadow: none !important;

  /* 简化渲染 */
  backface-visibility: visible !important;
  perspective: none !important;
}

.shop-card-optimized:hover {
  /* 极简hover效果 */
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.12);
  
  /* 禁用所有变换 */
  transform: none;
  box-shadow: none;
}

.icon-container {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  
  /* 禁用所有效果 */
  transform: none;
  transition: none;
}

.shop-icon {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  
  /* 优化图片渲染 */
  image-rendering: optimizeSpeed;
  
  /* 禁用所有效果 */
  filter: none;
  transform: none;
  transition: none;
}

.icon-placeholder {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: 600;
  color: white;
  
  /* 禁用所有效果 */
  transform: none;
  transition: none;
}

.title-container {
  width: 360px;
  height: 60px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* 禁用所有效果 */
  transform: none;
  transition: none;
}

.static-title {
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-weight: 400;
  color: white;
  text-align: center;
  line-height: 1.2;
  
  /* 简化文本渲染 */
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: auto;
  
  /* 禁用所有效果 */
  transform: none;
  transition: none;
}

/* 走马灯效果 - 保留但简化 */
.marquee-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
}

.marquee-text {
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-weight: 400;
  color: white;
  white-space: nowrap;
  
  /* 简化文本渲染 */
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: auto;
  
  /* 走马灯动画 */
  animation-fill-mode: both;
}

@keyframes marquee-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .shop-card-optimized,
  .shop-card-optimized * {
    transform: none !important;
    transition: none !important;
    animation: none !important;
    will-change: auto !important;
    contain: none !important;
    backdrop-filter: none !important;
    filter: none !important;
    box-shadow: none !important;
  }
  
  /* 移动端保留走马灯 */
  .marquee-text {
    animation: marquee-scroll 4s linear infinite !important;
  }
}
</style>
