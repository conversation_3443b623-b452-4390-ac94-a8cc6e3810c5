<template>
  <div 
    ref="container"
    :class="['marquee-container', containerClass, { 'overflowing': isOverflowing }]"
    :style="containerStyle"
  >
    <div 
      ref="textElement"
      :class="['marquee-text', textClass]"
      :style="computedTextStyle"
    >
      {{ text }}
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

@Component
export default class MarqueeText extends Vue {
  @Prop({ required: true })
  text!: string
  
  @Prop({ default: '' })
  containerClass!: string
  
  @Prop({ default: '' })
  textClass!: string
  
  @Prop({ default: () => ({}) })
  containerStyle!: Record<string, any>
  
  @Prop({ default: () => ({}) })
  textStyle!: Record<string, any>
  
  @Prop({ default: 3 })
  speed!: number // 滚动速度，秒数
  
  @Prop({ default: 0.3 })
  delay!: number // 开始滚动前的延迟，秒数

  @Prop({ default: 'bounce' })
  animationType!: 'linear' | 'bounce' // 动画类型

  private isOverflowing = false
  private animationDuration = 0
  private resizeObserver: ResizeObserver | null = null

  mounted() {
    this.checkOverflow()
    this.setupResizeObserver()
  }

  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  }

  @Watch('text')
  onTextChange() {
    this.$nextTick(() => {
      this.checkOverflow()
    })
  }

  private setupResizeObserver() {
    if ('ResizeObserver' in window) {
      this.resizeObserver = new ResizeObserver(() => {
        this.checkOverflow()
      })
      this.resizeObserver.observe(this.$refs.container as Element)
    }
  }

  private checkOverflow() {
    const container = this.$refs.container as HTMLElement
    const textElement = this.$refs.textElement as HTMLElement
    
    if (!container || !textElement) {
      return
    }

    // 重置样式以准确测量
    textElement.style.animation = 'none'
    textElement.style.transform = 'none'
    textElement.style.width = 'auto'
    textElement.style.maxWidth = 'none'
    
    this.$nextTick(() => {
      const containerWidth = container.offsetWidth
      const textWidth = textElement.scrollWidth
      
      this.isOverflowing = textWidth > containerWidth
      
      if (this.isOverflowing) {
        // 计算精确的移动距离 - 文本宽度超出容器的部分
        const overflowWidth = textWidth - containerWidth
        const moveDistance = overflowWidth
        
        // 根据动画类型计算动画持续时间
        if (this.animationType === 'bounce') {
          this.animationDuration = Math.max(4, (moveDistance / containerWidth) * this.speed * 2) // 回弹动画需要更长时间
        } else {
          this.animationDuration = (moveDistance / containerWidth) * this.speed * 2 // 线性动画
        }
        
        // 设置CSS自定义属性来控制移动距离
        const containerElement = container as any
        containerElement.style.setProperty('--marquee-distance', `-${moveDistance}px`)
        
        // 根据动画类型应用不同的动画
        let animationCSS: string
        if (this.animationType === 'bounce') {
          animationCSS = `marquee-bounce-precise ${this.animationDuration}s ease-in-out ${this.delay}s infinite`
        } else {
          animationCSS = `marquee-precise ${this.animationDuration}s linear ${this.delay}s infinite`
        }
        
        textElement.style.animation = animationCSS
      } else {
        textElement.style.animation = 'none'
        textElement.style.transform = 'none'
      }
    })
  }

  get computedTextStyle() {
    return {
      ...this.textStyle,
      ...(this.isOverflowing ? {
        display: 'inline-block',
        whiteSpace: 'nowrap'
      } : {
        display: 'block',
        textAlign: 'center'
      })
    }
  }
}
</script>

<style scoped>
.marquee-container {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.marquee-text {
  white-space: nowrap;
}

/* 当文本溢出时启用滚动动画 */
.marquee-container.overflowing .marquee-text {
  display: inline-block;
  white-space: nowrap;
}

/* 当文本不溢出时，居中显示 */
.marquee-container:not(.overflowing) .marquee-text {
  display: block;
  white-space: nowrap;
  width: 100%;
  text-align: center;
  /* 移除text-overflow: ellipsis，因为它会阻止正确的宽度测量 */
}
</style>

<style>
/* 全局样式，用于keyframes动画 */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  50% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 回弹式走马灯动画 - 更平滑的效果 */
@keyframes marquee-bounce {
  0% {
    transform: translateX(0%);
  }
  25% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(-100%);
  }
  75% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* 精确控制的线性走马灯动画 */
@keyframes marquee-precise {
  0% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(var(--marquee-distance, -100px));
  }
  100% {
    transform: translateX(0px);
  }
}

/* 精确控制的回弹走马灯动画 */
@keyframes marquee-bounce-precise {
  0% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(var(--marquee-distance, -100px));
  }
  50% {
    transform: translateX(var(--marquee-distance, -100px));
  }
  75% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(0px);
  }
}
</style>