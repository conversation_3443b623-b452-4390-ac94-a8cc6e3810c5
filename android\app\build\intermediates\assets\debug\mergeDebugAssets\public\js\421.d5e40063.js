"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[421],{8421:(t,s,e)=>{e.r(s),e.d(s,{default:()=>x});var i=function(){var t=this,s=t._self._c;t._self._setupProxy;return s("div",{staticClass:"shop-detail-container responsive-page-container",style:t.containerStyle},[s("BackgroundImage"),s("TopBar"),s("div",{staticClass:"page-title-fixed page-title-fixed--shop"},[s("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.shopDetail")))])]),s("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[s("div",{staticClass:"main-content-container main-content-container--detail"},[t.loading?s("div",{staticClass:"loading-container"},[s("div",{staticClass:"loading-spinner"}),s("div",{staticClass:"loading-text"},[t._v(t._s(t.$t("common.loading")||"加载中..."))])]):t.error?s("div",{staticClass:"error-container"},[s("div",{staticClass:"error-icon"},[t._v("⚠️")]),s("div",{staticClass:"error-text"},[t._v("加载商店信息失败，即将返回商店列表...")])]):[t._m(0),s("div",{staticClass:"shop-info-container",style:t.containerHeightStyle},[s("div",{staticClass:"shop-logo"},[t.shopData.logo?s("img",{staticClass:"logo-image",attrs:{src:t.shopData.logo,alt:t.shopDisplayName}}):s("div",{staticClass:"logo-placeholder"},[t._v(t._s(t.shopDisplayName.charAt(0)))])]),s("div",{staticClass:"shop-details",style:t.detailsHeightStyle},[s("div",{staticClass:"shop-name-large"},[t._v(t._s(t.shopDisplayName))]),s("div",{staticClass:"shop-location-text"},[t._v(t._s(t.shopData.location))]),s("div",{staticClass:"shop-hours-text"},[t._v(t._s(t.shopData.hours))]),t.shopData.tel?s("div",{staticClass:"shop-tel-text"},[t._v(t._s(t.shopData.tel))]):t._e(),s("div",{staticClass:"shop-description-container"},[s("div",{staticClass:"shop-description-text",class:{expanded:t.isDescriptionExpanded},domProps:{innerHTML:t._s(t.renderedDescription)}}),t.shopData.description&&t.shopData.description.length>100?s("div",{staticClass:"expand-button",on:{click:t.toggleDescription}},[s("svg",{staticClass:"expand-icon",class:{rotated:t.isDescriptionExpanded},attrs:{viewBox:"0 0 24 24",fill:"none"}},[s("path",{attrs:{d:"M19 9L12 16L5 9",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])]):t._e()])]),s("div",{staticClass:"back-button",on:{click:t.goBackToShop}},[s("svg",{staticClass:"back-icon",attrs:{viewBox:"0 0 24 24",fill:"none"}},[s("path",{attrs:{d:"M19 12H5M12 5L5 12L12 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})]),s("span",{staticClass:"back-text"},[t._v(t._s(t.$t("common.back")))])])])]],2)]),s("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),s("BottomMarquee")],1)},a=[function(){var t=this,s=t._self._c;t._self._setupProxy;return s("div",{staticClass:"map-container"},[s("div",{staticClass:"floor-indicator"},[t._v("2/F")]),s("img",{staticClass:"map-image",attrs:{src:"/img/shops/shop-map.png",alt:"Shop Location Map"}})])}],o=e(1635),n=e(9603),r=e(3452),c=e(3205),l=e(4184),p=e(256),h=e(5185),d=e(7959),g=e(6004),u=e(8091),C=e(23);let v=class extends((0,n.Xe)(h.A,d.A,g.A)){getBackgroundColor(){return"#0F04A9"}shopData={id:"",name:"",name_tc:"",name_zh:"",location:"",hours:"",tel:"",description:"",logo:""};loading=!0;error=!1;isDescriptionExpanded=!1;renderedDescriptionCache="";get shopDisplayName(){const t=this.$i18n?.locale||"en";switch(t){case"zh-TW":case"zh-CN":return this.shopData.name_tc||this.shopData.name_zh||this.shopData.name;case"en":default:return this.shopData.name}}get descriptionLineCount(){if(!this.shopData.description)return 0;const t=Math.floor(38.925),s=Math.ceil(this.shopData.description.length/t);return s}get containerHeightStyle(){if(!this.isDescriptionExpanded)return{height:"879px"};const t=879,s=77,e=4,i=this.descriptionLineCount,a=Math.max(0,i-e),o=a*s,n=t+o;return{height:`${n}px`}}get detailsHeightStyle(){if(!this.isDescriptionExpanded)return{height:"553px"};const t=553,s=77,e=4,i=this.descriptionLineCount,a=Math.max(0,i-e),o=a*s,n=t+o;return{height:`${n}px`}}get renderedDescription(){if(!this.shopData.description)return"";if(this.renderedDescriptionCache)return this.renderedDescriptionCache;try{const t=C.xI.parse(this.shopData.description,{breaks:!0,gfm:!0});return"string"===typeof t?(this.renderedDescriptionCache=t,t):(t.then(t=>{this.renderedDescriptionCache=t,this.$forceUpdate()}),this.shopData.description)}catch(t){return console.error("Markdown渲染错误:",t),this.shopData.description}}mounted(){const t=window.Capacitor;t&&t.Plugins&&t.Plugins.App&&t.Plugins.App.addListener("backButton",this.handleBackButton),window.addEventListener("popstate",this.handlePopState)}beforeDestroy(){const t=window.Capacitor;t&&t.Plugins&&t.Plugins.App&&t.Plugins.App.removeAllListeners(),window.removeEventListener("popstate",this.handlePopState)}async created(){const t=this.$route.params.id;if(t)try{this.loading=!0,console.log("正在加载商店详情:",t);const s=await(0,u.z)(t);s?(this.shopData=s,this.renderedDescriptionCache="",console.log("成功加载商店详情:",s)):(console.log("未找到商店数据，返回商店列表"),this.$router.push("/shop"))}catch(s){console.error("加载商店详情失败:",s),this.error=!0,setTimeout(()=>{this.$router.push("/shop")},2e3)}finally{this.loading=!1}else this.$router.push("/shop")}goBackToShop(){this.playClickSound(),window.history.length>1?this.$router.go(-1):this.$router.push("/shop")}handleBackButton(){this.goBackToShop()}handlePopState(){}toggleDescription(){this.playClickSound(),this.isDescriptionExpanded=!this.isDescriptionExpanded}};v=(0,o.Cg)([(0,n.uA)({components:{TopBar:r.A,BottomBar:c.A,BottomMarquee:l.A,BackgroundImage:p.A}})],v);const m=v,D=m;var _=e(1656),k=(0,_.A)(D,i,a,!1,null,"6f1e17ee",null);const x=k.exports}}]);