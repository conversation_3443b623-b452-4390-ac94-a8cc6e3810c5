// 國際化 Vue Mixin
import { Vue, Component } from 'vue-property-decorator'
import i18nService, { Language, LanguageConfig } from '@/i18n'

@Component
export default class I18nMixin extends Vue {
  /**
   * 翻譯文字
   */
  $t(key: string, fallback?: string): string {
    return i18nService.t(key, fallback)
  }

  /**
   * 獲取當前語言
   */
  get $currentLanguage(): Language {
    return i18nService.getCurrentLanguage()
  }

  /**
   * 設置語言
   */
  $setLanguage(language: Language): void {
    i18nService.setCurrentLanguage(language)
    // 觸發重新渲染
    this.$forceUpdate()
    // 通知其他組件語言已變更
    this.$root.$emit('language-changed', language)
  }

  /**
   * 獲取語言配置
   */
  $getLanguageConfig(language?: Language): LanguageConfig | null {
    return i18nService.getLanguageConfig(language)
  }

  /**
   * 獲取所有支持的語言
   */
  get $supportedLanguages(): LanguageConfig[] {
    return i18nService.getSupportedLanguages()
  }

  /**
   * 檢查是否為當前語言
   */
  $isCurrentLanguage(language: Language): boolean {
    return this.$currentLanguage === language
  }

  /**
   * 獲取當前語言的本地化名稱
   */
  get $currentLanguageNativeName(): string {
    const config = this.$getLanguageConfig()
    return config?.nativeName || this.$currentLanguage
  }

  /**
   * 生命週期 - 監聽語言變更事件
   */
  mounted() {
    // 監聽語言變更事件
    this.$root.$on('language-changed', this.onLanguageChanged)
  }

  /**
   * 生命週期 - 清理事件監聽
   */
  beforeDestroy() {
    this.$root.$off('language-changed', this.onLanguageChanged)
  }

  /**
   * 語言變更處理
   */
  onLanguageChanged(language: Language) {
    // 強制更新組件
    this.$forceUpdate()
  }
} 