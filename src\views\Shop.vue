<template>
  <div class="shop-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Shop主标题 - 固定位置 -->
    <div class="page-title-fixed">
      <h1 class="app-title">{{ $t('pageTitle.shop') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="content-area " :style="contentAreaStyle">
      <div class="main-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">{{ $t('common.loading') || '加载中...' }}</div>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ errorMessage }}</div>
          <button class="retry-button" @click="retryLoadShops">
            {{ $t('common.retry') || '重试' }}
          </button>
        </div>
        
        <!-- 商店卡片网格 - 使用优化版本 -->
        <div v-else class="shop-grid">
          <ShopCardOptimized
            v-for="shop in shops"
            :key="shop.id"
            :shop-name="getShopDisplayName(shop)"
            :logo-src="shop.logo"
            :enable-marquee="true"
            :marquee-speed="4"
            :marquee-delay="1.5"
            @click="navigateToShop(shop.id)"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import ShopCardOptimized from '@/components/ShopCardOptimized.vue'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { ShopBasicInfo, getShopsBasicInfo } from '@/services/shopService'

@Component({
  components: {
    ShopCardOptimized,
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class Shop extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#0F04A9' // Shop页面的蓝色背景
  }

  // 数据状态
  private shops: ShopBasicInfo[] = []
  private loading = true
  private error = false
  private errorMessage = ''

  // 根据当前语言获取店铺显示名称
  public getShopDisplayName(shop: ShopBasicInfo): string {
    // 安全地访问i18n，如果未初始化则使用默认值
    const currentLang = (this as any).$i18n?.locale || 'en'
    
    switch (currentLang) {
      case 'zh-TW':
      case 'zh-CN':
        return shop.name_tc || shop.name_zh || shop.name
      case 'en':
      default:
        return shop.name
    }
  }

  async mounted() {
    // 禁用Shop页面的音效以提升性能
    try {
      const { soundService } = await import('@/services/audioManager')
      soundService.setEnabled(false)
      console.log('Shop页面音效已禁用以提升滚动性能')
    } catch (error) {
      console.warn('禁用音效失败:', error)
    }

    await this.loadShops()
  }

  beforeDestroy() {
    // 离开Shop页面时重新启用音效
    try {
      const { soundService } = require('@/services/audioManager')
      soundService.setEnabled(true)
      console.log('离开Shop页面，音效已重新启用')
    } catch (error) {
      console.warn('重新启用音效失败:', error)
    }
  }

  async loadShops() {
    this.loading = true
    this.error = false
    this.errorMessage = ''

    try {
      console.log('开始加载商店数据...')
      this.shops = await getShopsBasicInfo()
      console.log(`成功加载${this.shops.length}个商店`)
    } catch (error) {
      console.error('加载商店数据失败:', error)
      this.error = true
      this.errorMessage = error instanceof Error 
        ? `加载失败: ${error.message}` 
        : '网络连接错误，请检查网络后重试'
    } finally {
      this.loading = false
    }
  }

  async retryLoadShops() {
    await this.loadShops()
  }

  navigateToShop(shopId: string) {
    console.log('导航到商店:', shopId)
    this.$router.push(`/shop/${shopId}`)
  }
}
</script>

<style scoped>
/* 引入Shop页面极端性能优化 */
@import '@/styles/shop-performance.css';
.content-area {
  /* 极简滚动 - 移除所有优化以避免卡顿 */
  overflow-y: scroll;
  overflow-x: hidden;

  /* 禁用所有可能导致卡顿的属性 */
  -webkit-overflow-scrolling: auto;
  transform: none;
  will-change: auto;
  contain: none;

  /* 隐藏滚动条以减少渲染负担 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 完全隐藏滚动条以提升性能 */
.content-area::-webkit-scrollbar {
  display: none;
}

.main-content {
  position: relative;
  width: 100%;
  min-height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.shop-grid {
  /* 强制4列网格布局 */
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  width: 100% !important;
  max-width: 1600px !important;
  margin: 0 auto !important;

  /* 移除所有优化属性以避免卡顿 */
  contain: none !important;
  transform: none !important;
  will-change: auto !important;
}

/* 确保每个卡片的尺寸正确 */
.shop-grid > * {
  width: 100% !important;
  aspect-ratio: 1 !important;
  max-width: 380px !important;
  flex-shrink: 0 !important;
}

/* 固定元素样式 */
.main-title {
  position: absolute;
  top: 340px;
  left: 922px;
  width: 316px;
  height: 155px;
  z-index: 15;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  color: white;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-top: 6px solid #00EEFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-family: 'Inter', sans-serif;
  font-size: 48px;
  font-weight: 300;
  color: white;
  text-align: center;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  color: white;
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 120px;
  margin-bottom: 30px;
}

.error-text {
  font-family: 'Inter', sans-serif;
  font-size: 36px;
  font-weight: 300;
  color: white;
  margin-bottom: 40px;
  max-width: 800px;
  line-height: 1.4;
}

.retry-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-weight: 300;
  padding: 20px 40px;
  cursor: pointer;

  /* 移除所有动效 */
  transition: none;
  transform: none;
  backdrop-filter: none;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  /* 移除变换 */
  transform: none;
}

.retry-button:active {
  /* 移除变换 */
  transform: none;
}

</style>