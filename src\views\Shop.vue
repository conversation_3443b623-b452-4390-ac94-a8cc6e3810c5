<template>
  <div class="shop-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Shop主标题 - 固定位置 -->
    <div class="page-title-fixed">
      <h1 class="app-title">{{ $t('pageTitle.shop') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="content-area " :style="contentAreaStyle">
      <div class="main-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">{{ $t('common.loading') || '加载中...' }}</div>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ errorMessage }}</div>
          <button class="retry-button" @click="retryLoadShops">
            {{ $t('common.retry') || '重试' }}
          </button>
        </div>
        
        <!-- 商店卡片网格 -->
        <div v-else class="shop-grid">
          <ShopCard
            v-for="shop in shops"
            :key="shop.id"
            :shop-name="getShopDisplayName(shop)"
            :logo-src="shop.logo"
            @click="navigateToShop(shop.id)"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import ShopCard from '@/components/ShopCard.vue'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { ShopBasicInfo, getShopsBasicInfo } from '@/services/shopService'

@Component({
  components: {
    ShopCard,
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class Shop extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#0F04A9' // Shop页面的蓝色背景
  }

  // 数据状态
  private shops: ShopBasicInfo[] = []
  private loading = true
  private error = false
  private errorMessage = ''

  // 根据当前语言获取店铺显示名称
  public getShopDisplayName(shop: ShopBasicInfo): string {
    // 安全地访问i18n，如果未初始化则使用默认值
    const currentLang = (this as any).$i18n?.locale || 'en'
    
    switch (currentLang) {
      case 'zh-TW':
      case 'zh-CN':
        return shop.name_tc || shop.name_zh || shop.name
      case 'en':
      default:
        return shop.name
    }
  }

  async mounted() {
    await this.loadShops()
  }

  async loadShops() {
    this.loading = true
    this.error = false
    this.errorMessage = ''

    try {
      console.log('开始加载商店数据...')
      this.shops = await getShopsBasicInfo()
      console.log(`成功加载${this.shops.length}个商店`)
    } catch (error) {
      console.error('加载商店数据失败:', error)
      this.error = true
      this.errorMessage = error instanceof Error 
        ? `加载失败: ${error.message}` 
        : '网络连接错误，请检查网络后重试'
    } finally {
      this.loading = false
    }
  }

  async retryLoadShops() {
    await this.loadShops()
  }

  navigateToShop(shopId: string) {
    console.log('导航到商店:', shopId)
    this.$router.push(`/shop/${shopId}`)
  }
}
</script>

<style scoped>
.content-area {
  /* Webview滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 减少重绘 */
  contain: layout style paint;
  
  /* 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255,255,255,0.4) rgba(255,255,255,0.1);
}

/* WebKit浏览器滚动条样式 */
.content-area::-webkit-scrollbar {
  width: 12px;
}

.content-area::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 6px;
}

.content-area::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.4);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  min-height: 30px;
}

.content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.6);
  background-clip: content-box;
}

.main-content {
  position: relative;
  width: 100%;
  min-height: 100%;
  padding: 112px;
  box-sizing: border-box;
}

.shop-grid {
  /* 固定4列布局，保持原设计 */
  display: grid;
  grid-template-columns: repeat(4, 428px);
  gap: 91px 70px;
  width: 1922px;
  margin: 0 auto;
  
  /* 性能优化 */
  contain: layout;
  transform: translateZ(0);
}

/* 固定元素样式 */
.main-title {
  position: absolute;
  top: 340px;
  left: 922px;
  width: 316px;
  height: 155px;
  z-index: 15;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  color: white;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-top: 6px solid #00EEFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-family: 'Inter', sans-serif;
  font-size: 48px;
  font-weight: 300;
  color: white;
  text-align: center;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  color: white;
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 120px;
  margin-bottom: 30px;
}

.error-text {
  font-family: 'Inter', sans-serif;
  font-size: 36px;
  font-weight: 300;
  color: white;
  margin-bottom: 40px;
  max-width: 800px;
  line-height: 1.4;
}

.retry-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-weight: 300;
  padding: 20px 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.retry-button:active {
  transform: translateY(0);
}

</style>