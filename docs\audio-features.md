# 全局音效功能说明

## 功能概述
已为项目实现全局按钮音效系统，包括多种使用方式和灵活的配置选项。

## 实现的功能

### 1. 全局音效覆盖
- **GlassButton组件**：主页面大按钮，已有音效
- **BaseCard组件**：Shop卡片、设施卡片、交通按钮等，新增音效
- **原生button元素**：Video页面控制按钮等，通过指令添加音效
- **自定义音频文件**：使用你的 `button_voice.mp3` 文件

### 2. 多种使用方式

#### A. 组件内置音效（推荐）
自动为组件提供音效，无需额外配置：
```vue
<!-- GlassButton 和 BaseCard 自动有音效 -->
<GlassButton @click="doSomething">按钮</GlassButton>
<BaseCard title="卡片" @click="cardClick" />
```

#### B. 全局指令方式
为任意元素添加音效：
```vue
<!-- 基础用法 -->
<button v-click-sound @click="handleClick">原生按钮</button>

<!-- 自定义配置 -->
<button v-click-sound="{ volume: 0.6, enabled: true }" @click="handleClick">
  自定义音量
</button>

<!-- 禁用音效 -->
<button v-click-sound="false" @click="handleClick">静音按钮</button>
```

#### C. 编程方式
在代码中直接调用：
```typescript
// 在Vue组件中
this.$playClickSound() // 默认音量
this.$playClickSound(0.6) // 自定义音量

// 使用Mixin的组件中
this.$playClickSound(0.4, 'buttonClick')
```

#### D. SoundMixin方式
继承音效功能：
```typescript
import SoundMixin from '@/mixins/SoundMixin'

@Component
export default class MyComponent extends Mixins(SoundMixin) {
  handleClick() {
    this.$playClickSound()
    // 其他逻辑
  }
}
```

### 3. 配置选项

#### 组件级配置
```vue
<!-- 禁用特定组件的音效 -->
<GlassButton :enable-sound="false">静音按钮</GlassButton>
<BaseCard :enable-sound="false" title="静音卡片" />
```

#### 全局音效控制
```typescript
// 禁用所有音效
this.$setSoundEnabled(false)

// 启用所有音效  
this.$setSoundEnabled(true)

// 检查音效状态
if (this.$isSoundEnabled()) {
  console.log('音效已启用')
}
```

### 4. 已适配的组件和页面

#### ✅ 已完成音效适配
- **Home页面**：所有9个功能按钮（GlassButton）
- **BottomBar**：AI按钮、Home按钮、语言切换按钮
- **Shop页面**：商店卡片（BaseCard）
- **Office页面**：办公设施卡片（BaseCard）
- **Transport页面**：交通方式按钮（BaseCard）
- **Facility页面**：设施卡片（BaseCard）
- **Video页面**：音量控制按钮（原生button + 指令）
- **AIChat页面**：发送按钮（GlassButton）

#### 🎵 音效特性
- **智能备用**：优先使用音频文件，失败时自动切换到合成音效
- **移动端优化**：音量和参数专门为Android设备调整
- **用户交互激活**：符合现代浏览器音频政策
- **性能优化**：单例服务，资源占用极低

## 技术实现

### 核心组件
1. **SoundService** (`src/services/soundService.ts`) - 音效服务单例
2. **SoundMixin** (`src/mixins/SoundMixin.ts`) - 音效功能Mixin
3. **SoundPlugin** (`src/plugins/sound.ts`) - 全局音效插件和指令
4. **音频文件** (`public/sounds/button_voice.mp3`) - 自定义音效

### 音效层级
1. **优先级1**：自定义音频文件 (`button_voice.mp3`)
2. **优先级2**：复杂合成音效（三角波 + 低通滤波）
3. **优先级3**：简单备用音效（正弦波）

## 使用建议

### 为新组件添加音效
```vue
<template>
  <!-- 方式1：使用指令 -->
  <button v-click-sound @click="handleClick">新按钮</button>
  
  <!-- 方式2：继承SoundMixin -->
  <button @click="handleClickWithSound">新按钮</button>
</template>

<script>
import SoundMixin from '@/mixins/SoundMixin'

export default class NewComponent extends Mixins(SoundMixin) {
  handleClickWithSound() {
    this.$playClickSound()
    // 其他逻辑
  }
}
</script>
```

### 音效调试
```typescript
// 检查音频状态
console.log('音频状态:', this.$audioState)
console.log('音效启用:', this.$isSoundEnabled())

// 测试音效
this.$playClickSound(0.8) // 大音量测试
```

## Android APK兼容性
- ✅ 音效文件自动打包到APK
- ✅ 移动端音频系统预热
- ✅ Capacitor平台检测和优化
- ✅ 用户交互激活机制

现在整个项目的按钮都有统一的音效体验！🎵