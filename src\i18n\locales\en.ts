// English language pack
export default {
  // Common
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    search: 'Search',
    filter: 'Filter',
    all: 'All',
    none: 'None',
    yes: 'Yes',
    no: 'No'
  },

  // Navigation
  nav: {
    home: 'Home',
    shop: 'Shop',
    food: 'Food',
    office: 'Office',
    facility: 'Facility',
    poster: 'Poster',
    transport: 'Transport',
    worldTime: 'World Time',
    video: 'Video',
    about: 'About',
    web: 'Web',
    aiSearch: 'AI Search'
  },

  // Page titles
  pageTitle: {
    eMap: 'eMap',
    shop: 'Shop',
    food: 'Food',
    office: 'Office',
    facility: 'Facility',
    poster: 'Poster',
    transport: 'Transport',
    worldTime: 'World Time',
    video: 'Sport Video',
    about: 'About',
    web: 'Web Browser',
    airQuality: 'Air Quality Index',
    aiSearch: 'Search',
    aiChat: 'AI Assistant',
    shopDetail: 'Shop'
  },

  // Shop related
  shop: {
    name: 'Shop Name',
    location: 'Location',
    hours: 'Opening Hours',
    description: 'Description',
    searchPlaceholder: 'Search shop name',
    noResults: 'No shops found',
    tryOtherKeywords: 'Please try other keywords',
    startSearch: 'Start Search',
    searchPrompt: 'Enter shop name in the search box above to find your desired shop'
  },

  // Office related
  office: {
    companyName: 'Company Name',
    roomNumber: 'Room Number',
    floor: 'Floor',
    byFloor: 'by Floor',
    byName: 'by Name',
    filterBy: 'Filter by'
  },

  // Facility related
  facility: {
    men: 'Men',
    women: 'Women',
    baby: 'Baby',
    services: 'Services',
    lift: 'Lift',
    escalator: 'Escalator',
    accessibly: 'Accessibly',
    locker: 'Locker'
  },

  // Poster related
  poster: {
    title: 'Title',
    description: 'Description',
    previous: 'Previous',
    next: 'Next',
    pause: 'Pause',
    play: 'Play',
    autoplay: 'Autoplay',
    defaultTitle: 'Poster',
    defaultDescription: 'View exciting content'
  },

  // Transport related
  transport: {
    bus: 'Bus',
    mtr: 'MTR',
    lightRail: 'Light Rail',
    miniBus: 'Mini Bus',
    nearby: 'Nearby Transport',
    schedule: 'Schedule',
    route: 'Route'
  },

  // Food related
  food: {
    title: 'Dining Services',
    comingSoon: 'Coming Soon'
  },

  // About page related
  about: {
    title: 'About eMap AI',
    techStack: 'Tech Stack',
    features: 'Features',
    version: 'Version Info',
    team: 'Team Info'
  },

  // World time related
  worldTime: {
    title: 'World Time',
    realtimeTitle: 'Real-time World Time',
    hongkong: 'Hong Kong',
    tokyo: 'Tokyo',
    newyork: 'New York',
    london: 'London',
    paris: 'Paris',
    sydney: 'Sydney',
    beijing: 'Beijing',
    seoul: 'Seoul',
    dubai: 'Dubai',
    currentTime: 'Current Time',
    timezone: 'Timezone'
  },

  // Video related
  video: {
    title: 'Title',
    description: 'Description',
    duration: 'Duration',
    category: 'Category',
    mute: 'Mute',
    unmute: 'Unmute',
    fullscreen: 'Fullscreen',
    mutedNotice: 'Videos play muted by default'
  },

  // Weather related
  weather: {
    temperature: 'Temperature',
    feelsLike: 'Feels Like',
    humidity: 'Humidity',
    sunny: 'Sunny',
    cloudy: 'Cloudy',
    rainy: 'Rainy',
    snowy: 'Snowy',
    stormy: 'Stormy'
  },

  // Language related
  language: {
    current: 'Current Language',
    switch: 'Switch Language',
    traditionalChinese: 'Traditional Chinese',
    english: 'English',
    spanish: 'Spanish',
    short: 'EN'
  },

  // About page detailed content
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - Progressive JavaScript Framework',
      typescript: 'TypeScript - JavaScript superset with type system',
      tailwind: 'TailwindCSS - Utility-first CSS framework',
      capacitor: 'Capacitor - Cross-platform native app building tool'
    },
    features: {
      smartNavigation: 'Smart Navigation System',
      realtimeLocation: 'Real-time Location Service',
      multiLanguage: 'Multi-language Support',
      crossPlatform: 'Cross-platform Compatibility'
    },
    version: {
      current: 'Current Version: v2.1.0',
      releaseDate: 'Release Date: 2024',
      updateFrequency: 'Update Frequency: Monthly Updates',
      supportedPlatforms: 'Supported Platforms: iOS, Android, Web'
    },
    team: {
      frontend: 'Frontend Development: Vue.js + TypeScript',
      mobile: 'Mobile Development: Capacitor Cross-platform',
      design: 'UI/UX Design: Modern Glass Style',
      data: 'Data Support: Real-time Sync'
    }
  },

  // City names
  cities: {
    hongkong: 'Hong Kong',
    tokyo: 'Tokyo',
    newyork: 'New York',
    london: 'London',
    paris: 'Paris',
    sydney: 'Sydney',
    beijing: 'Beijing',
    seoul: 'Seoul',
    dubai: 'Dubai',
    losangeles: 'Los Angeles'
  },

  // Poster content
  posterContent: {
    splus: {
      title: 'S+ REWARDS Members',
      description: 'Add more to life, register as S+ REWARDS member now for continuous surprises and rewards'
    },
    ikea: {
      title: 'IKEA Home Ideas',
      description: 'HomeSquare IKEA promotional activities, furniture discounts not to be missed'
    },
    more: {
      title: 'More Promotional Information',
      description: 'View more mall promotions and event details'
    }
  },

  // Video related
  videoContent: {
    sound: {
      on: 'Turn On Sound',
      off: 'Turn Off Sound',
      notice: 'Click the button below or the sound icon on the video player to enable volume'
    },
    videos: {
      basketball: {
        title: 'Hotel Sport Event - Basketball Highlights',
        description: 'Exciting moments review of hotel sports basketball competition',
        category: 'Basketball'
      },
      swimming: {
        title: 'Swimming Competition Highlights',
        description: 'Intense competition and exciting performance of swimming matches',
        category: 'Swimming'
      },
      tennis: {
        title: 'Tennis Championship Final',
        description: 'Exciting showdown of tennis championship final',
        category: 'Tennis'
      }
    }
  },

  // AI search
  aiSearch: {
    placeholder: 'Search shops...'
  },

  // AI chat
  aiChat: {
    welcomeTitle: 'Hello, I\'m Winnie!',
    welcomeMessage: 'I\'m the smart customer service assistant for this mall. How can I help you?',
    inputPlaceholder: 'Please enter your question...',
    listening: 'Listening...',
    sendMessage: 'Send',
    voiceInput: 'Voice Input',
    voiceMessage: '[Voice Message]',
    typing: 'Winnie is typing...',
    error: 'Sorry, I can\'t respond to your message right now. Please try again later.',
    newChat: 'New Chat',
    clearChat: 'Clear Chat',
    recordingGuide: 'Recording... Please speak...'
  }
}
