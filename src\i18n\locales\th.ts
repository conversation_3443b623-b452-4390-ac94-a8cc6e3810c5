// Thai language pack
export default {
  // ทั่วไป
  common: {
    loading: 'กำลังโหลด...',
    error: 'ข้อผิดพลาด',
    success: 'สำเร็จ',
    cancel: 'ยกเลิก',
    confirm: 'ยืนยัน',
    back: 'กลับ',
    next: 'ถัดไป',
    previous: 'ก่อนหน้า',
    close: 'ปิด',
    save: 'บันทึก',
    delete: 'ลบ',
    edit: 'แก้ไข',
    search: 'ค้นหา',
    filter: 'กรอง',
    all: 'ทั้งหมด',
    none: 'ไม่มี',
    yes: 'ใช่',
    no: 'ไม่'
  },

  // การนำทาง
  nav: {
    home: 'หน้าหลัก',
    shop: 'ร้านค้า',
    food: 'อาหาร',
    office: 'สำนักงาน',
    facility: 'สิ่งอำนวยความสะดวก',
    poster: 'โปสเตอร์',
    transport: 'การขนส่ง',
    worldTime: 'เวลาโลก',
    video: 'วิดีโอ',
    about: 'เกี่ยวกับ',
    web: 'เว็บ',
    aiSearch: 'ค้นหา AI'
  },

  // ชื่อหน้า
  pageTitle: {
    eMap: 'eMap',
    shop: 'ร้านค้า',
    food: 'อาหาร',
    office: 'สำนักงาน',
    facility: 'สิ่งอำนวยความสะดวก',
    poster: 'โปสเตอร์',
    transport: 'การขนส่ง',
    worldTime: 'เวลาโลก',
    video: 'วิดีโอกีฬา',
    about: 'เกี่ยวกับ',
    web: 'เว็บเบราว์เซอร์',
    airQuality: 'ดัชนีคุณภาพอากาศ',
    aiSearch: 'ค้นหา',
    aiChat: 'ผู้ช่วย AI',
    shopDetail: 'ร้านค้า'
  },

  // เกี่ยวกับร้านค้า
  shop: {
    name: 'ชื่อร้าน',
    location: 'สถานที่',
    hours: 'เวลาทำการ',
    description: 'รายละเอียด',
    searchPlaceholder: 'ค้นหาชื่อร้าน',
    noResults: 'ไม่พบร้านค้า',
    tryOtherKeywords: 'โปรดลองใช้คำค้นหาอื่น',
    startSearch: 'เริ่มค้นหา',
    searchPrompt: 'ป้อนชื่อร้านในช่องค้นหาด้านบนเพื่อค้นหาร้านที่ต้องการ'
  },

  // เกี่ยวกับสำนักงาน
  office: {
    companyName: 'ชื่อบริษัท',
    roomNumber: 'หมายเลขห้อง',
    floor: 'ชั้น',
    byFloor: 'ตามชั้น',
    byName: 'ตามชื่อ',
    filterBy: 'กรองโดย'
  },

  // เกี่ยวกับสิ่งอำนวยความสะดวก
  facility: {
    men: 'ผู้ชาย',
    women: 'ผู้หญิง',
    baby: 'เด็ก',
    services: 'บริการ',
    lift: 'ลิฟต์',
    escalator: 'บันไดเลื่อน',
    accessibly: 'การเข้าถึง',
    locker: 'ล็อกเกอร์'
  },

  // เกี่ยวกับโปสเตอร์
  poster: {
    title: 'ชื่อเรื่อง',
    description: 'รายละเอียด',
    previous: 'ก่อนหน้า',
    next: 'ถัดไป',
    pause: 'หยุดชั่วคราว',
    play: 'เล่น',
    autoplay: 'เล่นอัตโนมัติ',
    defaultTitle: 'โปสเตอร์',
    defaultDescription: 'ดูเนื้อหาที่น่าสนใจ'
  },

  // เกี่ยวกับการขนส่ง
  transport: {
    bus: 'รถบัส',
    mtr: 'รถไฟฟ้า',
    lightRail: 'รถไฟฟ้าเบา',
    miniBus: 'รถมินิบัส',
    nearby: 'การขนส่งใกล้เคียง',
    schedule: 'ตารางเวลา',
    route: 'เส้นทาง'
  },

  // เกี่ยวกับอาหาร
  food: {
    title: 'บริการร้านอาหาร',
    comingSoon: 'เร็วๆ นี้'
  },

  // เกี่ยวกับหน้าแนะนำ
  about: {
    title: 'เกี่ยวกับ eMap AI',
    techStack: 'เทคโนโลยี',
    features: 'คุณสมบัติ',
    version: 'ข้อมูลเวอร์ชัน',
    team: 'ข้อมูลทีม'
  },

  // เกี่ยวกับเวลาโลก
  worldTime: {
    title: 'เวลาโลก',
    realtimeTitle: 'เวลาโลกแบบเรียลไทม์',
    hongkong: 'ฮ่องกง',
    tokyo: 'โตเกียว',
    newyork: 'นิวยอร์ก',
    london: 'ลอนดอน',
    paris: 'ปารีส',
    sydney: 'ซิดนีย์',
    beijing: 'ปักกิ่ง',
    seoul: 'โซล',
    dubai: 'ดูไบ',
    currentTime: 'เวลาปัจจุบัน',
    timezone: 'เขตเวลา'
  },

  // เกี่ยวกับวิดีโอ
  video: {
    title: 'ชื่อเรื่อง',
    description: 'รายละเอียด',
    duration: 'ความยาว',
    category: 'หมวดหมู่',
    mute: 'ปิดเสียง',
    unmute: 'เปิดเสียง',
    fullscreen: 'เต็มหน้าจอ',
    mutedNotice: 'วิดีโอเล่นโดยปิดเสียงตามค่าเริ่มต้น'
  },

  // เกี่ยวกับสภาพอากาศ
  weather: {
    temperature: 'อุณหภูมิ',
    feelsLike: 'รู้สึกเหมือน',
    humidity: 'ความชื้น',
    sunny: 'แสงแดด',
    cloudy: 'มีเมฆ',
    rainy: 'ฝนตก',
    snowy: 'หิมะตก',
    stormy: 'พายุ'
  },

  // เกี่ยวกับภาษา
  language: {
    current: 'ภาษาปัจจุบัน',
    switch: 'เปลี่ยนภาษา',
    traditionalChinese: 'จีนแบบดั้งเดิม',
    english: 'อังกฤษ',
    spanish: 'สเปน',
    japanese: 'ญี่ปุ่น',
    korean: 'เกาหลี',
    thai: 'ไทย',
    short: 'TH'
  },

  // เนื้อหาละเอียดของหน้าแนะนำ
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - Progressive JavaScript Framework',
      typescript: 'TypeScript - JavaScript superset พร้อมระบบ type',
      tailwind: 'TailwindCSS - Utility-first CSS framework',
      capacitor: 'Capacitor - เครื่องมือสร้างแอพพลิเคชันข้ามแพลตฟอร์ม'
    },
    features: {
      smartNavigation: 'ระบบนำทางอัจฉริยะ',
      realtimeLocation: 'บริการตำแหน่งแบบเรียลไทม์',
      multiLanguage: 'รองรับหลายภาษา',
      crossPlatform: 'ความเข้ากันได้ข้ามแพลตฟอร์ม'
    },
    version: {
      current: 'เวอร์ชันปัจจุบัน: v2.1.0',
      releaseDate: 'วันที่เปิดตัว: 2024',
      updateFrequency: 'ความถี่ในการอัพเดต: อัพเดตรายเดือน',
      supportedPlatforms: 'แพลตฟอร์มที่รองรับ: iOS, Android, Web'
    },
    team: {
      frontend: 'การพัฒนา Frontend: Vue.js + TypeScript',
      mobile: 'การพัฒนา Mobile: Capacitor Cross-platform',
      design: 'การออกแบบ UI/UX: สไตล์แก้วสมัยใหม่',
      data: 'การสนับสนุนข้อมูล: การซิงค์แบบเรียลไทม์'
    }
  },

  // ชื่อเมือง
  cities: {
    hongkong: 'ฮ่องกง',
    tokyo: 'โตเกียว',
    newyork: 'นิวยอร์ก',
    london: 'ลอนดอน',
    paris: 'ปารีส',
    sydney: 'ซิดนีย์',
    beijing: 'ปักกิ่ง',
    seoul: 'โซล',
    dubai: 'ดูไบ',
    losangeles: 'ลอสแองเจลิส'
  },

  // เนื้อหาโปสเตอร์
  posterContent: {
    splus: {
      title: 'สมาชิก S+ REWARDS',
      description: 'เพิ่มเติมให้กับชีวิต สมัครเป็นสมาชิก S+ REWARDS เดี๋ยวนี้เพื่อรับความประหลาดใจและรางวัลอย่างต่อเนื่อง'
    },
    ikea: {
      title: 'IKEA Home Ideas',
      description: 'กิจกรรมส่งเสริมการขาย HomeSquare IKEA ส่วนลดเฟอร์นิเจอร์ที่ไม่ควรพลาด'
    },
    more: {
      title: 'ข้อมูลการส่งเสริมการขายเพิ่มเติม',
      description: 'ดูการส่งเสริมการขายและรายละเอียดกิจกรรมของห้างสรรพสินค้าเพิ่มเติม'
    }
  },

  // เกี่ยวกับวิดีโอ
  videoContent: {
    sound: {
      on: 'เปิดเสียง',
      off: 'ปิดเสียง',
      notice: 'คลิกปุ่มด้านล่างหรือไอคอนเสียงบนเครื่องเล่นวิดีโอเพื่อเปิดเสียง'
    },
    videos: {
      basketball: {
        title: 'กิจกรรมกีฬาของโรงแรม - ไฮไลท์บาสเกตบอล',
        description: 'ย้อนดูช่วงเวลาที่น่าตื่นเต้นของการแข่งขันบาสเกตบอลกีฬาของโรงแรม',
        category: 'บาสเกตบอล'
      },
      swimming: {
        title: 'ไฮไลท์การแข่งขันว่ายน้ำ',
        description: 'การแข่งขันที่เข้มข้นและการแสดงที่น่าตื่นเต้นของการแข่งขันว่ายน้ำ',
        category: 'ว่ายน้ำ'
      },
      tennis: {
        title: 'รอบชิงชนะเลิศเทนนิส',
        description: 'การดวลที่น่าตื่นเต้นของรอบชิงชนะเลิศเทนนิส',
        category: 'เทนนิส'
      }
    }
  },

  // การค้นหา AI
  aiSearch: {
    placeholder: 'ค้นหาร้านค้า...'
  },

  // แชท AI
  aiChat: {
    welcomeTitle: 'สวัสดีค่ะ ฉันชื่อ Winnie!',
    welcomeMessage: 'ฉันเป็นผู้ช่วยบริการลูกค้าอัจฉริยะของห้างสรรพสินค้านี้ มีอะไรให้ฉันช่วยไหมคะ?',
    inputPlaceholder: 'โปรดป้อนคำถามของคุณ...',
    listening: 'กำลังฟัง...',
    sendMessage: 'ส่ง',
    voiceInput: 'ป้อนเสียง',
    voiceMessage: '[ข้อความเสียง]',
    typing: 'Winnie กำลังพิมพ์...',
    error: 'ขออภัย ตอนนี้ฉันไม่สามารถตอบข้อความของคุณได้ โปรดลองใหม่อีกครั้งในภายหลัง',
    newChat: 'แชทใหม่',
    clearChat: 'ล้างแชท',
    recordingGuide: 'กำลังบันทึก... โปรดพูด...'
  },

  // หน้าเว็บ
  web: {
    loading: 'กำลังโหลด...',
    error: 'โหลดล้มเหลว',
    refresh: 'รีเฟรช',
    back: 'กลับ'
  }
}