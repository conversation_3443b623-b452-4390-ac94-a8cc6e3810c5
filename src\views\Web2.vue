<template>
  <div class="web2-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Web2主标题 - 固定位置 -->
    <div class="page-title-fixed">
      <h1 class="app-title">{{ $t('pageTitle.airQuality') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="content-area" :style="contentAreaStyle">
      <div class="main-content">
        <!-- AQI折线图 -->
        <div class="chart-container">
          <h3 class="chart-title">空氣質量指數趨勢</h3>
          <canvas ref="lineChart" id="lineChart"></canvas>
        </div>
        
        <!-- 污染物柱状图 -->
        <div class="chart-container">
          <h3 class="chart-title">污染物濃度</h3>
          <canvas ref="barChart" id="barChart"></canvas>
        </div>

      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

// 动态导入Chart.js
let Chart: any = null

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class Web2 extends Mixins(ResponsiveMixin, I18nMixin) {
  private lineChart: any = null
  private barChart: any = null

  getBackgroundColor(): string {
    return '#1a1a2e' // AQI仪表板的深色背景
  }

  async mounted() {
    try {
      // 动态加载Chart.js
      await this.loadChartJS()
      // 初始化图表
      this.initCharts()
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    } catch (error) {
      console.error('Failed to load Chart.js:', error)
    }
  }

  beforeDestroy() {
    // 清理图表实例
    if (this.lineChart) {
      this.lineChart.destroy()
    }
    if (this.barChart) {
      this.barChart.destroy()
    }
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
  }

  private async loadChartJS() {
    if (typeof window !== 'undefined' && !Chart) {
      // 创建script标签来加载Chart.js
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js'
        script.onload = () => {
          Chart = (window as any).Chart
          resolve(Chart)
        }
        script.onerror = reject
        document.head.appendChild(script)
      })
    }
  }

  private initCharts() {
    if (!Chart) return

    this.initLineChart()
    this.initBarChart()
  }

  // 根据屏幕宽度计算字体大小
  private getResponsiveFontSize(baseSize: number): number {
    const screenWidth = window.innerWidth
    // 基准：1920px屏幕使用baseSize
    // 4K(3840px)屏幕字体放大2倍
    // 较小屏幕相应缩小
    const scaleFactor = Math.max(0.5, Math.min(2.5, screenWidth / 1920))
    // 将字体大小放大3倍
    return Math.round(baseSize * scaleFactor * 3)
  }

  // 处理窗口大小变化
  private handleResize = () => {
    // 防抖处理
    clearTimeout(this.resizeTimeout)
    this.resizeTimeout = setTimeout(() => {
      if (this.lineChart && this.barChart) {
        // 销毁现有图表
        this.lineChart.destroy()
        this.barChart.destroy()
        // 重新初始化图表
        this.initCharts()
      }
    }, 300)
  }

  private resizeTimeout: any = null

  private initLineChart() {
    const ctx = (this.$refs.lineChart as HTMLCanvasElement).getContext('2d')
    if (!ctx) return

    this.lineChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
          label: '空氣質量指數 (AQI)',
          data: [50, 70, 90, 120, 100, 80, 60],
          borderColor: '#00EEFF',
          backgroundColor: 'rgba(0, 238, 255, 0.2)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#00EEFF',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: '#00EEFF'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { 
            labels: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(14)
              }
            } 
          },
          tooltip: { 
            backgroundColor: 'rgba(0, 0, 0, 0.8)', 
            titleColor: '#00EEFF', 
            bodyColor: '#00EEFF',
            titleFont: {
              size: this.getResponsiveFontSize(14)
            },
            bodyFont: {
              size: this.getResponsiveFontSize(12)
            }
          }
        },
        scales: {
          x: { 
            ticks: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(12)
              }
            }, 
            grid: { color: 'rgba(255, 255, 255, 0.1)' } 
          },
          y: { 
            ticks: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(12)
              }
            }, 
            grid: { color: 'rgba(255, 255, 255, 0.1)' } 
          }
        }
      }
    })
  }

  private initBarChart() {
    const ctx = (this.$refs.barChart as HTMLCanvasElement).getContext('2d')
    if (!ctx) return

    this.barChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['PM2.5', 'PM10', 'NO2', 'SO2', 'O3'],
        datasets: [{
          label: '污染物濃度 (μg/m³)',
          data: [35, 50, 40, 20, 60],
          backgroundColor: [
            'rgba(0, 238, 255, 0.6)',
            'rgba(0, 204, 255, 0.6)',
            'rgba(255, 102, 204, 0.6)',
            'rgba(255, 204, 0, 0.6)',
            'rgba(102, 255, 102, 0.6)'
          ],
          borderColor: '#fff',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { 
            labels: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(16)
              }
            } 
          },
          tooltip: { 
            backgroundColor: 'rgba(0, 0, 0, 0.8)', 
            titleColor: '#00EEFF', 
            bodyColor: '#00EEFF',
            titleFont: {
              size: this.getResponsiveFontSize(16)
            },
            bodyFont: {
              size: this.getResponsiveFontSize(16)
            }
          }
        },
        scales: {
          x: { 
            ticks: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(16)
              }
            }, 
            grid: { color: 'rgba(255, 255, 255, 0.1)' } 
          },
          y: { 
            ticks: { 
              color: '#e0e0e0',
              font: {
                size: this.getResponsiveFontSize(14)
              }
            }, 
            grid: { color: 'rgba(255, 255, 255, 0.1)' } 
          }
        }
      }
    })
  }
}
</script>

<style scoped>
.content-area {
  /* Webview滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 减少重绘 */
  contain: layout style paint;
  
  /* 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255,255,255,0.4) rgba(255,255,255,0.1);
}

/* WebKit浏览器滚动条样式 */
.content-area::-webkit-scrollbar {
  width: 12px;
}

.content-area::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 6px;
}

.content-area::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.4);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  min-height: 30px;
}

.content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.6);
  background-clip: content-box;
}

.main-content {
  position: relative;
  width: 100%;
  min-height: 100%;
  padding: 80px 120px;
  box-sizing: border-box;
}

.chart-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 40px;
  padding: 60px;
  margin: 60px auto;
  width: 100%;
  max-width: 1800px;
  box-shadow: 
    0 0 30px rgba(0, 238, 255, 0.3),
    var(--shadow-glass-inset);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(0, 238, 255, 0.2);
}

.chart-title {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  color: #00EEFF;
  font-size: 64px;
  text-align: center;
  margin: 0 0 40px 0;
  text-shadow: 0 0 20px #00EEFF;
}

canvas {
  width: 100% !important;
  height: 800px !important;
}
</style>