// 音效服务
import { Capacitor } from '@capacitor/core'

export class SoundService {
  private static instance: SoundService
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()
  private enabled: boolean = true
  private isInitialized: boolean = false

  private constructor() {
    this.initAudioContext()
  }

  public static getInstance(): SoundService {
    if (!SoundService.instance) {
      SoundService.instance = new SoundService()
    }
    return SoundService.instance
  }

  private initAudioContext() {
    try {
      // 支持不同浏览器的AudioContext
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        this.audioContext = new AudioContextClass()
      }
    } catch (error) {
      console.warn('音频上下文初始化失败:', error)
    }
  }

  // 预加载音效文件
  public async preloadSound(name: string, url: string): Promise<void> {
    if (!this.audioContext) {
      console.warn('音频上下文不可用')
      return
    }

    try {
      const response = await fetch(url)
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
      this.sounds.set(name, audioBuffer)
      console.log(`音效预加载成功: ${name}`)
    } catch (error) {
      console.error(`音效预加载失败 ${name}:`, error)
    }
  }

  // 播放音效
  public playSound(name: string, volume: number = 0.3): boolean {
    if (!this.enabled || !this.audioContext) {
      return false
    }

    const audioBuffer = this.sounds.get(name)
    if (!audioBuffer) {
      console.warn(`音效未找到: ${name}`)
      return false
    }

    try {
      // 确保音频上下文处于运行状态
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()
      
      source.buffer = audioBuffer
      gainNode.gain.value = volume
      
      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      source.start(0)
      return true
    } catch (error) {
      console.error(`播放音效失败 ${name}:`, error)
      return false
    }
  }

  // 简单的按钮点击音效
  public playSimpleClick(volume: number = 0.15): void {
    if (!this.enabled || !this.audioContext) {
      return
    }

    try {
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      // 简单的嘀嗒声
      oscillator.type = 'sine'
      oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime)
      
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.05)
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.05)
    } catch (error) {
      console.error('播放简单音效失败:', error)
    }
  }
  public playButtonClick(volume: number = 0.15): void {
    if (!this.enabled || !this.audioContext) {
      return
    }

    try {
      // 确保音频上下文处于运行状态
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      const filterNode = this.audioContext.createBiquadFilter()
      
      // 移动端优化的音效参数 - 清脆但不刺耳的点击声
      oscillator.type = 'triangle'
      oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(800, this.audioContext.currentTime + 0.08)
      
      // 添加低通滤波器使声音更温和
      filterNode.type = 'lowpass'
      filterNode.frequency.setValueAtTime(2000, this.audioContext.currentTime)
      filterNode.Q.setValueAtTime(1, this.audioContext.currentTime)
      
      // 音量包络 - 更短的音效持续时间适合移动端
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.005)
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.08)
      
      oscillator.connect(filterNode)
      filterNode.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.08)
    } catch (error) {
      console.error('播放按钮音效失败:', error)
      // 备用方案：使用简单的Audio API
      this.playFallbackSound()
    }
  }

  // 备用音效方案（兼容性更好）
  private playFallbackSound(): void {
    try {
      // 创建一个简单的数据URL音效
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.frequency.value = 800
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1)
      
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.1)
    } catch (error) {
      console.warn('备用音效也失败:', error)
    }
  }

  // 启用/禁用音效
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled
    console.log(`音效已${enabled ? '启用' : '禁用'}`)
  }

  public isEnabled(): boolean {
    return this.enabled
  }

  // 初始化用户交互（需要用户手势激活音频上下文）
  public async initUserInteraction(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    if (this.audioContext && this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume()
        this.isInitialized = true
        console.log('音频上下文已激活')
        
        // 在原生平台上，播放一个无声音效来确保音频系统准备就绪
        if (Capacitor.isNativePlatform()) {
          this.warmUpAudio()
        }
      } catch (error) {
        console.error('激活音频上下文失败:', error)
      }
    } else if (this.audioContext) {
      this.isInitialized = true
      if (Capacitor.isNativePlatform()) {
        this.warmUpAudio()
      }
    }
  }

  // 预热音频系统（移动端优化）
  private warmUpAudio(): void {
    try {
      if (!this.audioContext) return
      
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      // 播放一个极短的无声音效
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      oscillator.frequency.setValueAtTime(440, this.audioContext.currentTime)
      
      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.01)
      
      console.log('音频系统预热完成')
    } catch (error) {
      console.warn('音频系统预热失败:', error)
    }
  }

  // 获取音频上下文状态
  public getAudioState(): string {
    return this.audioContext?.state || 'unavailable'
  }
}

// 导出单例实例
export const soundService = SoundService.getInstance()