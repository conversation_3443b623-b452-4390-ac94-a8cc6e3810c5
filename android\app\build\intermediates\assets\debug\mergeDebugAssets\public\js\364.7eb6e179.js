"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[364],{7364:(e,t,i)=>{i.r(t),i.d(t,{default:()=>y});var o=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"worldtime-page responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--worldtime"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.worldTime")))])]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--standard"},[t("div",{staticClass:"worldtime-content"},[t("div",{staticClass:"worldtime-card"},[t("h2",{staticClass:"content-title"},[e._v(e._s(e.$t("worldTime.title")))]),t("div",{staticClass:"features"},[t("h3",{staticClass:"section-title"},[e._v(e._s(e.$t("worldTime.realtimeTitle")))]),t("div",{staticClass:"time-zones"},e._l(e.timezones,function(i){return t("div",{key:i.id,staticClass:"time-zone"},[t("div",{staticClass:"city-name"},[e._v(e._s(i.displayName))]),t("div",{staticClass:"current-time"},[e._v(e._s(e.getTimeForTimezone(i.timezone)))]),t("div",{staticClass:"timezone-info"},[e._v(e._s(i.timezoneInfo))]),t("div",{staticClass:"date-info"},[e._v(e._s(e.getDateForTimezone(i.timezone)))])])}),0)])])])])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}}),t("BottomMarquee")],1)},a=[],n=i(1635),s=i(9603),l=i(3452),r=i(3205),d=i(4184),m=i(256),c=i(5185),g=i(7959);let u=class extends((0,s.Xe)(c.A,g.A)){getBackgroundColor(){return"#E67E22"}timeUpdateInterval=null;get timezones(){return[{id:"hongkong",displayName:this.$t("cities.hongkong"),timezone:"Asia/Hong_Kong",timezoneInfo:"HKT (UTC+8)"},{id:"tokyo",displayName:this.$t("cities.tokyo"),timezone:"Asia/Tokyo",timezoneInfo:"JST (UTC+9)"},{id:"newyork",displayName:this.$t("cities.newyork"),timezone:"America/New_York",timezoneInfo:"EST/EDT (UTC-5/-4)"},{id:"london",displayName:this.$t("cities.london"),timezone:"Europe/London",timezoneInfo:"GMT/BST (UTC+0/+1)"},{id:"paris",displayName:this.$t("cities.paris"),timezone:"Europe/Paris",timezoneInfo:"CET/CEST (UTC+1/+2)"},{id:"sydney",displayName:this.$t("cities.sydney"),timezone:"Australia/Sydney",timezoneInfo:"AEST/AEDT (UTC+10/+11)"},{id:"beijing",displayName:this.$t("cities.beijing"),timezone:"Asia/Shanghai",timezoneInfo:"CST (UTC+8)"},{id:"seoul",displayName:this.$t("cities.seoul"),timezone:"Asia/Seoul",timezoneInfo:"KST (UTC+9)"},{id:"dubai",displayName:this.$t("cities.dubai"),timezone:"Asia/Dubai",timezoneInfo:"GST (UTC+4)"},{id:"losangeles",displayName:this.$t("cities.losangeles"),timezone:"America/Los_Angeles",timezoneInfo:"PST/PDT (UTC-8/-7)"}]}getTimeForTimezone(e){try{const t=new Date,i=t.toLocaleTimeString("zh-TW",{timeZone:e,hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});return i}catch(t){return console.error(`获取时区 ${e} 时间失败:`,t),"--:--:--"}}getDateForTimezone(e){try{const t=new Date,i=t.toLocaleDateString("zh-TW",{timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",weekday:"short"});return i}catch(t){return console.error(`获取时区 ${e} 日期失败:`,t),"----/--/--"}}forceUpdate(){this.$forceUpdate()}mounted(){console.log("WorldTime 组件已挂载，开始时间更新"),this.timeUpdateInterval=setInterval(()=>{this.forceUpdate()},1e3)}beforeDestroy(){this.timeUpdateInterval&&(clearInterval(this.timeUpdateInterval),this.timeUpdateInterval=null,console.log("WorldTime 组件销毁，清理定时器"))}};u=(0,n.Cg)([(0,s.uA)({components:{TopBar:l.A,BottomBar:r.A,BottomMarquee:d.A,BackgroundImage:m.A}})],u);const T=u,p=T;var h=i(1656),C=(0,h.A)(p,o,a,!1,null,"57295ea3",null);const y=C.exports}}]);