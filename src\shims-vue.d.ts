declare module '*.vue' {
  import Vue from 'vue'
  export default Vue
}

// Vue Language Service 类型声明
declare global {
  namespace __VLS_ {
    type IntrinsicElements = any
    type ElementAsFunctionalComponent = any
    type FunctionalComponentArgsRest = any
    type FunctionalComponentProps = any
    type PickFunctionalComponentCtx = any
    type NormalizeEmits = any
  }
  
  const __VLS_intrinsicElements: __VLS_.IntrinsicElements
  const __VLS_elementAsFunctionalComponent: __VLS_.ElementAsFunctionalComponent
  const __VLS_functionalComponentArgsRest: __VLS_.FunctionalComponentArgsRest
  const __VLS_FunctionalComponentProps: __VLS_.FunctionalComponentProps
  const __VLS_pickFunctionalComponentCtx: __VLS_.PickFunctionalComponentCtx
  const __VLS_NormalizeEmits: __VLS_.NormalizeEmits
} 