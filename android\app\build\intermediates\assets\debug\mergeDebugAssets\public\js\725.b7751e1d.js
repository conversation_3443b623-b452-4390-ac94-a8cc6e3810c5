"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[725],{9725:(e,t,s)=>{s.r(t),s.d(t,{default:()=>S});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"ai-chat-page responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--ai"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.aiChat")))])]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--standard"},[0===e.messages.length?t("div",{staticClass:"welcome-container"},[t("div",{staticClass:"welcome-card"},[e._m(0),t("div",{staticClass:"welcome-content"},[t("h2",{staticClass:"welcome-title"},[e._v(e._s(e.$t("aiChat.welcomeTitle")))]),t("p",{staticClass:"welcome-message"},[e._v(e._s(e.$t("aiChat.welcomeMessage")))])])])]):e._e(),t("div",{ref:"chatMessages",staticClass:"chat-messages"},[e._l(e.messages,function(s){return t("div",{key:s.id,staticClass:"message-wrapper",class:s.role},[t("div",{staticClass:"message-bubble"},[t("div",{staticClass:"message-content"},[e._v(e._s(s.content))]),t("div",{staticClass:"message-time"},[e._v(e._s(e.formatTime(s.timestamp)))])])])}),e.isLoading?t("div",{staticClass:"loading-message"},[e._m(1)]):e._e()],2)])]),t("div",{staticClass:"input-area",style:e.inputAreaStyle},[e.isRecording?t("div",{staticClass:"recording-animation-container"},[t("div",{staticClass:"recording-guide-text"},[e._v(" "+e._s(e.$t("aiChat.recordingGuide"))+" ")]),e._m(2)]):e._e(),t("div",{staticClass:"input-container"},[t("div",{staticClass:"input-wrapper"},[t("GlassButton",{staticClass:"voice-button",class:{recording:e.isRecording},attrs:{size:"small",variant:"normal"},on:{mousedown:e.startRecording,mouseup:e.stopRecording,mouseleave:e.cancelRecording,touchstart:function(t){return t.preventDefault(),e.startRecording.apply(null,arguments)},touchend:function(t){return t.preventDefault(),e.stopRecording.apply(null,arguments)},touchcancel:e.cancelRecording}},[e.isRecording?t("div",{staticClass:"voice-animation"},[t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"})]):t("div",{staticClass:"voice-icon"},[t("svg",{attrs:{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor"}},[t("path",{attrs:{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}}),t("path",{attrs:{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}})])])]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.userMessage,expression:"userMessage"}],ref:"messageInput",staticClass:"message-input",attrs:{type:"text",placeholder:e.$t("aiChat.inputPlaceholder"),disabled:e.isLoading,contenteditable:"true"},domProps:{value:e.userMessage},on:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.sendMessage.apply(null,arguments)},input:[function(t){t.target.composing||(e.userMessage=t.target.value)},e.onInputChange],focus:e.onInputFocus,blur:e.onInputBlur,paste:e.onPaste}}),t("GlassButton",{staticClass:"send-button",class:{disabled:!e.canSend},attrs:{size:"small",variant:"highlight"},on:{click:e.sendMessage}},[t("div",{staticClass:"send-icon"},[t("svg",{attrs:{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor"}},[t("path",{attrs:{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"}})])])])],1),e.isListening?t("div",{staticClass:"voice-status"},[e._m(3),t("span",{staticClass:"voice-text"},[e._v(e._s(e.$t("aiChat.listening")))])]):e._e()])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}}),t("BottomMarquee")],1)},i=[function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"winnie-avatar"},[t("div",{staticClass:"avatar-circle"},[t("span",{staticClass:"avatar-text"},[e._v("🤖")])])])},function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"loading-bubble"},[t("div",{staticClass:"typing-indicator"},[t("span"),t("span"),t("span")])])},function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"recording-box"},[t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"}),t("div",{staticClass:"sound-wave"})])},function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"voice-indicator"},[t("div",{staticClass:"voice-wave"}),t("div",{staticClass:"voice-wave"}),t("div",{staticClass:"voice-wave"})])}],o=s(1635),n=s(9603),r=s(3452),c=s(3205),l=s(4184),d=s(256),u=s(7237),h=s(5185),g=s(7959),m=s(5719);const p={NODE_ENV:"production",VUE_APP_PERFORMANCE_MODE:"optimized",BASE_URL:""}.VUE_APP_GEMINI_API_KEY||"AIzaSyBptBAXOzf42Q-jMg4ezWBam8ScrNlBBwQ",v=new m.ij(p),f="\n你是Winnie，一个位于商场的智能AI客服助手。你的主要职责是：\n\n1. 友好地欢迎顾客，提供热情周到的服务\n2. 帮助顾客了解商场内的商店、设施、服务等信息\n3. 回答关于商场导航、营业时间、促销活动等问题\n4. 提供实用的购物建议和推荐\n5. 用简洁明了的语言与顾客交流\n\n请始终保持专业、友好和乐于助人的态度。如果遇到不确定的问题，请礼貌地表示会为顾客寻找相关信息。\n\n对话语言：请根据用户的输入语言或语音的语种进行回复。\n";async function w(e,t=[]){try{const s=v.getGenerativeModel({model:"gemini-2.5-flash"});let a;if("string"===typeof e)a=[{text:e}];else{const t=new Uint8Array(e.audioData);let s="";for(let e=0;e<t.byteLength;e++)s+=String.fromCharCode(t[e]);const i=btoa(s);a=[{text:"请听取以下音频内容。首先，请在你的回复开头用这样的格式标注语种：[LANGUAGE:语种代码]（例如：[LANGUAGE:zh-CN]表示中文，[LANGUAGE:en]表示英文，[LANGUAGE:es]表示西班牙语）。然后换行，给出对音频内容的恰当回复："},{inlineData:{mimeType:e.mimeType,data:i}}]}const i=[{role:"system",content:f},...t],o=i.filter(e=>"system"!==e.role).map(e=>({role:"assistant"===e.role?"model":"user",parts:[{text:e.content}]}));o.push({role:"user",parts:a}),o.length>0&&("string"!==typeof e&&o[0].parts.length>1?o[0].parts.unshift({text:f+"\n\n"}):o[0].parts[0].text&&(o[0].parts[0].text=f+"\n\n"+o[0].parts[0].text));const n=await s.generateContent({contents:o,generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:2048}}),r=n.response,c=r.text();if(!c)throw new Error("没有收到AI回复");return c.trim()}catch(s){return console.error("Gemini API 调用失败:",s),"抱歉，我现在无法回复您的消息。请稍后再试，或者联系商场工作人员寻求帮助。"}}var y=s(7666);const C=new y.Ay({NODE_ENV:"production",VUE_APP_PERFORMANCE_MODE:"optimized",BASE_URL:""}.VUE_APP_POCKETBASE_URL||"https://base.bwaiwork.xyz"),b="ai_sessions_byemap",_="ai_conversations_byemap";async function A(e,t){try{const s={title:e,summary:t||"",_byemap:JSON.stringify({source:"ai_chat",timestamp:(new Date).toISOString(),device:"kiosk"})},a=await C.collection(b).create(s);return a}catch(s){throw console.error("创建会话失败:",s),s}}async function R(e,t,s,a="gemini-2.0-flash-exp",i){try{const o={session_id:e,role:t,content:s,model:a,metadata:i||{},_byemap:JSON.stringify({source:"ai_chat",timestamp:(new Date).toISOString(),device:"kiosk",character_count:s.length})},n=await C.collection(_).create(o);return n}catch(o){if(o instanceof Error&&"AbortError"===o.name)return console.log("保存消息请求被中止（可能是页面切换）"),{};throw console.error("保存消息失败:",o),o}}async function k(e,t,s){try{const a=s||L(e),i=await A(a,B(e,t)),o=i.id;return await R(o,"user",e),await R(o,"assistant",t),o}catch(a){if(a instanceof Error&&"AbortError"===a.name)return console.log("保存对话请求被中止（可能是页面切换）"),"";throw console.error("保存对话失败:",a),a}}function L(e){const t=e.length>30?e.substring(0,30)+"...":e;return t||"新对话"}function B(e,t){return`用户询问: ${e.substring(0,50)}${e.length>50?"...":""}`}var T=s(6546),E=s(4214);let M=class extends((0,n.Xe)(h.A,g.A)){getBackgroundColor(){return"#016513"}userMessage="";messages=[];isLoading=!1;isListening=!1;isRecording=!1;recordingStartTime=0;minRecordingDuration=300;sessionId=null;recognition=null;abortController=null;mediaRecorder=null;audioChunks=[];isInputFocused=!1;inputKeyboardOffset=0;get canSend(){return this.userMessage.trim().length>0&&!this.isLoading}get inputAreaStyle(){const e=530,t=this.isInputFocused?e+this.inputKeyboardOffset:e;return{position:"absolute",bottom:`${t}px`,left:"50%",transform:"translateX(-50%)",width:"1400px",zIndex:20,transition:"bottom 0.3s ease"}}async mounted(){try{await this.initAudioRecording()}catch(e){console.error("初始化音频录制失败:",e)}this.scrollToBottom()}async initAudioRecording(){if(T.Ii.isNativePlatform())try{const e=await E.R.hasAudioRecordingPermission();if(!e){const e=await E.R.requestAudioRecordingPermission();if(!e||!e.value)return console.error("用户拒绝了麦克风权限"),void alert("请在设置中允许麦克风权限以使用语音功能")}console.log("原生音频录制权限已获得")}catch(e){console.error("请求音频权限失败:",e)}else try{const e=await navigator.mediaDevices.getUserMedia({audio:!0});this.mediaRecorder=new MediaRecorder(e),this.mediaRecorder.ondataavailable=e=>{e.data.size>0&&this.audioChunks.push(e.data)},this.mediaRecorder.onstop=async()=>{if(this.audioChunks.length>0){const e=new Blob(this.audioChunks,{type:"audio/webm"});await this.sendAudioMessage(e)}this.audioChunks=[]}}catch(e){console.error("无法访问麦克风:",e),alert("请允许访问麦克风以使用语音功能")}}getSpeechLanguage(){const e={"zh-TW":"zh-TW",en:"en-US",es:"es-ES"};return e[this.$currentLanguage]||"zh-TW"}async sendMessage(){if(!this.canSend)return;const e={id:Date.now(),role:"user",content:this.userMessage.trim(),timestamp:new Date};this.messages.push(e);const t=this.userMessage.trim();this.userMessage="",this.isLoading=!0;try{await this.detectAndSwitchLanguage(t);const e=this.messages.slice(0,-1).map(e=>({role:e.role,content:e.content})),s=await w(t,e),a={id:Date.now()+1,role:"assistant",content:s,timestamp:new Date};this.messages.push(a),await this.saveConversationToPocketBase(t,s)}catch(s){console.error("Error calling AI:",s);const e={id:Date.now()+1,role:"assistant",content:this.$t("aiChat.error"),timestamp:new Date};this.messages.push(e)}finally{this.isLoading=!1,this.scrollToBottom()}}async startRecording(){if(!this.isRecording){this.isRecording=!0,this.recordingStartTime=Date.now();try{if(T.Ii.isNativePlatform())await E.R.startRecording();else{if(!this.mediaRecorder&&(await this.initAudioRecording(),!this.mediaRecorder))return alert("无法访问麦克风，请检查权限设置"),void(this.isRecording=!1);this.audioChunks=[],this.mediaRecorder.start()}}catch(e){console.error("开始录音失败:",e),this.isRecording=!1,alert("无法开始录音，请检查麦克风权限")}}}async stopRecording(){if(!this.isRecording)return;const e=Date.now()-this.recordingStartTime;if(e<this.minRecordingDuration)this.cancelRecording();else{this.isRecording=!1;try{if(T.Ii.isNativePlatform()){const e=await E.R.stopRecording();if(e.value&&e.value.recordDataBase64){const t=e.value.recordDataBase64,s=atob(t),a=new Array(s.length);for(let e=0;e<s.length;e++)a[e]=s.charCodeAt(e);const i=new Uint8Array(a),o=new Blob([i],{type:e.value.mimeType||"audio/webm"});await this.sendAudioMessage(o)}}else this.mediaRecorder&&"recording"===this.mediaRecorder.state&&this.mediaRecorder.stop()}catch(t){console.error("停止录音失败:",t)}}}async cancelRecording(){if(this.isRecording){this.isRecording=!1;try{T.Ii.isNativePlatform()?await E.R.stopRecording():this.mediaRecorder&&"recording"===this.mediaRecorder.state&&(this.audioChunks=[],this.mediaRecorder.stop())}catch(e){console.error("取消录音失败:",e)}}}async toggleVoiceInput(){if(T.Ii.isNativePlatform())if(this.isListening)try{const e=await E.R.stopRecording();if(this.isListening=!1,e.value&&e.value.recordDataBase64){const t=e.value.recordDataBase64,s=atob(t),a=new Array(s.length);for(let e=0;e<s.length;e++)a[e]=s.charCodeAt(e);const i=new Uint8Array(a),o=new Blob([i],{type:e.value.mimeType||"audio/webm"});await this.sendAudioMessage(o)}}catch(e){console.error("停止录音失败:",e),this.isListening=!1}else try{await E.R.startRecording(),this.isListening=!0}catch(e){console.error("开始录音失败:",e),alert("无法开始录音，请检查麦克风权限")}else{if(!this.mediaRecorder)return void alert("无法访问麦克风，请检查权限设置");this.isListening?(this.mediaRecorder.stop(),this.isListening=!1):(this.audioChunks=[],this.mediaRecorder.start(),this.isListening=!0)}}async sendAudioMessage(e){this.isLoading=!0;try{const t={id:Date.now(),role:"user",content:`🎤 ${this.$t("aiChat.voiceMessage")}`,timestamp:new Date};this.messages.push(t);const s=this.messages.slice(0,-1).map(e=>({role:e.role,content:e.content})),a=await e.arrayBuffer(),i=await w({audioData:a,mimeType:"audio/webm"},s),o=i.match(/\[LANGUAGE:([^\]]+)\]/);if(o){const e=o[1];console.log("🌍 检测到语种:",e),await this.mapAndSetGlobalLanguage(e);const t=i.replace(/\[LANGUAGE:[^\]]+\]\s*\n?/,""),s={id:Date.now()+1,role:"assistant",content:t,timestamp:new Date};this.messages.push(s),await this.saveConversationToPocketBase(`🎤 ${this.$t("aiChat.voiceMessage")}`,t)}else{console.log("⚠️ 未能识别语种");const e={id:Date.now()+1,role:"assistant",content:i,timestamp:new Date};this.messages.push(e),await this.saveConversationToPocketBase(`🎤 ${this.$t("aiChat.voiceMessage")}`,i)}}catch(t){console.error("Error processing audio:",t);const e={id:Date.now()+1,role:"assistant",content:this.$t("aiChat.error"),timestamp:new Date};this.messages.push(e)}finally{this.isLoading=!1,this.scrollToBottom()}}onInputChange(){}onInputFocus(){this.isInputFocused=!0,this.adjustForKeyboard()}onInputBlur(){this.isInputFocused=!1,this.inputKeyboardOffset=0}onPaste(e){console.log("粘贴内容到输入框")}adjustForKeyboard(){const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);if(e){const e=window.innerHeight,t=()=>{const t=window.innerHeight,s=e-t;this.inputKeyboardOffset=s>150?Math.max(0,s-100):0};window.addEventListener("resize",t),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("resize",t)})}}async saveConversationToPocketBase(e,t){try{this.sessionId?(await R(this.sessionId,"user",e),await R(this.sessionId,"assistant",t)):this.sessionId=await k(e,t)}catch(s){if(s instanceof Error&&"AbortError"===s.name)return void console.log("保存对话请求被取消（可能是页面切换）");console.error("保存对话失败:",s)}}formatTime(e){return e.toLocaleTimeString(this.$currentLanguage,{hour:"2-digit",minute:"2-digit"})}scrollToBottom(){this.$nextTick(()=>{const e=document.querySelector(".scrollable-content-area");e&&(e.scrollTop=e.scrollHeight)})}resetChat(){this.messages=[],this.sessionId=null,this.userMessage="",this.isLoading=!1,this.isListening=!1}async detectAndSwitchLanguage(e){try{const t=this.detectTextLanguage(e);t&&(console.log(`🔍 检测到文字语言: ${t}`),await this.mapAndSetGlobalLanguage(t))}catch(t){console.error("检测文字语言失败:",t)}}detectTextLanguage(e){const t=e.trim();if(t.length<2)return null;const s=/[\u4e00-\u9fff]/;if(s.test(t))return"zh";const a=/[\u3040-\u309f\u30a0-\u30ff]/;if(a.test(t))return"ja";const i=/[\uac00-\ud7af]/;if(i.test(t))return"ko";const o=/[\u0e00-\u0e7f]/;if(o.test(t))return"th";const n=/[ñáéíóúü]/i,r=/\b(hola|gracias|por favor|buenos días|buenas tardes|cómo|qué|dónde|cuándo|español|sí|no|muy|bien|mal|grande|pequeño|casa|tiempo|agua|comida|trabajo|familia|amigo|ciudad|país|mundo|vida|año|día|hora|minuto|segundo|dinero|persona|gente|mujer|hombre|niño|niña|padre|madre|hijo|hija|hermano|hermana|escuela|universidad|médico|hospital|restaurante|hotel|aeropuerto|estación|banco|tienda|mercado|iglesia|museo|parque|playa|montaña|río|lago|mar|océano|cielo|sol|luna|estrella|nube|lluvia|viento|calor|frío|primavera|verano|otoño|invierno|lunes|martes|miércoles|jueves|viernes|sábado|domingo|enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)\b/i;if(n.test(t)||r.test(t))return"es";const c=/\b(hello|hi|thank you|thanks|please|good morning|good afternoon|good evening|how|what|where|when|why|who|english|yes|no|very|good|bad|big|small|house|time|water|food|work|family|friend|city|country|world|life|year|day|hour|minute|second|money|person|people|woman|man|child|boy|girl|father|mother|son|daughter|brother|sister|school|university|doctor|hospital|restaurant|hotel|airport|station|bank|store|shop|market|church|museum|park|beach|mountain|river|lake|sea|ocean|sky|sun|moon|star|cloud|rain|wind|hot|cold|spring|summer|autumn|winter|monday|tuesday|wednesday|thursday|friday|saturday|sunday|january|february|march|april|may|june|july|august|september|october|november|december)\b/i;return c.test(t)?"en":null}async mapAndSetGlobalLanguage(e){try{const t={zh:"zh-TW","zh-CN":"zh-TW","zh-TW":"zh-TW","zh-HK":"zh-TW",chinese:"zh-TW",mandarin:"zh-TW",cantonese:"zh-TW",en:"en","en-US":"en","en-GB":"en",english:"en",es:"es","es-ES":"es","es-MX":"es",spanish:"es",español:"es",ja:"ja","ja-JP":"ja",japanese:"ja",日本語:"ja",ko:"ko","ko-KR":"ko",korean:"ko",한국어:"ko",th:"th","th-TH":"th",thai:"th",ภาษาไทย:"th"},s=e.toLowerCase().trim();let a=t[s];if(!a)for(const[e,i]of Object.entries(t))if(s.includes(e)||e.includes(s)){a=i;break}if(a&&this.$supportedLanguages.some(e=>e.code===a)){const e=this.$currentLanguage;if(e!==a){console.log(`🔄 语言切换: ${e} -> ${a}`),this.$setLanguage(a),await this.$nextTick();const t=this.$getLanguageConfig(a);t&&setTimeout(()=>{console.log(`✅ 已自动切换语言为: ${t.nativeName}`)},100)}else console.log(`📌 检测到当前语言 ${a}，无需切换`)}else console.warn(`⚠️ 无法映射语种 "${e}" 到支持的语言`)}catch(t){console.error("设置全局语言失败:",t)}}created(){this.resetChat()}beforeDestroy(){this.abortController&&(this.abortController.abort(),this.abortController=null),this.recognition&&this.isListening&&this.recognition.stop(),console.log("AIChat组件已销毁，清理完成")}};M=(0,o.Cg)([(0,n.uA)({components:{TopBar:r.A,BottomBar:c.A,BottomMarquee:l.A,BackgroundImage:d.A,GlassButton:u.A}})],M);const I=M,P=I;var x=s(1656),D=(0,x.A)(P,a,i,!1,null,"66543e37",null);const S=D.exports}}]);