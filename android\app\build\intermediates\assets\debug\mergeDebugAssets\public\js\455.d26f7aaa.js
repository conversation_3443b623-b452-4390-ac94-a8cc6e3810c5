"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[455],{6455:(t,a,e)=>{e.r(a),e.d(a,{default:()=>g});var s=function(){var t=this,a=t._self._c;t._self._setupProxy;return a("div",{staticClass:"about-page responsive-page-container",style:t.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--about"},[a("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.about")))])]),a("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--standard"},[a("div",{staticClass:"about-content"},[a("div",{staticClass:"about-card"},[a("h2",{staticClass:"content-title"},[t._v(t._s(t.$t("about.title")))]),a("div",{staticClass:"tech-stack"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.techStack")))]),a("ul",{staticClass:"tech-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.techStack.vue")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.typescript")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.tailwind")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.capacitor")))])])]),a("div",{staticClass:"features"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.features")))]),a("ul",{staticClass:"feature-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.features.smartNavigation")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.realtimeLocation")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.multiLanguage")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.crossPlatform")))])])]),a("div",{staticClass:"extra-sections"},[a("div",{staticClass:"version-info"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.version")))]),a("ul",{staticClass:"version-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.version.current")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.releaseDate")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.updateFrequency")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.supportedPlatforms")))])])]),a("div",{staticClass:"team-info"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.team")))]),a("ul",{staticClass:"team-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.team.frontend")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.mobile")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.design")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.data")))])])])])])])])]),a("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),a("BottomMarquee")],1)},i=[],l=e(1635),o=e(9603),c=e(3452),n=e(3205),u=e(4184),r=e(256),_=e(5185),v=e(7959);let d=class extends((0,o.Xe)(_.A,v.A)){getBackgroundColor(){return"#34495E"}};d=(0,l.Cg)([(0,o.uA)({components:{TopBar:c.A,BottomBar:n.A,BottomMarquee:u.A,BackgroundImage:r.A}})],d);const b=d,C=b;var m=e(1656),p=(0,m.A)(C,s,i,!1,null,"54853cfa",null);const g=p.exports}}]);