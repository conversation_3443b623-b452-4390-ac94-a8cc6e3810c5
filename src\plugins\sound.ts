// 全局音效指令（优化版）
import Vue, { VueConstructor, DirectiveOptions } from 'vue'
import { soundService } from '@/services/audioManager'

interface SoundDirectiveBinding {
  value?: {
    enabled?: boolean
    volume?: number
    soundName?: string
  } | boolean
}

const clickSoundDirective: DirectiveOptions = {
  bind(el: HTMLElement, binding: SoundDirectiveBinding) {
    // 解析指令参数
    let config = {
      enabled: true,
      volume: 0.4,
      soundName: 'buttonClick'
    }

    if (typeof binding.value === 'boolean') {
      config.enabled = binding.value
    } else if (binding.value && typeof binding.value === 'object') {
      config = { ...config, ...binding.value }
    }

    // 保存原始的点击处理器
    const originalClick = (el as any)._originalClick || null

    // 创建新的点击处理器
    const clickHandler = (event: Event) => {
      // 播放音效（优化版）
      if (config.enabled) {
        try {
          soundService.playButtonClick(config.volume)
        } catch (error) {
          // 静默处理错误
        }
      }

      // 执行原始处理器
      if (originalClick) {
        originalClick.call(el, event)
      }
    }

    // 保存处理器引用以便后续更新/解绑
    ;(el as any)._soundClickHandler = clickHandler
    ;(el as any)._soundConfig = config

    // 绑定点击事件
    el.addEventListener('click', clickHandler)
  },

  update(el: HTMLElement, binding: SoundDirectiveBinding) {
    // 更新配置
    let newConfig = {
      enabled: true,
      volume: 0.4,
      soundName: 'buttonClick'
    }

    if (typeof binding.value === 'boolean') {
      newConfig.enabled = binding.value
    } else if (binding.value && typeof binding.value === 'object') {
      newConfig = { ...newConfig, ...binding.value }
    }

    // 保存新配置
    ;(el as any)._soundConfig = newConfig
  },

  unbind(el: HTMLElement) {
    // 移除事件监听器
    const handler = (el as any)._soundClickHandler
    if (handler) {
      el.removeEventListener('click', handler)
      delete (el as any)._soundClickHandler
      delete (el as any)._soundConfig
      delete (el as any)._originalClick
    }
  }
}

// 安装插件
const SoundPlugin = {
  install(Vue: VueConstructor) {
    // 注册全局指令
    Vue.directive('click-sound', clickSoundDirective)

    // 添加全局方法 - 优化版本
    Vue.prototype.playClickSound = function(volume: number = 0.4, soundName: string = 'buttonClick') {
      try {
        soundService.playButtonClick(volume)
      } catch (error) {
        // 静默处理错误
      }
    }

    Vue.prototype.setSoundEnabled = function(enabled: boolean) {
      soundService.setEnabled(enabled)
    }

    Vue.prototype.isSoundEnabled = function() {
      return soundService.isEnabled()
    }
  }
}

export default SoundPlugin
export { clickSoundDirective }