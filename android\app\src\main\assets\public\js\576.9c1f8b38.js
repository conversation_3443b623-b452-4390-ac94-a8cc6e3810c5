"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[576],{150:(e,t,a)=>{a.d(t,{A:()=>p});var s=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{class:e.cardClasses,on:{click:function(t){return e.$emit("click")}}},[e.showIcon?t("div",{class:e.iconContainerClasses},[e.iconSrc?t("img",{class:e.iconImageClasses,attrs:{src:e.iconSrc,alt:e.title}}):e.showPlaceholder?t("div",{class:e.placeholderClasses},[e._v(" "+e._s(e.placeholderText)+" ")]):e._e()]):e._e(),e.showContent?t("div",{class:e.contentClasses},[e._t("default",function(){return[e.title?t("div",{class:e.titleClasses},[e._v(" "+e._s(e.title)+" ")]):e._e(),e.subtitle?t("div",{class:e.subtitleClasses},[e._v(" "+e._s(e.subtitle)+" ")]):e._e()]})],2):e._e(),e._t("custom")],2)},o=[],i=a(635),n=a(233);let l=class extends n.lD{title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const e=["card-base","text-primary"],t={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},a={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},s={vertical:"card-vertical",horizontal:"card-horizontal"};return[...e,t[this.size],a[this.variant],s[this.layout]]}get iconContainerClasses(){const e=["icon-container"],t={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...e,t[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const e=["icon-placeholder"],t={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...e,t[this.size]]}get contentClasses(){const e=["card-content"],t={vertical:"content-vertical",horizontal:"content-horizontal"};return[...e,t[this.layout]]}get titleClasses(){const e=["card-title","text-primary"],t={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},a={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...e,t[this.size],a[this.variant]]}get subtitleClasses(){const e=["card-subtitle","text-primary"],t={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...e,t[this.size]]}};(0,i.Cg)([(0,n.kv)({required:!0})],l.prototype,"title",void 0),(0,i.Cg)([(0,n.kv)()],l.prototype,"subtitle",void 0),(0,i.Cg)([(0,n.kv)()],l.prototype,"iconSrc",void 0),(0,i.Cg)([(0,n.kv)({default:"medium"})],l.prototype,"size",void 0),(0,i.Cg)([(0,n.kv)({default:"facility"})],l.prototype,"variant",void 0),(0,i.Cg)([(0,n.kv)({default:"vertical"})],l.prototype,"layout",void 0),(0,i.Cg)([(0,n.kv)({default:!0})],l.prototype,"showIcon",void 0),(0,i.Cg)([(0,n.kv)({default:!0})],l.prototype,"showContent",void 0),(0,i.Cg)([(0,n.kv)({default:!0})],l.prototype,"showPlaceholder",void 0),l=(0,i.Cg)([n.uA],l);const r=l,c=r;var d=a(656),u=(0,d.A)(c,s,o,!1,null,"022da4db",null);const p=u.exports},576:(e,t,a)=>{a.r(t),a.d(t,{default:()=>C});var s=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"shop-container responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"main-title"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.shop")))])]),t("div",{staticClass:"content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content"},[t("div",{staticClass:"shop-grid"},[e._l(e.shops,function(a){return t("ShopCard",{key:a.id,attrs:{"shop-name":a.name,"logo-src":a.logo},on:{click:function(t){return e.navigateToShop(a.id)}}})}),e._l(e.extraShops,function(a){return t("ShopCard",{key:`extra-${a.id}`,attrs:{"shop-name":a.name,"logo-src":a.logo},on:{click:function(t){return e.navigateToShop(a.id)}}})})],2)])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}})],1)},o=[],i=a(635),n=a(233),l=a(710),r=a(14),c=a(958),d=a(256),u=a(185),p=a(959),g=a(713);let m=class extends((0,n.Xe)(u.A,p.A)){getBackgroundColor(){return"#0F04A9"}get shops(){return(0,g.aF)()}extraShops=Array.from({length:4},()=>(0,g.aF)()).flat();navigateToShop(e){console.log("导航到商店:",e),this.$router.push(`/shop/${e}`)}};m=(0,i.Cg)([(0,n.uA)({components:{ShopCard:l.A,TopBar:r.A,BottomBar:c.A,BackgroundImage:d.A}})],m);const h=m,f=h;var v=a(656),y=(0,v.A)(f,s,o,!1,null,"7edd416f",null);const C=y.exports},710:(e,t,a)=>{a.d(t,{A:()=>g});var s=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("BaseCard",{attrs:{title:e.shopName,"icon-src":e.logoSrc,size:"large",variant:"shop",layout:"vertical"},on:{click:function(t){return e.$emit("click")}}})},o=[],i=a(635),n=a(233),l=a(150);let r=class extends n.lD{shopName;logoSrc};(0,i.Cg)([(0,n.kv)({required:!0})],r.prototype,"shopName",void 0),(0,i.Cg)([(0,n.kv)({default:""})],r.prototype,"logoSrc",void 0),r=(0,i.Cg)([(0,n.uA)({components:{BaseCard:l.A}})],r);const c=r,d=c;var u=a(656),p=(0,u.A)(d,s,o,!1,null,null,null);const g=p.exports},713:(e,t,a)=>{a.d(t,{aF:()=>i,kv:()=>n,z:()=>o});const s={"7-eleven":{id:"7-eleven",name:"7-Eleven",location:"G028-G029",hours:"10:00 - 22:00 (Closed)",description:"Convenient, 24/7, quick snacks, essentials, drinks, grab-and-go, bright signage, busy, affordable, compact, diverse products, coffee, chilled treats, ATM, friendly staff, always open, efficient, popular, handy, lifesaver.",logo:"/img/shops/7-eleven.png"},"din-tai-fung":{id:"din-tai-fung",name:"Din Tai Fung",location:"G030-G031",hours:"11:00 - 21:30",description:"Famous for xiaolongbao (soup dumplings), authentic Taiwanese cuisine, fresh ingredients, handmade dumplings, steamed buns, noodles, premium dining experience, quality service.",logo:"/img/shops/din-tai-fung.png"},amoment:{id:"amoment",name:"Amoment",location:"G032-G033",hours:"10:00 - 22:00",description:"Modern lifestyle store, trendy accessories, home decor, unique gifts, contemporary design, quality products, Instagram-worthy items, urban lifestyle.",logo:"/img/shops/amoment.png"},starbucks:{id:"starbucks",name:"Starbucks",location:"G034-G035",hours:"07:00 - 23:00",description:"Premium coffee chain, espresso drinks, frappuccinos, pastries, cozy atmosphere, free WiFi, meeting spot, consistent quality, global brand.",logo:"/img/shops/starbucks.png"},"law-mark-kee":{id:"law-mark-kee",name:"Law Mark Kee",location:"G036-G037",hours:"08:00 - 20:00",description:"Traditional Hong Kong style restaurant, dim sum, wonton noodles, congee, cha chaan teng classics, authentic flavors, local favorites.",logo:"/img/shops/law-mark-kee.png"},"haagen-dazs":{id:"haagen-dazs",name:"Haagen-Dazs",location:"G038-G039",hours:"10:00 - 22:00",description:"Premium ice cream brand, luxury frozen desserts, rich flavors, creamy texture, sundaes, milkshakes, indulgent treats, high quality ingredients.",logo:"/img/shops/haagen-dazs.png"},catalo:{id:"catalo",name:"Catalo",location:"G040-G041",hours:"10:00 - 21:00",description:"Health and wellness store, nutritional supplements, vitamins, natural products, healthcare solutions, wellness consultation, trusted brand.",logo:"/img/shops/catalo.png"},"italian-tomato":{id:"italian-tomato",name:"Italian Tomato",location:"G042-G043",hours:"09:00 - 22:00",description:"Italian cuisine, fresh pasta, pizza, authentic flavors, casual dining, family-friendly, Mediterranean atmosphere, quality ingredients.",logo:"/img/shops/italian-tomato.png"},smarton:{id:"smarton",name:"Smarton",location:"G044-G045",hours:"10:00 - 21:00",description:"Electronics and technology store, smartphones, gadgets, accessories, latest tech trends, competitive prices, technical support.",logo:"/img/shops/smarton.png"},"paul-joe":{id:"paul-joe",name:"Paul & Joe",location:"G046-G047",hours:"10:00 - 21:00",description:"French fashion and cosmetics brand, chic clothing, makeup, skincare, Parisian style, feminine designs, quality fashion accessories.",logo:"/img/shops/paul-joe.png"},buglls:{id:"buglls",name:"Buglls",location:"G048-G049",hours:"10:00 - 21:00",description:"Trendy fashion store, contemporary clothing, stylish accessories, modern designs, urban fashion, quality apparel, fashion-forward styles.",logo:"/img/shops/buglls.png"},select:{id:"select",name:"Select",location:"G050-G051",hours:"10:00 - 21:00",description:"Curated lifestyle store, premium products, unique selections, quality items, modern lifestyle, exclusive brands, sophisticated choices.",logo:"/img/shops/select.png"}},o=e=>s[e]||null,i=()=>Object.values(s).map(e=>({id:e.id,name:e.name,logo:e.logo})),n=e=>e in s}}]);