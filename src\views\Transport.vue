<template>
  <div class="transport-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Transport主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--transport">
      <h1 class="app-title">{{ $t('pageTitle.transport') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 交通信息图片 -->
        <div class="transport-image-container">
          <img 
            :src="'/img/transports/img.png'" 
            alt="Transport Information" 
            class="transport-image"
          />
        </div>

        <!-- 交通方式按钮组 -->
        <div class="transport-buttons">
          <TransportButton
            v-for="transport in transportOptions"
            :key="transport.id"
            :transport-id="transport.id"
            :transport-name="transport.name"
            :icon-src="transport.icon"
            :is-selected="selectedTransport === transport.id"
            @transport-selected="selectTransport"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import TransportButton from '@/components/TransportButton.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

interface TransportItem {
  id: string
  name: string
  icon: string
  type: string
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    TransportButton,
    BackgroundImage
  }
})
export default class Transport extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#1E90FF' // Transport页面的蓝色背景
  }

  selectedTransport: string = 'mtr'
  
  transportOptions: TransportItem[] = [
    {
      id: 'mtr',
      name: 'MTR',
      icon: '/img/transports/mtr.svg',
      type: 'transport'
    },
    {
      id: 'light-rail',
      name: 'Light Rail',
      icon: '/img/transports/light-rail.svg',
      type: 'transport'
    },
    {
      id: 'bus',
      name: 'Bus', 
      icon: '/img/transports/bus.svg',
      type: 'transport'
    },
    {
      id: 'mini-bus',
      name: 'Mini Bus',
      icon: '/img/transports/mini-bus.svg',
      type: 'transport'
    },
    {
      id: 'map',
      name: 'Map',
      icon: '/img/transports/map.svg',
      type: 'map'
    }
  ]
  
  selectTransport(transportId: string) {
    this.selectedTransport = transportId
    console.log('Selected transport:', transportId)
    // 这里可以添加逻辑来切换显示不同交通方式的信息
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
/* 交通信息图片 */
.transport-image-container {
  width: 100%;
  margin: 0 auto 60px;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease;
}

.transport-image-container:hover {
  transform: scale(1.02);
  filter: drop-shadow(0px 8px 16px rgba(0, 0, 0, 0.3));
}

.transport-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 20px;
  transition: all 0.3s ease;
}

/* 交通方式按钮组 */
.transport-buttons {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 40px;
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
  justify-items: center;
}
</style> 