<template>
  <div 
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 图标区域 -->
    <div 
      v-if="showIcon"
      :class="iconContainerClasses"
    >
      <img 
        v-if="iconSrc" 
        :src="iconSrc" 
        :alt="title" 
        :class="iconImageClasses"
      />
      <div 
        v-else-if="showPlaceholder" 
        :class="placeholderClasses"
      >
        {{ placeholderText }}
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div 
      v-if="showContent"
      :class="contentClasses"
    >
      <slot>
        <!-- 默认内容：标题 -->
        <MarqueeText 
          v-if="title && enableMarquee"
          :text="title"
          :container-class="titleContainerClass"
          :text-class="marqueeTextClasses.join(' ')"
          :container-style="titleContainerStyle"
          :text-style="titleTextStyle"
          :speed="marqueeSpeed"
          :delay="marqueeDelay"
          :animation-type="marqueeType"
        />
        <div 
          v-else-if="title"
          :class="titleClasses"
        >
          {{ title }}
        </div>
        
        <!-- 子标题 -->
        <div 
          v-if="subtitle"
          :class="subtitleClasses"
        >
          {{ subtitle }}
        </div>
      </slot>
    </div>
    
    <!-- 自定义内容插槽 -->
    <slot name="custom"></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Mixins } from 'vue-property-decorator'
import SoundMixin from '@/mixins/SoundMixin'
import MarqueeText from './MarqueeText.vue'

export type CardSize = 'small' | 'medium' | 'large' | 'extra-large'
export type CardVariant = 'facility' | 'shop' | 'office' | 'transport'
export type CardLayout = 'vertical' | 'horizontal'

@Component({
  components: {
    MarqueeText
  }
})
export default class BaseCard extends Mixins(SoundMixin) {
  @Prop({ required: true })
  title!: string
  
  @Prop()
  subtitle?: string
  
  @Prop()
  iconSrc?: string
  
  @Prop({ default: 'medium' })
  size!: CardSize
  
  @Prop({ default: 'facility' })
  variant!: CardVariant
  
  @Prop({ default: 'vertical' })
  layout!: CardLayout
  
  @Prop({ default: true })
  showIcon!: boolean
  
  @Prop({ default: true })
  showContent!: boolean
  
  @Prop({ default: true })
  showPlaceholder!: boolean

  @Prop({ default: true })
  enableSound!: boolean

  @Prop({ default: false })
  enableMarquee!: boolean

  @Prop({ default: 3 })
  marqueeSpeed!: number

  @Prop({ default: 1 })
  marqueeDelay!: number

  @Prop({ default: 'bounce' })
  marqueeType!: 'linear' | 'bounce'

  private handleClick(event: Event): void {
    // 播放音效
    if (this.enableSound) {
      this.playClickSound()
    }
    
    // 触发原始click事件
    this.$emit('click', event)
  }

  get placeholderText(): string {
    return this.title?.charAt(0) || '?'
  }

  get cardClasses(): string[] {
    const baseClasses = [
      'card-base',
      'text-primary'
    ]
    
    // 尺寸样式
    const sizeClasses = {
      small: 'card-small',
      medium: 'card-medium', 
      large: 'card-large',
      'extra-large': 'card-extra-large'
    }
    
    // 变体样式
    const variantClasses = {
      facility: 'card-facility',
      shop: 'card-shop',
      office: 'card-office',
      transport: 'card-transport'
    }
    
    // 布局样式
    const layoutClasses = {
      vertical: 'card-vertical',
      horizontal: 'card-horizontal'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      layoutClasses[this.layout]
    ]
  }

  get iconContainerClasses(): string[] {
    const baseClasses = ['icon-container']
    
    const sizeClasses = {
      small: 'icon-container-small',
      medium: 'icon-container-medium',
      large: 'icon-container-large', 
      'extra-large': 'icon-container-extra-large'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size]
    ]
  }

  get iconImageClasses(): string[] {
    return ['icon-image']
  }

  get placeholderClasses(): string[] {
    const baseClasses = ['icon-placeholder']
    
    const sizeClasses = {
      small: 'placeholder-small',
      medium: 'placeholder-medium',
      large: 'placeholder-large',
      'extra-large': 'placeholder-extra-large'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size]
    ]
  }

  get contentClasses(): string[] {
    const baseClasses = ['card-content']
    
    const layoutClasses = {
      vertical: 'content-vertical',
      horizontal: 'content-horizontal'
    }
    
    return [
      ...baseClasses,
      layoutClasses[this.layout]
    ]
  }

  get titleClasses(): string[] {
    const baseClasses = ['card-title', 'text-primary']
    
    const sizeClasses = {
      small: 'title-small',
      medium: 'title-medium',
      large: 'title-large',
      'extra-large': 'title-extra-large'
    }
    
    const variantClasses = {
      facility: 'title-facility',
      shop: 'title-shop', 
      office: 'title-office',
      transport: 'title-transport'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant]
    ]
  }

  get subtitleClasses(): string[] {
    const baseClasses = ['card-subtitle', 'text-primary']
    
    const sizeClasses = {
      small: 'subtitle-small',
      medium: 'subtitle-medium',
      large: 'subtitle-large',
      'extra-large': 'subtitle-extra-large'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size]
    ]
  }

  get marqueeTextClasses(): string[] {
    // 为MarqueeText创建不包含overflow样式的类
    const baseClasses = ['marquee-title', 'text-primary']
    
    const sizeClasses = {
      small: 'marquee-title-small',
      medium: 'marquee-title-medium',
      large: 'marquee-title-large',
      'extra-large': 'marquee-title-extra-large'
    }
    
    const variantClasses = {
      facility: 'title-facility',
      shop: 'title-shop', 
      office: 'title-office',
      transport: 'title-transport'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant]
    ]
  }

  get titleContainerClass(): string {
    return `title-container-${this.size}`
  }

  get titleContainerStyle(): Record<string, any> {
    // 为走马灯提供容器样式
    if (this.size === 'large') {
      return {
        width: '360px', /* 减小宽度以便更容易触发走马灯效果 */
        position: 'absolute',
        bottom: '60px',
        left: '50%',
        transform: 'translateX(-50%)'
      }
    }
    return {}
  }

  get titleTextStyle(): Record<string, any> {
    // 为走马灯文本提供样式
    const sizeStyles = {
      small: {
        fontWeight: 'var(--font-weight-medium)',
        fontSize: 'var(--font-size-xs)'
      },
      medium: {
        fontWeight: 'var(--font-weight-normal)',
        fontSize: 'var(--font-size-sm)'
      },
      large: {
        fontWeight: 'var(--font-weight-medium)',
        fontSize: 'var(--font-size-md)'
      },
      'extra-large': {
        fontWeight: 'var(--font-weight-light)',
        fontSize: 'var(--font-size-lg)'
      }
    }
    
    return {
      color: 'inherit',
      ...sizeStyles[this.size]
    }
  }
}
</script>

<style scoped>
/* 卡片基础样式已在 design-system.css 中定义 */

/* 卡片尺寸变体 */
.card-small {
  width: 200px;
  height: 200px;
  border-radius: var(--radius-xl);
}

.card-medium {
  width: 219px;
  height: 218px;
  border-radius: var(--radius-md);
}

.card-large {
  width: 428px;
  height: 428px;
  border-radius: var(--radius-xl);
  border-width: 9px;
}

.card-extra-large {
  width: 2000px;
  height: 197px;
  border-radius: var(--radius-md);
  border-width: 2px;
}

/* 卡片布局 */
.card-vertical {
  flex-direction: column;
}

.card-horizontal {
  flex-direction: row;
}

/* 图标容器尺寸 */
.icon-container-small {
  width: 80px;
  height: 80px;
}

.icon-container-medium {
  width: 91px;
  height: 131px;
  margin-bottom: var(--spacing-xs);
}

.icon-container-large {
  width: 320px;
  height: 206px;
  margin-bottom: 34px;
  position: relative;
  top: -35px;
}

.icon-container-extra-large {
  width: 190px;
  height: 156px;
  margin-left: 43px;
  flex-shrink: 0;
}

/* 图标图片 */
.icon-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 占位符尺寸 */
.placeholder-small {
  width: 60px;
  height: 60px;
  font-size: var(--font-size-xs);
}

.placeholder-medium {
  width: 60px;
  height: 60px;
  font-size: var(--font-size-xs);
}

.placeholder-large {
  width: 160px;
  height: 160px;
  font-size: var(--font-size-xl);
}

.placeholder-extra-large {
  width: 120px;
  height: 120px;
  font-size: var(--font-size-md);
}

/* 内容区域 */
.card-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-vertical {
  flex-direction: column;
  text-align: center;
}

.content-horizontal {
  flex: 1;
  justify-content: space-between;
  padding: 0 85px 0 48px;
}

/* 标题样式 */
.card-title {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* MarqueeText专用标题样式 - 不包含overflow限制 */
.marquee-title {
  text-align: center;
  /* 不设置overflow和text-overflow，让MarqueeText自己处理 */
}

.marquee-title-small {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xs);
}

.marquee-title-medium {
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-sm);
}

.marquee-title-large {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
}

.marquee-title-extra-large {
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-lg);
  text-align: left;
}

.title-small {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xs);
  max-width: 180px;
}

.title-medium {
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-sm);
  max-width: 200px;
}

.title-large {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
  max-width: 200px; /* 与走马灯容器宽度保持一致 */
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
}

.title-extra-large {
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-lg);
  text-align: left;
}

/* 子标题样式 */
.card-subtitle {
  text-align: center;
}

.subtitle-extra-large {
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-lg);
  text-align: right;
}

/* 变体特定样式 - 移动端性能优化版 */
.card-facility {
  /* 使用简化的box-shadow以提升性能 */
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);

  /* 移动端优化：移除transition以提升滚动性能 */
  transition: none;

  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

.card-shop {
  /* 静态阴影，移除hover动画以提升滚动性能 */
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);

  /* 优化GPU使用 */
  transform: translateZ(0);
  backface-visibility: hidden;

  /* 减少重绘 */
  contain: layout style paint;
}

.card-transport.card-small {
  gap: 16px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);

  /* 移动端优化：移除transition */
  transition: none;

  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 只在桌面端启用hover效果 */
@media (hover: hover) and (pointer: fine) {
  .card-facility {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .card-facility:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.3);
  }

  .card-transport.card-small {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .card-transport.card-small:hover {
    transform: translateY(-2px) scale(1.02) translateZ(0);
    box-shadow:
      0px 12px 24px 0px rgba(0, 0, 0, 0.25),
      inset 0px 2px 4px 0px rgba(255, 255, 255, 0.15),
      inset 0px -2px 4px 0px rgba(0, 0, 0, 0.15);
  }
}

/* 响应式缩放 - 适用于office卡片 */
.card-extra-large {
  /* 4K基准缩放适配 */
}

/* 走马灯标题容器样式 */
.title-container-small {
  max-width: 180px;
}

.title-container-medium {
  max-width: 200px;
}

.title-container-large {
  width: 360px;
}

.title-container-extra-large {
  flex: 1;
}

@media screen and (max-width: 2160px) {
  .card-extra-large {
    transform: scale(calc(100vw / 2160));
    transform-origin: left top;
  }
}
</style>