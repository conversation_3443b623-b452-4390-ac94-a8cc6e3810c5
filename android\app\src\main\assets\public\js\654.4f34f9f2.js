"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[654],{654:(s,a,e)=>{e.r(a),e.d(a,{default:()=>k});var t=function(){var s=this,a=s._self._c;s._self._setupProxy;return a("div",{staticClass:"shop-detail-container responsive-page-container",style:s.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--shop"},[a("h1",{staticClass:"app-title"},[s._v(s._s(s.$t("pageTitle.shopDetail")))])]),a("div",{staticClass:"scrollable-content-area",style:s.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--detail"},[s._m(0),a("div",{staticClass:"shop-info-container"},[a("div",{staticClass:"shop-logo"},[s.shopData.logo?a("img",{staticClass:"logo-image",attrs:{src:s.shopData.logo,alt:s.shopData.name}}):a("div",{staticClass:"logo-placeholder"},[s._v(s._s(s.shopData.name.charAt(0)))])]),a("div",{staticClass:"shop-details"},[a("div",{staticClass:"shop-name-large"},[s._v(s._s(s.shopData.name))]),a("div",{staticClass:"shop-location-text"},[s._v(s._s(s.shopData.location))]),a("div",{staticClass:"shop-hours-text"},[s._v(s._s(s.shopData.hours))]),a("div",{staticClass:"shop-description-text"},[s._v(s._s(s.shopData.description))])]),a("div",{staticClass:"back-button",on:{click:s.goBackToShop}},[a("svg",{staticClass:"back-icon",attrs:{viewBox:"0 0 24 24",fill:"none"}},[a("path",{attrs:{d:"M19 12H5M12 5L5 12L12 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})]),a("span",{staticClass:"back-text"},[s._v(s._s(s.$t("common.back")))])])])])]),a("BottomBar",{on:{"home-clicked":s.onHomeClick,"language-changed":s.onLanguageChange,"ai-clicked":s.onAIClick}})],1)},o=[function(){var s=this,a=s._self._c;s._self._setupProxy;return a("div",{staticClass:"map-container"},[a("img",{staticClass:"map-image",attrs:{src:"/img/shops/map001.png",alt:"Shop Location Map"}})])}],i=e(635),n=e(233),l=e(14),r=e(958),c=e(256),p=e(185),d=e(959),h=e(713);let u=class extends((0,n.Xe)(p.A,d.A)){getBackgroundColor(){return"#0F04A9"}shopData={id:"",name:"",location:"",hours:"",description:"",logo:""};mounted(){const s=window.Capacitor;s&&s.Plugins&&s.Plugins.App&&s.Plugins.App.addListener("backButton",this.handleBackButton),window.addEventListener("popstate",this.handlePopState)}beforeDestroy(){const s=window.Capacitor;s&&s.Plugins&&s.Plugins.App&&s.Plugins.App.removeAllListeners(),window.removeEventListener("popstate",this.handlePopState)}created(){const s=this.$route.params.id;if(s&&(0,h.kv)(s)){const a=(0,h.z)(s);a?this.shopData=a:this.$router.push("/shop")}else this.$router.push("/shop")}goBackToShop(){window.history.length>1?this.$router.go(-1):this.$router.push("/shop")}handleBackButton(){this.goBackToShop()}handlePopState(){}};u=(0,i.Cg)([(0,n.uA)({components:{TopBar:l.A,BottomBar:r.A,BackgroundImage:c.A}})],u);const g=u,m=g;var f=e(656),v=(0,f.A)(m,t,o,!1,null,"359e247e",null);const k=v.exports},713:(s,a,e)=>{e.d(a,{aF:()=>i,kv:()=>n,z:()=>o});const t={"7-eleven":{id:"7-eleven",name:"7-Eleven",location:"G028-G029",hours:"10:00 - 22:00 (Closed)",description:"Convenient, 24/7, quick snacks, essentials, drinks, grab-and-go, bright signage, busy, affordable, compact, diverse products, coffee, chilled treats, ATM, friendly staff, always open, efficient, popular, handy, lifesaver.",logo:"/img/shops/7-eleven.png"},"din-tai-fung":{id:"din-tai-fung",name:"Din Tai Fung",location:"G030-G031",hours:"11:00 - 21:30",description:"Famous for xiaolongbao (soup dumplings), authentic Taiwanese cuisine, fresh ingredients, handmade dumplings, steamed buns, noodles, premium dining experience, quality service.",logo:"/img/shops/din-tai-fung.png"},amoment:{id:"amoment",name:"Amoment",location:"G032-G033",hours:"10:00 - 22:00",description:"Modern lifestyle store, trendy accessories, home decor, unique gifts, contemporary design, quality products, Instagram-worthy items, urban lifestyle.",logo:"/img/shops/amoment.png"},starbucks:{id:"starbucks",name:"Starbucks",location:"G034-G035",hours:"07:00 - 23:00",description:"Premium coffee chain, espresso drinks, frappuccinos, pastries, cozy atmosphere, free WiFi, meeting spot, consistent quality, global brand.",logo:"/img/shops/starbucks.png"},"law-mark-kee":{id:"law-mark-kee",name:"Law Mark Kee",location:"G036-G037",hours:"08:00 - 20:00",description:"Traditional Hong Kong style restaurant, dim sum, wonton noodles, congee, cha chaan teng classics, authentic flavors, local favorites.",logo:"/img/shops/law-mark-kee.png"},"haagen-dazs":{id:"haagen-dazs",name:"Haagen-Dazs",location:"G038-G039",hours:"10:00 - 22:00",description:"Premium ice cream brand, luxury frozen desserts, rich flavors, creamy texture, sundaes, milkshakes, indulgent treats, high quality ingredients.",logo:"/img/shops/haagen-dazs.png"},catalo:{id:"catalo",name:"Catalo",location:"G040-G041",hours:"10:00 - 21:00",description:"Health and wellness store, nutritional supplements, vitamins, natural products, healthcare solutions, wellness consultation, trusted brand.",logo:"/img/shops/catalo.png"},"italian-tomato":{id:"italian-tomato",name:"Italian Tomato",location:"G042-G043",hours:"09:00 - 22:00",description:"Italian cuisine, fresh pasta, pizza, authentic flavors, casual dining, family-friendly, Mediterranean atmosphere, quality ingredients.",logo:"/img/shops/italian-tomato.png"},smarton:{id:"smarton",name:"Smarton",location:"G044-G045",hours:"10:00 - 21:00",description:"Electronics and technology store, smartphones, gadgets, accessories, latest tech trends, competitive prices, technical support.",logo:"/img/shops/smarton.png"},"paul-joe":{id:"paul-joe",name:"Paul & Joe",location:"G046-G047",hours:"10:00 - 21:00",description:"French fashion and cosmetics brand, chic clothing, makeup, skincare, Parisian style, feminine designs, quality fashion accessories.",logo:"/img/shops/paul-joe.png"},buglls:{id:"buglls",name:"Buglls",location:"G048-G049",hours:"10:00 - 21:00",description:"Trendy fashion store, contemporary clothing, stylish accessories, modern designs, urban fashion, quality apparel, fashion-forward styles.",logo:"/img/shops/buglls.png"},select:{id:"select",name:"Select",location:"G050-G051",hours:"10:00 - 21:00",description:"Curated lifestyle store, premium products, unique selections, quality items, modern lifestyle, exclusive brands, sophisticated choices.",logo:"/img/shops/select.png"}},o=s=>t[s]||null,i=()=>Object.values(t).map(s=>({id:s.id,name:s.name,logo:s.logo})),n=s=>s in t}}]);