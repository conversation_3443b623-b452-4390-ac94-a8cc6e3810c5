"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[103],{103:(e,t,s)=>{s.r(t),s.d(t,{default:()=>C});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"ai-search-page responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--ai"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.aiSearch")))])]),t("div",{staticClass:"search-input-container"},[t("div",{staticClass:"search-icon"},[t("svg",{attrs:{width:"116",height:"116",viewBox:"0 0 116 116",fill:"none"}},[t("path",{attrs:{d:"M51.6 90.2c21.3 0 38.6-17.3 38.6-38.6S72.9 13 51.6 13 13 30.3 13 51.6 30.3 90.2 51.6 90.2zM96.8 103L71.4 77.6",stroke:"white","stroke-width":"6","stroke-linecap":"round","stroke-linejoin":"round"}})])]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.searchQuery,expression:"searchQuery"}],staticClass:"search-input",attrs:{type:"text",placeholder:e.$t("aiSearch.placeholder")},domProps:{value:e.searchQuery},on:{input:[function(t){t.target.composing||(e.searchQuery=t.target.value)},e.onSearchInput]}})]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--shop"},[e.searchQuery.trim()?0===e.filteredShops.length?t("div",{staticClass:"no-results"},[t("div",{staticClass:"no-results-icon"},[e._v("😔")]),t("h2",{staticClass:"no-results-title"},[e._v(e._s(e.$t("shop.noResults")))]),t("p",{staticClass:"no-results-description"},[e._v(e._s(e.$t("shop.tryOtherKeywords")))])]):t("div",{staticClass:"shop-grid"},e._l(e.filteredShops,function(s){return t("ShopCard",{key:s.id,attrs:{"shop-name":s.name,"logo-src":s.logo},on:{click:function(t){return e.navigateToShop(s.id)}}})}),1):t("div",{staticClass:"search-prompt"},[t("div",{staticClass:"prompt-icon"},[e._v("🔍")]),t("h2",{staticClass:"prompt-title"},[e._v(e._s(e.$t("shop.startSearch")))]),t("p",{staticClass:"prompt-description"},[e._v(e._s(e.$t("shop.searchPrompt")))])])])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}})],1)},o=[],i=s(635),r=s(233),n=s(14),l=s(958),c=s(256),d=s(710),u=s(185),p=s(959),h=s(713);let g=class extends((0,r.Xe)(u.A,p.A)){getBackgroundColor(){return"#FF69B4"}searchQuery="";get shops(){return(0,h.aF)()}get filteredShops(){if(!this.searchQuery.trim())return[];const e=this.searchQuery.toLowerCase().trim();return this.shops.filter(t=>t.name.toLowerCase().includes(e))}onSearchInput(e){const t=e.target;this.searchQuery=t.value}navigateToShop(e){console.log("导航到商店:",e),this.$router.push(`/shop/${e}`)}};g=(0,i.Cg)([(0,r.uA)({components:{TopBar:n.A,BottomBar:l.A,BackgroundImage:c.A,ShopCard:d.A}})],g);const m=g,v=m;var f=s(656),y=(0,f.A)(v,a,o,!1,null,"58301d76",null);const C=y.exports},150:(e,t,s)=>{s.d(t,{A:()=>p});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{class:e.cardClasses,on:{click:function(t){return e.$emit("click")}}},[e.showIcon?t("div",{class:e.iconContainerClasses},[e.iconSrc?t("img",{class:e.iconImageClasses,attrs:{src:e.iconSrc,alt:e.title}}):e.showPlaceholder?t("div",{class:e.placeholderClasses},[e._v(" "+e._s(e.placeholderText)+" ")]):e._e()]):e._e(),e.showContent?t("div",{class:e.contentClasses},[e._t("default",function(){return[e.title?t("div",{class:e.titleClasses},[e._v(" "+e._s(e.title)+" ")]):e._e(),e.subtitle?t("div",{class:e.subtitleClasses},[e._v(" "+e._s(e.subtitle)+" ")]):e._e()]})],2):e._e(),e._t("custom")],2)},o=[],i=s(635),r=s(233);let n=class extends r.lD{title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const e=["card-base","text-primary"],t={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},s={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},a={vertical:"card-vertical",horizontal:"card-horizontal"};return[...e,t[this.size],s[this.variant],a[this.layout]]}get iconContainerClasses(){const e=["icon-container"],t={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...e,t[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const e=["icon-placeholder"],t={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...e,t[this.size]]}get contentClasses(){const e=["card-content"],t={vertical:"content-vertical",horizontal:"content-horizontal"};return[...e,t[this.layout]]}get titleClasses(){const e=["card-title","text-primary"],t={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},s={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...e,t[this.size],s[this.variant]]}get subtitleClasses(){const e=["card-subtitle","text-primary"],t={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...e,t[this.size]]}};(0,i.Cg)([(0,r.kv)({required:!0})],n.prototype,"title",void 0),(0,i.Cg)([(0,r.kv)()],n.prototype,"subtitle",void 0),(0,i.Cg)([(0,r.kv)()],n.prototype,"iconSrc",void 0),(0,i.Cg)([(0,r.kv)({default:"medium"})],n.prototype,"size",void 0),(0,i.Cg)([(0,r.kv)({default:"facility"})],n.prototype,"variant",void 0),(0,i.Cg)([(0,r.kv)({default:"vertical"})],n.prototype,"layout",void 0),(0,i.Cg)([(0,r.kv)({default:!0})],n.prototype,"showIcon",void 0),(0,i.Cg)([(0,r.kv)({default:!0})],n.prototype,"showContent",void 0),(0,i.Cg)([(0,r.kv)({default:!0})],n.prototype,"showPlaceholder",void 0),n=(0,i.Cg)([r.uA],n);const l=n,c=l;var d=s(656),u=(0,d.A)(c,a,o,!1,null,"022da4db",null);const p=u.exports},710:(e,t,s)=>{s.d(t,{A:()=>h});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("BaseCard",{attrs:{title:e.shopName,"icon-src":e.logoSrc,size:"large",variant:"shop",layout:"vertical"},on:{click:function(t){return e.$emit("click")}}})},o=[],i=s(635),r=s(233),n=s(150);let l=class extends r.lD{shopName;logoSrc};(0,i.Cg)([(0,r.kv)({required:!0})],l.prototype,"shopName",void 0),(0,i.Cg)([(0,r.kv)({default:""})],l.prototype,"logoSrc",void 0),l=(0,i.Cg)([(0,r.uA)({components:{BaseCard:n.A}})],l);const c=l,d=c;var u=s(656),p=(0,u.A)(d,a,o,!1,null,null,null);const h=p.exports},713:(e,t,s)=>{s.d(t,{aF:()=>i,kv:()=>r,z:()=>o});const a={"7-eleven":{id:"7-eleven",name:"7-Eleven",location:"G028-G029",hours:"10:00 - 22:00 (Closed)",description:"Convenient, 24/7, quick snacks, essentials, drinks, grab-and-go, bright signage, busy, affordable, compact, diverse products, coffee, chilled treats, ATM, friendly staff, always open, efficient, popular, handy, lifesaver.",logo:"/img/shops/7-eleven.png"},"din-tai-fung":{id:"din-tai-fung",name:"Din Tai Fung",location:"G030-G031",hours:"11:00 - 21:30",description:"Famous for xiaolongbao (soup dumplings), authentic Taiwanese cuisine, fresh ingredients, handmade dumplings, steamed buns, noodles, premium dining experience, quality service.",logo:"/img/shops/din-tai-fung.png"},amoment:{id:"amoment",name:"Amoment",location:"G032-G033",hours:"10:00 - 22:00",description:"Modern lifestyle store, trendy accessories, home decor, unique gifts, contemporary design, quality products, Instagram-worthy items, urban lifestyle.",logo:"/img/shops/amoment.png"},starbucks:{id:"starbucks",name:"Starbucks",location:"G034-G035",hours:"07:00 - 23:00",description:"Premium coffee chain, espresso drinks, frappuccinos, pastries, cozy atmosphere, free WiFi, meeting spot, consistent quality, global brand.",logo:"/img/shops/starbucks.png"},"law-mark-kee":{id:"law-mark-kee",name:"Law Mark Kee",location:"G036-G037",hours:"08:00 - 20:00",description:"Traditional Hong Kong style restaurant, dim sum, wonton noodles, congee, cha chaan teng classics, authentic flavors, local favorites.",logo:"/img/shops/law-mark-kee.png"},"haagen-dazs":{id:"haagen-dazs",name:"Haagen-Dazs",location:"G038-G039",hours:"10:00 - 22:00",description:"Premium ice cream brand, luxury frozen desserts, rich flavors, creamy texture, sundaes, milkshakes, indulgent treats, high quality ingredients.",logo:"/img/shops/haagen-dazs.png"},catalo:{id:"catalo",name:"Catalo",location:"G040-G041",hours:"10:00 - 21:00",description:"Health and wellness store, nutritional supplements, vitamins, natural products, healthcare solutions, wellness consultation, trusted brand.",logo:"/img/shops/catalo.png"},"italian-tomato":{id:"italian-tomato",name:"Italian Tomato",location:"G042-G043",hours:"09:00 - 22:00",description:"Italian cuisine, fresh pasta, pizza, authentic flavors, casual dining, family-friendly, Mediterranean atmosphere, quality ingredients.",logo:"/img/shops/italian-tomato.png"},smarton:{id:"smarton",name:"Smarton",location:"G044-G045",hours:"10:00 - 21:00",description:"Electronics and technology store, smartphones, gadgets, accessories, latest tech trends, competitive prices, technical support.",logo:"/img/shops/smarton.png"},"paul-joe":{id:"paul-joe",name:"Paul & Joe",location:"G046-G047",hours:"10:00 - 21:00",description:"French fashion and cosmetics brand, chic clothing, makeup, skincare, Parisian style, feminine designs, quality fashion accessories.",logo:"/img/shops/paul-joe.png"},buglls:{id:"buglls",name:"Buglls",location:"G048-G049",hours:"10:00 - 21:00",description:"Trendy fashion store, contemporary clothing, stylish accessories, modern designs, urban fashion, quality apparel, fashion-forward styles.",logo:"/img/shops/buglls.png"},select:{id:"select",name:"Select",location:"G050-G051",hours:"10:00 - 21:00",description:"Curated lifestyle store, premium products, unique selections, quality items, modern lifestyle, exclusive brands, sophisticated choices.",logo:"/img/shops/select.png"}},o=e=>a[e]||null,i=()=>Object.values(a).map(e=>({id:e.id,name:e.name,logo:e.logo})),r=e=>e in a}}]);