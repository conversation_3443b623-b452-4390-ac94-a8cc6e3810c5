// 語言包載入器
import i18nService from './index'
import { detectAndSetLanguage } from './detector'
import zhTW from './locales/zh-TW'
import en from './locales/en'
import es from './locales/es'
import ja from './locales/ja'
import ko from './locales/ko'
import th from './locales/th'

// 載入所有語言包
export function loadTranslations(): void {
  i18nService.setTranslations('zh-TW', zhTW)
  i18nService.setTranslations('en', en)
  i18nService.setTranslations('es', es)
  i18nService.setTranslations('ja', ja)
  i18nService.setTranslations('ko', ko)
  i18nService.setTranslations('th', th)
}

// 初始化國際化
export async function initI18n(vueInstance?: any): Promise<void> {
  console.log('[i18n] 開始初始化國際化系統...')
  
  // 載入所有語言包
  loadTranslations()
  console.log('[i18n] 語言包載入完成')
  
  // 確保語言包已經載入
  await new Promise(resolve => setTimeout(resolve, 10))
  
  // 自動檢測並設置語言
  const detectedLanguage = detectAndSetLanguage(vueInstance)
  console.log(`[i18n] 檢測到語言: ${detectedLanguage}`)
  
  // 強制觸發一次更新
  if (vueInstance) {
    vueInstance.$forceUpdate()
    
    // 確保所有子組件也更新
    setTimeout(() => {
      vueInstance.$root.$emit('language-changed', detectedLanguage)
    }, 100)
  }
  
  console.log(`[i18n] 初始化完成，當前語言: ${detectedLanguage}`)
}

export default {
  loadTranslations,
  initI18n
} 