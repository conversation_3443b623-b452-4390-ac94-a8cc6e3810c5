@tailwind base;
@tailwind components;
@tailwind utilities;

/* 引入设计系统 */
@import './design-system.css';

/* 引入移动端性能优化样式 */
@import './mobile-optimizations.css';

/* 全局样式 - 模拟原生应用 */
/* 禁用文字选择 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许输入框选择文字 */
input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  -webkit-touch-callout: default; /* 允许长按操作 */
}

/* 禁用长按上下文菜单 */
* {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 禁用拖拽 */
* {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 禁用图片拖拽和保存 */
img {
  pointer-events: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 恢复按钮和链接的点击事件 */
button, a, input, textarea, select {
  pointer-events: auto;
}

/* 恢复 Swiper 组件的触摸事件 */
.swiper-container, .swiper-wrapper, .swiper-slide {
  pointer-events: auto;
}

.swiper-container img, .swiper-slide img {
  pointer-events: auto;
}

/* 禁用双击选择 */
body {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 禁用iOS Safari的弹跳效果 */
body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* 移除默认的 focus outline，使用自定义样式 */
* {
  outline: none;
}

