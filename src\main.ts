import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './styles/main.css'

// 國際化相關導入
import { initI18n } from '@/i18n/loader'
import '@/i18n/debug' // 導入調試工具

// Capacitor 相关导入
import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'
import { SplashScreen } from '@capacitor/splash-screen'

// 音效服务导入
import { soundService } from '@/services/audioManager'
import SoundPlugin from '@/plugins/sound'

Vue.config.productionTip = false

// 注册音效插件
Vue.use(SoundPlugin)

// 禁用原生行为函数
const disableNativeBehaviors = () => {
  console.log('开始禁用原生浏览器行为...')
  
  // 禁用双击缩放
  let lastTouchEnd = 0
  document.addEventListener('touchend', (event) => {
    const now = (new Date()).getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, false)

  // 禁用双击事件
  document.addEventListener('dblclick', (event) => {
    event.preventDefault()
  }, false)

  // 禁用鼠标右键菜单
  document.addEventListener('contextmenu', (event) => {
    event.preventDefault()
  }, false)

  // 禁用拖拽事件
  document.addEventListener('dragstart', (event) => {
    event.preventDefault()
  }, false)

  // 禁用选择文字（额外保障）
  document.addEventListener('selectstart', (event) => {
    event.preventDefault()
  }, false)

  // 禁用键盘快捷键（如 Ctrl+A, Ctrl+C 等）
  document.addEventListener('keydown', (event) => {
    // 禁用常见的快捷键
    if (event.ctrlKey || event.metaKey) {
      // 允许 F5 刷新和开发者工具
      if (event.key !== 'F5' && event.key !== 'F12') {
        event.preventDefault()
      }
    }
    
    // 禁用 F12 开发者工具（可选）
    if (event.key === 'F12') {
      event.preventDefault()
    }
  }, false)

  // 禁用缩放手势
  document.addEventListener('gesturestart', (event) => {
    event.preventDefault()
  }, false)

  document.addEventListener('gesturechange', (event) => {
    event.preventDefault()
  }, false)

  document.addEventListener('gestureend', (event) => {
    event.preventDefault()
  }, false)

  // 禁用滚轮缩放
  document.addEventListener('wheel', (event) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault()
    }
  }, { passive: false })

  console.log('原生浏览器行为禁用完成')
}

// 强制全屏函数
const forceFullscreen = () => {
  if (Capacitor.isNativePlatform()) {
    // 使用原生JavaScript强制全屏
    setTimeout(() => {
      try {
        // 设置viewport为全屏
        const metaViewport = document.querySelector('meta[name="viewport"]')
        if (metaViewport) {
          metaViewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover')
        }

        // 强制设置body样式
        document.body.style.width = '100vw'
        document.body.style.height = '100vh'
        document.body.style.margin = '0'
        document.body.style.padding = '0'
        document.body.style.overflow = 'hidden'
        
        // 设置app容器
        const app = document.getElementById('app')
        if (app) {
          app.style.width = '100vw'
          app.style.height = '100vh'
          app.style.position = 'fixed'
          app.style.top = '0'
          app.style.left = '0'
        }

        console.log('强制全屏设置完成')
      } catch (error) {
        console.error('强制全屏设置失败:', error)
      }
    }, 100)
  }
}

// 初始化 Capacitor 功能
const initializeApp = async () => {
  // 只在原生平台运行
  if (Capacitor.isNativePlatform()) {
    try {
      // 先隐藏启动画面
      await SplashScreen.hide()
      
      // 设置状态栏为隐藏模式
      await StatusBar.hide()
      
      // 强制全屏设置
      forceFullscreen()
      
      console.log('Capacitor 插件初始化完成')
    } catch (error) {
      console.error('Capacitor 插件初始化失败:', error)
      // 即使插件失败，也尝试强制全屏
      forceFullscreen()
    }
  }
}

// 初始化应用配置
const initializeConfig = async () => {
  try {
    console.log('开始初始化应用配置...')
    await store.dispatch('config/initConfig')
    console.log('应用配置初始化完成')
  } catch (error) {
    console.error('应用配置初始化失败:', error)
  }
}

// 创建 Vue 实例
const createApp = async () => {
  // 立即禁用原生浏览器行为
  disableNativeBehaviors()
  
  // 先初始化 Capacitor 功能
  await initializeApp()
  
  // 初始化应用配置
  await initializeConfig()
  
  const app = new Vue({
    router,
    store,
    render: h => h(App),
    async created() {
      // 在組件创建時就初始化國際化系統
      await initI18n(this)
    },
    async mounted() {
      // 再次确保全屏设置
      if (Capacitor.isNativePlatform()) {
        forceFullscreen()
        
        // 监听窗口变化，确保始终全屏
        window.addEventListener('resize', forceFullscreen)
        window.addEventListener('orientationchange', () => {
          setTimeout(forceFullscreen, 300)
        })
      }
      
      // 确保原生行为禁用在所有情况下都生效
      setTimeout(disableNativeBehaviors, 100)
      
      // 初始化优化的音频管理器（需要用户交互）
      document.addEventListener('click', async () => {
        await soundService.initUserInteraction()
      }, { once: true })

      document.addEventListener('touchstart', async () => {
        await soundService.initUserInteraction()
      }, { once: true })
    }
  }).$mount('#app')
  
  return app
}

// 立即启用禁用原生行为（确保在DOM完全加载前就生效）
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', disableNativeBehaviors)
} else {
  disableNativeBehaviors()
}

// 启动应用
createApp() 