<template>
  <div class="facility-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Facility主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--facility">
      <h1 class="app-title">{{ $t('pageTitle.facility') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--facility">
        <!-- 地图区域 -->
        <div class="map-container">
          <img 
            src="/img/facilities/facility-map.png" 
            alt="Facility Map" 
            class="map-image"
            @error="onImageError"
          />
        </div>
        
        <!-- 设施按钮网格 - 右侧垂直排列 -->
        <div class="facilities-grid">
          <FacilityCard
            v-for="facility in facilities"
            :key="facility.id"
            :facility-id="facility.id"
            :facility-name="facility.name"
            :icon-src="facility.icon"
            @facility-clicked="onFacilityClick"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import FacilityCard from '@/components/FacilityCard.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

interface FacilityItem {
  id: string
  name: string
  icon: string
}

interface FacilityClickData {
  id: string
  name: string
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    FacilityCard,
    BackgroundImage
  }
})
export default class Facility extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#016513' // Facility页面的绿色背景
  }

  facilities: FacilityItem[] = [
    { id: 'men', name: 'Men', icon: '/img/facilities/men.png' },
    { id: 'women', name: 'Women', icon: '/img/facilities/women.png' },
    { id: 'baby', name: 'Baby', icon: '/img/facilities/baby.png' },
    { id: 'services', name: 'Services', icon: '/img/facilities/services.png' },
    { id: 'lift', name: 'Lift', icon: '/img/facilities/lift.png' },
    { id: 'escalator', name: 'Escalator', icon: '/img/facilities/escalator.png' },
    { id: 'accessibly', name: 'Accessibly', icon: '/img/facilities/accessibly.png' },
    { id: 'locker', name: 'Locker', icon: '/img/facilities/locker.png' }
  ]

  // 额外设施数据来测试滚动
  extraFacilities: FacilityItem[] = [
  ]

  onFacilityClick(facility: FacilityClickData) {
    console.log('点击设施:', facility)
    // 这里可以添加设施详情导航或功能
  }

  onImageError(event: Event) {
    const target = event.target as HTMLImageElement
    if (target) {
      target.src = '/img/placeholder-transport.png'
    }
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
/* 地图容器 - 根据设计稿调整位置和尺寸 */
.map-container {
  position: absolute;
  left: 140px;
  top: 0;
  width: 1600px;
  height: 2507px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  
  /* 毛玻璃效果 */
  box-shadow: 
    2px 2px 4px 0px rgba(0, 0, 0, 0.25),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}

/* map-placeholder 样式已移除，直接使用 map-image */

.map-image {
  position: absolute;
  left: 288px;
  top: 52px;
  width: 1096px;
  height: 2455px;
  object-fit: cover;
}

/* 设施网格 - 右侧垂直排列，根据设计稿调整 */
.facilities-grid {
  position: absolute;
  top: 0;
  right: 96px;
  width: 219px;
  
  display: flex;
  flex-direction: column;
  gap: 27px;
  align-items: center;
}

/* 鼠标悬浮效果 */
.facility-container :deep(.glass-button:hover),
.facility-container :deep(.facility-card:hover) {
  transform: translateY(-4px);
  box-shadow: 
    0px 8px 20px rgba(0, 0, 0, 0.3),
    inset 0px 4px 4px 0px rgba(255, 255, 255, 0.15),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.25);
}
</style> 