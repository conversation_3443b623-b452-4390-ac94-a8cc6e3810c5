<template>
  <div class="ai-search-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Search主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--ai">
      <h1 class="app-title">{{ $t('pageTitle.aiSearch') }}</h1>
    </div>
    
    <!-- 搜索输入框 - 固定位置，位于标题下方 -->
    <div class="search-input-container">
      <div class="search-icon">
        <svg width="116" height="116" viewBox="0 0 116 116" fill="none">
          <path d="M51.6 90.2c21.3 0 38.6-17.3 38.6-38.6S72.9 13 51.6 13 13 30.3 13 51.6 30.3 90.2 51.6 90.2zM96.8 103L71.4 77.6" stroke="white" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <input
        v-model="searchQuery"
        type="text"
        :placeholder="$t('aiSearch.placeholder')"
        class="search-input"
        @input="onSearchInput"
      />
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--shop">
        <!-- 搜索提示（没有搜索内容时显示） -->
        <div v-if="!searchQuery.trim()" class="search-prompt">
          <div class="prompt-icon">🔍</div>
          <h2 class="prompt-title">{{ $t('shop.startSearch') }}</h2>
          <p class="prompt-description">{{ $t('shop.searchPrompt') }}</p>
        </div>
        
        <!-- 没有搜索结果时的提示 -->
        <div v-else-if="filteredShops.length === 0" class="no-results">
          <div class="no-results-icon">😔</div>
          <h2 class="no-results-title">{{ $t('shop.noResults') }}</h2>
          <p class="no-results-description">{{ $t('shop.tryOtherKeywords') }}</p>
        </div>
        
        <!-- 商店卡片网格（有搜索结果时显示） -->
        <div v-else class="shop-grid">
          <ShopCard
            v-for="shop in filteredShops"
            :key="shop.id"
            :shop-name="shop.name"
            :logo-src="shop.logo"
            @click="navigateToShop(shop.id)"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ShopCard from '@/components/ShopCard.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { ShopBasicInfo, getShopsBasicInfo } from '@/services/shopService'

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage,
    ShopCard
  }
})
export default class AISearch extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#FF69B4' // AISearch页面的粉红色背景
  }

  searchQuery = ''
  private shops: ShopBasicInfo[] = []
  private shopsLoaded = false

  async mounted() {
    // 异步加载商店数据
    try {
      this.shops = await getShopsBasicInfo()
      this.shopsLoaded = true
    } catch (error) {
      console.error('加载商店数据失败:', error)
    }
  }

  // 实时筛选的商店列表
  get filteredShops(): ShopBasicInfo[] {
    // 没有搜索内容时不显示任何商店
    if (!this.searchQuery.trim()) {
      return []
    }
    
    const query = this.searchQuery.toLowerCase().trim()
    return this.shops.filter(shop => 
      shop.name.toLowerCase().includes(query)
    )
  }

  // 搜索输入处理
  onSearchInput(event: Event) {
    const target = event.target as HTMLInputElement
    this.searchQuery = target.value
    // 实时筛选，无需额外处理
  }

  // 导航到商店详情
  navigateToShop(shopId: string) {
    console.log('导航到商店:', shopId)
    this.$router.push(`/shop/${shopId}`)
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
/* 搜索输入框容器 - 位于标题下方，固定不滚动 */
.search-input-container {
  position: absolute;
  left: 568px;
  top: 570px; /* 位于标题下方 */
  width: 1024px;
  height: 134px;
  z-index: 15; /* 确保在其他内容之上 */
  
  display: flex;
  align-items: center;
  
  background: rgba(255, 255, 255, 0.05);
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-radius: 60px;
  backdrop-filter: blur(10px);
  
  box-shadow: 
    4px 4px 4px 0px rgba(0, 0, 0, 0.25),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.25),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 116px;
  height: 116px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 100%;
  padding: 0 76px 0 154px; /* 左边给图标留空间 */
  background: transparent;
  border: none;
  outline: none;
  
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 64px;
  color: #FFFFFF;
  line-height: 1.21;
  
  border-radius: 60px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 300;
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.05);
}

/* 搜索提示样式 */
.search-prompt,
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 150px 50px;
  margin: 100px auto;
  max-width: 800px;
}

.prompt-icon,
.no-results-icon {
  font-size: 120px;
  margin-bottom: 40px;
  opacity: 0.8;
}

.prompt-title,
.no-results-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 64px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 30px 0;
  line-height: 1.2;
}

.prompt-description,
.no-results-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 36px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
  max-width: 600px;
}

/* 商店网格布局 */
.shop-grid {
  display: grid;
  grid-template-columns: repeat(4, 428px); /* 4列，每列428px */
  grid-auto-rows: 428px; /* 每行428px */
  gap: 70px; /* 统一间距 */
  justify-content: center; /* grid内容水平居中 */
  margin: 0 auto;
  padding: 50px 0;
  width: fit-content;
}
</style> 