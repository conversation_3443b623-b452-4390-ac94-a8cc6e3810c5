@import url(https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700&display=swap);*{margin:0;padding:0;box-sizing:border-box}body,html{height:100%;overflow-x:hidden}#app{height:100vh;width:100vw}.glass-button[data-v-2a84b44f]{font-family:var(--font-family-primary);font-weight:var(--font-weight-medium);color:var(--color-white);line-height:var(--line-height-standard);display:flex;align-items:center;justify-content:center;border-radius:var(--radius-lg);border:5px solid transparent;-webkit-backdrop-filter:var(--blur-light);backdrop-filter:var(--blur-light);transition:var(--transition-standard);cursor:pointer;-o-border-image:var(--border-gradient-glass) 1;border-image:var(--border-gradient-glass) 1;box-shadow:var(--shadow-glass-inset)}.small-btn[data-v-2a84b44f]{width:200px;height:203px;font-size:var(--font-size-lg)}.medium-btn[data-v-2a84b44f]{width:550px;height:550px;font-size:var(--font-size-2xl)}.large-btn[data-v-2a84b44f]{width:300px;height:300px;font-size:var(--font-size-xl)}.glass-highlight[data-v-2a84b44f]{background:var(--glass-bg-normal)}.glass-normal[data-v-2a84b44f]{background:var(--glass-bg-light)}.glass-button[data-v-2a84b44f]:hover{transform:var(--transform-lift-md);box-shadow:var(--shadow-glass-inset-hover),var(--shadow-button-hover)}.glass-highlight[data-v-2a84b44f]:hover{background:var(--glass-bg-medium)}.glass-normal[data-v-2a84b44f]:hover{background:hsla(0,0%,100%,.07)}.glass-button-active[data-v-2a84b44f],.glass-button[data-v-2a84b44f]:active{transform:var(--transform-press)}.top-bar[data-v-7caba744]{position:absolute;top:0;left:0;width:100%;height:200px;z-index:20;background-color:hsla(0,0%,100%,.3)}.logo-section[data-v-7caba744]{position:absolute;top:14px;left:42px;width:273px;height:172px}.logo-image[data-v-7caba744]{width:100%;height:100%;-o-object-fit:contain;object-fit:contain}.datetime-section[data-v-7caba744]{position:absolute;top:42px;left:777px;width:1240px;height:116px;font-family:Inter,sans-serif;font-weight:200;color:#fff;font-size:96px;line-height:1.21}.weather-section[data-v-7caba744]{position:absolute;top:42px;right:110px;width:200px;height:116px;display:flex;align-items:center;justify-content:center;gap:15px}.weather-icon[data-v-7caba744]{font-size:96px;line-height:1;display:flex;align-items:center;justify-content:center}.weather-temp[data-v-7caba744]{font-family:Inter,sans-serif;font-weight:200;color:#fff;font-size:96px;line-height:1.21;white-space:nowrap}.bottom-bar[data-v-001050e4]{position:absolute;bottom:93px;left:608px;width:944px;height:430px;display:flex;flex-direction:column;align-items:center;z-index:20}.ai-section[data-v-001050e4]{position:absolute;top:0;left:283px;width:378px;height:168px;display:flex;justify-content:center;align-items:center}.ai-button[data-v-001050e4]{width:100%;height:100%}.ai-content[data-v-001050e4]{display:flex;align-items:center;justify-content:center;gap:20px}.ai-icon[data-v-001050e4]{width:65px;height:65px;-o-object-fit:contain;object-fit:contain}.ai-text[data-v-001050e4]{font-family:Inter,sans-serif;font-weight:400;font-size:56px;line-height:1;color:#fff;text-transform:lowercase}.nav-buttons[data-v-001050e4]{position:absolute;bottom:0;left:0;right:0;height:214px;display:flex;justify-content:center;align-items:center;gap:35px}.nav-button[data-v-001050e4]{width:189px;height:100%;position:relative;transition:all .3s ease}.lang-content[data-v-001050e4]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.lang-text[data-v-001050e4]{font-family:Inter,sans-serif;font-weight:500;font-size:36px;line-height:1;color:#fff;transition:all .3s ease}.nav-button[data-v-001050e4]:hover{background:hsla(0,0%,100%,.1)}.nav-button:hover .lang-text[data-v-001050e4]{color:#fff;text-shadow:0 0 4px hsla(0,0%,100%,.3)}.home-icon[data-v-001050e4]{width:48px;height:48px;color:#fff;transition:all .3s ease}.nav-button:hover .home-icon[data-v-001050e4]{color:#0ef;filter:drop-shadow(0 0 8px rgba(0,238,255,.5))}.background-image[data-v-4665a938]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:0;transition:background-image .5s ease-in-out}.background-image.with-offset[data-v-4665a938]{left:-360px;width:2880px;height:3840px}.function-grid[data-v-44a8acf5]{display:grid;grid-template-columns:repeat(3,550px);grid-template-rows:repeat(3,550px);gap:104px 86px;width:1822px;margin:0 auto}.text-center[data-v-44a8acf5]{text-align:center;line-height:1.1}.app-title[data-v-44a8acf5]{font-family:Inter,sans-serif;font-weight:500;color:#fff;font-size:128px;line-height:1.21;margin:0}

/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,::backdrop,:after,:before{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial}}}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.container{width:100%}.contents{display:contents}.flex{display:flex}.hidden{display:none}.flex-shrink{flex-shrink:1}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.resize{resize:both}.border{border-style:var(--tw-border-style);border-width:1px}.text-center{text-align:center}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.invert{--tw-invert:invert(100%)}.filter,.invert{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-filter{-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}:root{--color-primary:#0ef;--color-danger:#ff5252;--color-white:#fff;--color-background:#016513;--glass-bg-light:#ffffff05;--glass-bg-normal:#ffffff1a;--glass-bg-medium:#ffffff26;--glass-bg-heavy:#fff3;--glass-bg-extra:#ffffff40;--glass-border-light:#fff3;--glass-border-medium:#ffffff4d;--glass-border-primary:var(--color-primary);--glass-border-danger:var(--color-danger);--spacing-xs:8px;--spacing-sm:16px;--spacing-md:24px;--spacing-lg:48px;--spacing-xl:96px;--radius-xs:10px;--radius-sm:15px;--radius-md:20px;--radius-lg:26px;--radius-xl:40px;--shadow-glass-inset:inset 0px 2px 4px 0px #fff6,inset 0px -2px 4px 0px #0003;--shadow-glass-inset-hover:inset 0px 2px 4px 0px #ffffff80,inset 0px -2px 4px 0px #0000004d;--shadow-drop-light:2px 2px 4px #00000040;--shadow-drop-medium:4px 4px 8px #00000026;--shadow-drop-heavy:0px 8px 16px #0003;--shadow-drop-extra:0px 10px 25px #0000004d;--shadow-button:4px 4px 4px 0px #00000040;--shadow-button-hover:0px 10px 25px #0003;--shadow-primary-glow:0px 8px 16px 0px #00eeff4d;--shadow-danger-glow:0px 8px 16px 0px #ff525266;--border-gradient-glass:linear-gradient(135deg,#ffffff0d,#fff 50%,#ffffff0d);--blur-light:blur(10px);--blur-medium:blur(20px);--font-family-primary:"Inter",sans-serif;--font-weight-light:200;--font-weight-normal:300;--font-weight-medium:500;--font-weight-semibold:600;--line-height-standard:1.21;--font-size-xs:24px;--font-size-sm:36px;--font-size-md:48px;--font-size-lg:64px;--font-size-xl:72px;--font-size-2xl:96px;--font-size-3xl:128px;--transition-standard:all .3s ease;--transition-fast:all .2s ease;--transform-lift-sm:translateY(-4px);--transform-lift-md:translateY(-8px);--transform-lift-lg:translateY(-12px);--transform-press:translateY(-2px);--transform-scale-hover:scale(1.05)}.glass-effect{-webkit-backdrop-filter:var(--blur-light);backdrop-filter:var(--blur-light)}.glass-effect,.glass-effect-heavy{border:3px solid var(--glass-border-light);box-shadow:var(--shadow-glass-inset)}.glass-effect-heavy{-webkit-backdrop-filter:var(--blur-medium);backdrop-filter:var(--blur-medium)}.hover-lift{transition:var(--transition-standard);cursor:pointer}.hover-lift:hover{transform:var(--transform-lift-sm);box-shadow:var(--shadow-glass-inset-hover),var(--shadow-drop-heavy)}.hover-lift-md:hover{transform:var(--transform-lift-md);box-shadow:var(--shadow-glass-inset-hover),var(--shadow-drop-extra)}.button-glass{background:var(--glass-bg-light);border:5px solid #0000;-o-border-image:var(--border-gradient-glass)1;border-image:var(--border-gradient-glass)1;box-shadow:var(--shadow-glass-inset);transition:var(--transition-standard);cursor:pointer}.button-glass:hover{transform:var(--transform-lift-md);box-shadow:var(--shadow-glass-inset-hover),var(--shadow-button-hover)}.button-glass:active{transform:var(--transform-press)}.text-primary{font-family:var(--font-family-primary);color:var(--color-white);line-height:var(--line-height-standard)}.text-light{font-weight:var(--font-weight-light)}.text-normal{font-weight:var(--font-weight-normal)}.text-medium{font-weight:var(--font-weight-medium)}.text-semibold{font-weight:var(--font-weight-semibold)}.state-primary{border-color:var(--color-primary);box-shadow:var(--shadow-primary-glow),var(--shadow-glass-inset);background:#0ef3}.state-danger{background:#ff525226;border-color:#ff525266}.state-danger:hover{background:#ff525240;border-color:#ff525299}.state-danger.active{border-color:var(--color-danger);box-shadow:var(--shadow-danger-glow),var(--shadow-glass-inset);background:#ff52524d}.card-base{background:var(--glass-bg-normal);border:3px solid var(--glass-border-light);-webkit-backdrop-filter:var(--blur-light);backdrop-filter:var(--blur-light);border-radius:var(--radius-md);transition:var(--transition-standard);cursor:pointer;box-shadow:var(--shadow-glass-inset);justify-content:center;align-items:center;display:flex;position:relative}.card-base:hover{background:var(--glass-bg-medium);border-color:var(--glass-border-medium);transform:var(--transform-lift-sm);box-shadow:var(--shadow-glass-inset-hover),var(--shadow-drop-heavy)}.icon-container,.icon-placeholder{justify-content:center;align-items:center;display:flex}.icon-placeholder{background:var(--glass-bg-medium);border-radius:var(--radius-xs);font-weight:var(--font-weight-semibold);color:var(--color-white)}@media screen and (max-width:2160px){.responsive-scale{transform:scale(.0463vw);transform-origin:0 0}}.scrollable-content-area{z-index:10;scrollbar-width:thin;scrollbar-color:#fff6 #ffffff1a;position:absolute;left:0;right:0;overflow:hidden auto}.scrollable-content-area::-webkit-scrollbar{width:12px}.scrollable-content-area::-webkit-scrollbar-track{background:#ffffff1a;border-radius:6px}.scrollable-content-area::-webkit-scrollbar-thumb{background:#fff6 padding-box content-box;border:2px solid #0000;border-radius:6px;min-height:30px}.scrollable-content-area::-webkit-scrollbar-thumb:hover{background:#fff9 padding-box content-box}.scrollable-content-area::-webkit-scrollbar-track:vertical{background:#ffffff1a}.main-content-container{box-sizing:border-box;width:100%;min-height:100%;position:relative}.main-content-container--home{padding:169px}.main-content-container--shop{padding:112px}.main-content-container--detail{padding:60px 64px}.main-content-container--standard{padding:60px 120px}.responsive-page-container{width:2160px;height:3840px;font-family:var(--font-family-primary);transform-origin:0 0;background-color:var(--color-background,#016513);position:relative;overflow:hidden}.page-title-fixed{z-index:15;font-family:var(--font-family-primary);font-weight:var(--font-weight-medium);color:var(--color-white);font-size:var(--font-size-3xl);line-height:var(--line-height-standard);margin:0;position:absolute;top:340px}.page-title-fixed--home{width:342px;height:155px;left:909px}.page-title-fixed--shop{width:316px;height:155px;left:922px}.page-title-fixed--facility{width:436px;left:862px}.page-title-fixed--facility,.page-title-fixed--food{text-align:center;justify-content:center;align-items:center;height:155px;display:flex}.page-title-fixed--food{width:auto;left:50%;transform:translate(-50%)}.page-title-fixed--office{width:436px;left:862px}.page-title-fixed--office,.page-title-fixed--transport{text-align:center;justify-content:center;align-items:center;height:155px;display:flex}.page-title-fixed--transport{width:auto;left:50%;transform:translate(-50%)}.page-title-fixed--poster{text-align:center;justify-content:center;align-items:center;width:436px;height:155px;display:flex;left:862px}.page-title-fixed--about,.page-title-fixed--ai,.page-title-fixed--video,.page-title-fixed--web,.page-title-fixed--worldtime{text-align:center;justify-content:center;align-items:center;width:auto;height:155px;display:flex;left:50%;transform:translate(-50%)}*{-webkit-user-select:none;-moz-user-select:none;user-select:none}input,textarea{-webkit-user-select:text;-moz-user-select:text;user-select:text;-webkit-touch-callout:default}*{-webkit-touch-callout:none;-webkit-tap-highlight-color:transparent}*,img{-webkit-user-drag:none;-khtml-user-drag:none;-moz-user-drag:none;-o-user-drag:none;user-drag:none}img{pointer-events:none}.swiper-container,.swiper-container img,.swiper-slide,.swiper-slide img,.swiper-wrapper,a,button,input,select,textarea{pointer-events:auto}body{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;-khtml-user-select:none;overscroll-behavior:none;-webkit-overflow-scrolling:touch}*{outline:none}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}