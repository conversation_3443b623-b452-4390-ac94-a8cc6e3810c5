#!/usr/bin/env node

/**
 * 测试Shop页面性能优化
 */

const fs = require('fs')
const path = require('path')

console.log('🛍️ 检查Shop页面性能优化...\n')

let allGood = true

function checkFileContent(filePath, searchTexts, description) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${description}: 文件不存在 - ${filePath}`)
    allGood = false
    return false
  }
  
  const content = fs.readFileSync(filePath, 'utf8')
  const results = searchTexts.map(text => ({
    text,
    found: content.includes(text)
  }))
  
  const allFound = results.every(r => r.found)
  console.log(`${allFound ? '✅' : '❌'} ${description}`)
  
  if (!allFound) {
    results.forEach(r => {
      if (!r.found) {
        console.log(`   ❌ 缺少: ${r.text}`)
        allGood = false
      }
    })
  }
  
  return allFound
}

function checkFileNotContains(filePath, searchTexts, description) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${description}: 文件不存在 - ${filePath}`)
    allGood = false
    return false
  }
  
  const content = fs.readFileSync(filePath, 'utf8')
  const results = searchTexts.map(text => ({
    text,
    found: content.includes(text)
  }))
  
  const noneFound = results.every(r => !r.found)
  console.log(`${noneFound ? '✅' : '❌'} ${description}`)
  
  if (!noneFound) {
    results.forEach(r => {
      if (r.found) {
        console.log(`   ❌ 仍包含: ${r.text}`)
        allGood = false
      }
    })
  }
  
  return noneFound
}

console.log('📱 检查Shop页面优化...')

// 检查Shop.vue优化
checkFileContent(
  'src/views/Shop.vue',
  [
    'ShopCardOptimized',
    'shop-performance.css',
    'soundService.setEnabled(false)',
    'overflow-y: scroll',
    'transform: none'
  ],
  'Shop.vue性能优化'
)

// 检查是否移除了性能杀手
checkFileNotContains(
  'src/views/Shop.vue',
  [
    'will-change: scroll-position',
    'contain: layout style paint',
    'backdrop-filter: blur',
    'transition: all'
  ],
  'Shop.vue移除性能杀手'
)

console.log('\n🎴 检查优化的ShopCard...')

// 检查ShopCardOptimized组件
checkFileContent(
  'src/components/ShopCardOptimized.vue',
  [
    'transition: none',
    'transform: none',
    'will-change: auto',
    'contain: none',
    'backdrop-filter: none'
  ],
  'ShopCardOptimized组件优化'
)

// 检查走马灯是否保留
checkFileContent(
  'src/components/ShopCardOptimized.vue',
  [
    'marquee-scroll',
    '@keyframes marquee-scroll',
    'enableMarquee'
  ],
  'ShopCardOptimized保留走马灯'
)

console.log('\n🎨 检查CSS优化...')

// 检查极端性能CSS
checkFileContent(
  'src/styles/shop-performance.css',
  [
    'transition: none !important',
    'animation: none !important',
    'transform: none !important',
    'backdrop-filter: none !important',
    'box-shadow: none !important'
  ],
  'shop-performance.css极端优化'
)

// 检查走马灯例外
checkFileContent(
  'src/styles/shop-performance.css',
  [
    '.marquee-text',
    'animation: revert !important',
    'transition: revert !important'
  ],
  'shop-performance.css保留走马灯'
)

console.log('\n🔊 检查音效禁用...')

// 检查音效禁用逻辑
checkFileContent(
  'src/views/Shop.vue',
  [
    'soundService.setEnabled(false)',
    'beforeDestroy',
    'soundService.setEnabled(true)'
  ],
  'Shop页面音效控制'
)

console.log('\n📊 性能优化总结:')

if (allGood) {
  console.log('🎉 Shop页面性能优化检查通过！')
  
  console.log('\n✅ 已实施的优化:')
  console.log('• 移除所有CSS动效和过渡')
  console.log('• 禁用硬件加速和GPU优化')
  console.log('• 简化滚动容器')
  console.log('• 隐藏滚动条')
  console.log('• 禁用Shop页面音效')
  console.log('• 使用极简化的卡片组件')
  console.log('• 保留走马灯动画（唯一例外）')
  
  console.log('\n🎯 预期改进:')
  console.log('• 滚动FPS提升到接近60')
  console.log('• 消除滚动卡顿')
  console.log('• 减少CPU和GPU负载')
  console.log('• 降低内存使用')
  
  console.log('\n📱 测试步骤:')
  console.log('1. 运行: npm run build:prod')
  console.log('2. 运行: npm run build:apk:optimized')
  console.log('3. 在Android Studio中构建APK')
  console.log('4. 测试Shop页面滚动性能')
  
  console.log('\n🔧 如果仍有卡顿:')
  console.log('• 可以禁用走马灯: enableMarquee="false"')
  console.log('• 减少商店数量进行测试')
  console.log('• 检查设备硬件性能')
  
} else {
  console.log('❌ 发现优化问题，请检查上述错误')
  
  console.log('\n🔧 建议:')
  console.log('1. 确保所有文件都已正确创建')
  console.log('2. 检查import路径是否正确')
  console.log('3. 重新运行优化脚本')
}

console.log('\n💡 额外提示:')
console.log('• 这是极端性能优化，牺牲了视觉效果换取流畅度')
console.log('• 只有走马灯动画被保留')
console.log('• 如果需要恢复效果，可以逐步重新启用CSS属性')

process.exit(allGood ? 0 : 1)
