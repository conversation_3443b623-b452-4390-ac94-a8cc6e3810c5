<template>
  <BaseCard
    :title="companyName"
    :subtitle="roomNumber"
    :icon-src="logoSrc"
    size="extra-large"
    variant="office"
    layout="horizontal"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import BaseCard from './BaseCard.vue'

@Component({
  components: {
    BaseCard
  }
})
export default class OfficeCard extends Vue {
  @Prop({ required: true })
  companyName!: string
  
  @Prop({ required: true })
  roomNumber!: string
  
  @Prop()
  logoSrc?: string
}
</script> 