import PocketBase from 'pocketbase'
import { ChatMessage, ChatSession } from './geminiApi'

// PocketBase 实例
const pb = new PocketBase(process.env.VUE_APP_POCKETBASE_URL || 'https://base.bwaiwork.xyz')

// 数据库表名
const SESSIONS_TABLE = 'ai_sessions_byemap'
const CONVERSATIONS_TABLE = 'ai_conversations_byemap'

export interface PocketBaseSession {
  id?: string
  title: string
  summary?: string
  created?: string
  updated?: string
  _byemap?: string
}

export interface PocketBaseConversation {
  id?: string
  session_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model: string
  metadata?: any
  created?: string
  _byemap?: string
}

// PocketBase RecordModel 扩展类型
export interface SessionRecord extends PocketBaseSession {
  id: string
  created: string
  updated: string
  collectionId: string
  collectionName: string
  expand?: any
}

export interface ConversationRecord extends PocketBaseConversation {
  id: string
  created: string
  updated: string
  collectionId: string
  collectionName: string
  expand?: any
}

/**
 * 创建新的会话记录
 * @param title 会话标题
 * @param summary 会话摘要（可选）
 * @returns 创建的会话记录
 */
export async function createSession(
  title: string,
  summary?: string
): Promise<PocketBaseSession> {
  try {
    const sessionData: PocketBaseSession = {
      title,
      summary: summary || '',
      _byemap: JSON.stringify({
        source: 'ai_chat',
        timestamp: new Date().toISOString(),
        device: 'kiosk'
      })
    }

    const record = await pb.collection(SESSIONS_TABLE).create(sessionData)
return record as unknown as PocketBaseSession
  } catch (error) {
    console.error('创建会话失败:', error)
    throw error
  }
}

/**
 * 保存对话消息
 * @param sessionId 会话ID
 * @param role 角色（user/assistant）
 * @param content 消息内容
 * @param model 使用的模型名称
 * @param metadata 额外的元数据
 * @returns 保存的消息记录
 */
export async function saveMessage(
  sessionId: string,
  role: 'user' | 'assistant',
  content: string,
  model: string = 'gemini-2.0-flash-exp',
  metadata?: any
): Promise<PocketBaseConversation> {
  try {
    const messageData: PocketBaseConversation = {
      session_id: sessionId,
      role,
      content,
      model,
      metadata: metadata || {},
      _byemap: JSON.stringify({
        source: 'ai_chat',
        timestamp: new Date().toISOString(),
        device: 'kiosk',
        character_count: content.length
      })
    }

    const record = await pb.collection(CONVERSATIONS_TABLE).create(messageData)
return record as unknown as PocketBaseConversation
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('保存消息请求被中止（可能是页面切换）')
      // 不要抛出 AbortError，让它静默失败
      return {} as PocketBaseConversation
    }
    console.error('保存消息失败:', error)
    throw error
  }
}

/**
 * 获取会话历史
 * @param sessionId 会话ID
 * @returns 会话消息列表
 */
export async function getSessionMessages(
  sessionId: string
): Promise<PocketBaseConversation[]> {
  try {
    const records = await pb.collection(CONVERSATIONS_TABLE).getFullList({
      filter: `session_id = "${sessionId}"`,
      sort: 'created'
    })
return records as unknown as PocketBaseConversation[]
  } catch (error) {
    console.error('获取会话历史失败:', error)
    throw error
  }
}

/**
 * 完整的对话保存流程
 * 包括用户消息和AI回复
 * @param userMessage 用户消息
 * @param aiResponse AI回复
 * @param sessionTitle 会话标题
 * @returns 会话ID
 */
export async function saveConversation(
  userMessage: string,
  aiResponse: string,
  sessionTitle?: string
): Promise<string> {
  try {
    // 创建新会话
    const title = sessionTitle || generateSessionTitle(userMessage)
    const session = await createSession(title, generateSessionSummary(userMessage, aiResponse))
    
    const sessionId = session.id!

    // 保存用户消息
    await saveMessage(sessionId, 'user', userMessage)
    
    // 保存AI回复
    await saveMessage(sessionId, 'assistant', aiResponse)

    return sessionId
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('保存对话请求被中止（可能是页面切换）')
      // 不要抛出 AbortError，让它静默失败
      return ''
    }
    console.error('保存对话失败:', error)
    throw error
  }
}

/**
 * 生成会话标题
 * @param firstMessage 第一条消息
 * @returns 会话标题
 */
function generateSessionTitle(firstMessage: string): string {
  // 生成基于内容的标题
  const title = firstMessage.length > 30 
    ? firstMessage.substring(0, 30) + '...'
    : firstMessage
  
  return title || '新对话'
}

/**
 * 生成会话摘要
 * @param userMessage 用户消息
 * @param aiResponse AI回复
 * @returns 会话摘要
 */
function generateSessionSummary(userMessage: string, aiResponse: string): string {
  return `用户询问: ${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}`
}

/**
 * 批量保存对话（用于有历史记录的对话）
 * @param messages 消息列表
 * @param sessionTitle 会话标题
 * @returns 会话ID
 */
export async function saveBatchConversation(
  messages: ChatMessage[],
  sessionTitle?: string
): Promise<string> {
  try {
    if (messages.length === 0) {
      throw new Error('消息列表不能为空')
    }

    const firstUserMessage = messages.find(msg => msg.role === 'user')
    const title = sessionTitle || generateSessionTitle(firstUserMessage?.content || '对话')
    
    // 创建会话
    const session = await createSession(title)
    const sessionId = session.id!

    // 批量保存消息
    const savePromises = messages
      .filter(msg => msg.role !== 'system') // 过滤系统消息
      .map(msg => saveMessage(sessionId, msg.role as 'user' | 'assistant', msg.content))

    await Promise.all(savePromises)

    return sessionId
  } catch (error) {
    console.error('批量保存对话失败:', error)
    throw error
  }
}

/**
 * 测试PocketBase连接
 * @returns 连接状态
 */
export async function testConnection(): Promise<boolean> {
  try {
    await pb.health.check()
    return true
  } catch (error) {
    console.error('PocketBase连接失败:', error)
    return false
  }
}
