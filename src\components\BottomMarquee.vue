<template>
  <div class="bottom-marquee">
    <div class="marquee-container">
      <div class="marquee-content" :style="{ transform: `translateX(${translateX}px)` }">
        <span class="marquee-text">{{ marqueeText }}</span>
        <span class="marquee-text">{{ marqueeText }}</span>
        <span class="marquee-text">{{ marqueeText }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class BottomMarquee extends Vue {
  translateX = 0
  animationSpeed = 0.5 // 像素/帧，降低速度
  marqueeText = "t Yahoo Finance, you get free_stock_quotes, up-to-date_n"
  
  private animationId: number | null = null

  mounted() {
    this.startAnimation()
  }

  beforeDestroy() {
    this.stopAnimation()
  }

  startAnimation() {
    const animate = () => {
      this.translateX -= this.animationSpeed
      
      // 当第一个文本完全移出视窗时，重置位置
      const textWidth = 2100 // 根据设计稿的文字宽度
      if (this.translateX <= -textWidth) {
        this.translateX = 0
      }
      
      this.animationId = requestAnimationFrame(animate)
    }
    
    this.animationId = requestAnimationFrame(animate)
  }

  stopAnimation() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }
}
</script>

<style scoped>
.bottom-marquee {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px; /* 根据设计稿的底部区域高度 */
  background-color: rgba(0, 0, 0, 0.4); /* 根据设计稿的背景色 */
  z-index: 20;
  overflow: hidden;
}

.marquee-container {
  position: absolute;
  top: 47px; /* 根据设计稿的y位置 3687 - 3640 = 47 */
  left: 10px; /* 根据设计稿的x位置 */
  width: 2100px; /* 根据设计稿的文字宽度 */
  height: 97px; /* 根据设计稿的文字高度 */
  overflow: hidden;
}

.marquee-content {
  display: flex;
  white-space: nowrap;
  transition: transform 0.1s linear;
}

.marquee-text {
  font-family: 'Inter', sans-serif;
  font-weight: 200; /* 根据设计稿的字重 */
  font-size: 80px; /* 根据设计稿的字体大小 */
  line-height: 1.21;
  color: white;
  white-space: nowrap;
  margin-right: 200px; /* 增加文字之间的间距 */
}

/* 响应式缩放 - 基于4K宽度 */
@media screen and (max-width: 2160px) {
  .bottom-marquee {
    transform: scale(calc(100vw / 2160));
    transform-origin: left bottom;
  }
}

/* 确保在较小屏幕上也能正常显示 */
@media screen and (max-width: 1920px) {
  .marquee-text {
    font-size: calc(80px * (100vw / 2160));
  }
  
  .marquee-container {
    width: calc(2100px * (100vw / 2160));
    height: calc(97px * (100vw / 2160));
    top: calc(47px * (100vw / 2160));
    left: calc(10px * (100vw / 2160));
  }
}
</style> 