# eMap-AI 项目代码索引

## 项目概述
eMap-AI 是一个基于 Vue.js 2.7 + TypeScript 的移动端应用，支持 Capacitor 打包为 Android APK。项目采用 4K 竖屏设计，提供商店导航、设施查询、视频播放等功能。

## 项目结构

### 根目录文件
- `package.json` - 项目依赖和脚本配置
- `capacitor.config.ts` - Capacitor 移动端配置
- `vue.config.js` - Vue CLI 构建配置，包含图片和字体优化
- `tailwind.config.js` - Tailwind CSS 配置
- `tsconfig.json` - TypeScript 配置，支持装饰器和Vue文件
- `postcss.config.js` - PostCSS 配置

### 源代码结构 (`src/`)

#### 入口文件
- **`main.ts`** - 应用入口，Vue 实例初始化
- **`App.vue`** - 根组件，包含路由视图

#### 组件目录 (`components/`)
- **`BackgroundImage.vue`** - 背景图片组件，全局使用
- **`BottomBar.vue`** - 底部导航栏，包含语言切换和AI按钮
- **`TopBar.vue`** - 顶部状态栏，显示实时时间、天气信息
- **`GlassButton.vue`** - 玻璃态按钮组件，支持多种尺寸和样式
- **`BaseCard.vue`** - 基础卡片组件
- **`BaseIcon.vue`** - 基础图标组件
- **`ShopCard.vue`** - 商店卡片组件，显示商店信息
- **`FacilityCard.vue`** - 设施卡片组件
- **`OfficeCard.vue`** - 办公室卡片组件
- **`TransportButton.vue`** - 交通按钮组件

#### 页面视图 (`views/`)

##### 主要功能页面
- **`Home.vue`** - 首页，显示功能菜单网格
- **`Shop.vue`** - 商店列表页，4x3网格布局显示商店
- **`ShopDetail.vue`** - 商店详情页，显示地图和商店信息
- **`AISearch.vue`** - AI搜索页面，支持实时商店搜索

##### 其他功能页面
- **`About.vue`** - 关于页面
- **`Food.vue`** - 餐饮页面
- **`Office.vue`** - 办公室页面
- **`Facility.vue`** - 设施页面
- **`Transport.vue`** - 交通页面
- **`Video.vue`** - 视频页面，支持双视频播放
- **`Web.vue`** - 网页页面
- **`WorldTime.vue`** - 世界时间页面
- **`Poster.vue`** - 海报页面

#### 工具目录

##### 混入 (`mixins/`)
- **`ScalingMixin.ts`** - 缩放混入，提供响应式缩放功能
- **`ResponsiveMixin.ts`** - 响应式混入

##### 路由 (`router/`)
- **`index.ts`** - 路由配置，定义所有页面路由

##### 状态管理 (`store/`)
- **`index.ts`** - Vuex 状态管理配置

##### 样式 (`styles/`)
- **`main.css`** - 全局样式，Tailwind CSS 导入
- **`design-system.css`** - 设计系统样式定义

#### 类型声明
- **`shims-vue.d.ts`** - Vue 文件类型声明
- **`shims-tsx.d.ts`** - TSX 支持类型声明，包含JSX命名空间

### 资源目录 (`public/`)

#### 图片资源 (`public/img/`)
- **设施图标** (`facilities/`) - 电梯、洗手间、婴儿室等设施图标
- **商店图标** (`shops/`) - 各品牌商店的 logo 图片
- **交通图标** (`transports/`) - 公交、地铁等交通工具图标
- **海报图片** (`posters/`) - 宣传海报图片
- **通用图标** - AI图标、背景图、地图等

## 技术栈

### 前端框架
- **Vue.js 2.7.16** - 主框架，支持Composition API
- **TypeScript 5.8.3** - 类型安全，装饰器支持
- **Vue Router 3.6.5** - 路由管理
- **Vuex 3.6.2** - 状态管理
- **vue-property-decorator 9.1.2** - 类组件装饰器
- **vue-class-component 7.2.6** - 类组件支持

### UI 和样式
- **Tailwind CSS 4.1.11** - 原子化 CSS 框架
- **PostCSS 8.5.6** - CSS 后处理器
- **Inter 字体** - 统一字体风格
- **玻璃态设计** - 现代化视觉效果

### 移动端支持
- **Capacitor 7.4.0** - 跨平台移动应用框架
- **Android 平台** - 原生 APK 打包支持
- **Splash Screen & Status Bar** - 启动页和状态栏插件

### 构建工具
- **Vue CLI 5.0.8** - 项目构建工具
- **Webpack 5.99.9** - 模块打包器
- **TypeScript Loader 8.4.0** - TS编译器

### 其他工具
- **Swiper 5.4.5** - 轮播图组件
- **vue-awesome-swiper 4.1.1** - Vue轮播封装

## 核心功能模块

### 1. 导航系统
- **主导航**: Home页面的3x3功能网格
- **底部导航**: 语言切换、Home按钮、AI搜索
- **智能返回**: 支持浏览器和原生返回按钮

### 2. 商店系统
- **商店列表**: 4x4网格展示12个商店
- **商店搜索**: 实时搜索和筛选功能
- **商店详情**: 地图展示和详细信息

### 3. 多媒体播放
- **视频播放**: 双视频框，支持循环播放
- **YouTube集成**: 嵌入式播放器

### 4. 响应式设计
- **4K基准**: 2160x3840分辨率设计
- **缩放适配**: ScalingMixin提供自适应缩放
- **移动优化**: Capacitor原生体验

### 5. 国际化支持
- **多语言**: 英文、西班牙文、中文支持
- **语言切换**: 底部导航栏快速切换

### 6. 实时功能
- **时间显示**: TopBar组件每秒更新时间
- **天气信息**: 实时温度显示
- **AI搜索**: 智能搜索功能

## 开发和部署

### 开发命令
```bash
npm run dev          # 开发服务器
npm run build        # 生产构建
npm run build:prod   # 生产环境构建
npm run clean        # 清理缓存
```

### 移动端打包
```bash
npm run cap:init           # 初始化Capacitor
npm run cap:add            # 添加Android平台
npm run cap:sync:android   # 同步到Android
npm run cap:open           # 打开Android Studio
npm run build:apk          # 构建APK
npm run build:apk:release  # 构建发布版APK
```

## 代码规范

### 命名约定
- **文件名**: PascalCase (组件) / camelCase (工具)
- **组件名**: PascalCase
- **变量名**: camelCase
- **API端点**: snake_case
- **CSS类名**: kebab-case

### TypeScript配置
- **严格模式**: 启用所有严格类型检查
- **装饰器支持**: experimentalDecorators: true
- **JSX支持**: jsx: preserve
- **模块解析**: node方式，支持@/路径别名

### 字体管理
- **页面标题**: 128px
- **小按钮文字**: 64px  
- **中按钮文字**: 96px
- **时间显示**: 96px (TopBar)
- **标准行高**: 1.21
- **字体族**: 'Inter', sans-serif

### 样式规范
- **固定像素值**: 避免使用百分比，统一使用px
- **玻璃态效果**: 统一的 backdrop-filter 和阴影
- **颜色主题**: 绿色渐变背景 (#016513 - #0B5D14)
- **透明背景**: TopBar使用 rgba(255, 255, 255, 0.3)

## 构建优化

### Webpack配置
- **代码分割**: vendor和common chunk分离
- **图片优化**: 4KB以下内联，支持webp
- **字体优化**: 4KB以下内联
- **性能优化**: 关闭生产环境hints

### 开发体验
- **TypeScript**: transpileOnly模式提升编译速度
- **热重载**: 开发服务器支持热更新
- **Source Maps**: 开发环境启用，生产环境关闭

## 常见问题解决

### TypeScript Linter错误
如果遇到 `__VLS_intrinsicElements` 相关错误：
1. 确保IDE使用正确的TypeScript版本 (5.8.3)
2. 重启TypeScript服务：Cmd/Ctrl + Shift + P → "TypeScript: Restart TS Server"
3. 确保vue-template-compiler版本与vue版本匹配 (2.7.16)

### 构建问题
- **内存不足**: 增加Node.js内存限制 `--max-old-space-size=4096`
- **依赖冲突**: 使用 `npm run clean` 清理缓存后重新安装

## 项目特色

1. **4K设计**: 超高分辨率竖屏设计 (2160x3840)
2. **玻璃态UI**: 现代化毛玻璃视觉效果
3. **原生体验**: Capacitor提供原生应用体验
4. **智能搜索**: 实时搜索和筛选功能
5. **多媒体支持**: YouTube视频集成播放
6. **国际化**: 多语言本地化支持
7. **实时更新**: 时间、天气信息实时显示
8. **响应式适配**: 支持多种屏幕尺寸

---

*更新时间: 2025-01-27*
*版本: 1.0.1*
*技术栈: Vue 2.7.16 + TypeScript 5.8.3 + Tailwind CSS 4.1.11* 