/* eMap 设计系统 - Design System */
/* 基于现有组件样式提取的设计令牌 */

:root {
  /* 颜色系统 Color System */
  --color-primary: #00EEFF;
  --color-danger: #FF5252;
  --color-white: #FFFFFF;
  --color-background: #016513;

  /* 玻璃效果背景色 Glass Background Colors */
  --glass-bg-light: rgba(255, 255, 255, 0.02);
  --glass-bg-normal: rgba(255, 255, 255, 0.1);
  --glass-bg-medium: rgba(255, 255, 255, 0.15);
  --glass-bg-heavy: rgba(255, 255, 255, 0.2);
  --glass-bg-extra: rgba(255, 255, 255, 0.25);

  /* 边框颜色 Border Colors */
  --glass-border-light: rgba(255, 255, 255, 0.2);
  --glass-border-medium: rgba(255, 255, 255, 0.3);
  --glass-border-primary: var(--color-primary);
  --glass-border-danger: var(--color-danger);

  /* 间距系统 Spacing System */
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 48px;
  --spacing-xl: 96px;

  /* 圆角 Border Radius */
  --radius-xs: 10px;
  --radius-sm: 15px;
  --radius-md: 20px;
  --radius-lg: 26px;
  --radius-xl: 40px;

  /* 阴影效果 Shadow Effects */
  --shadow-glass-inset: inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
  --shadow-glass-inset-hover: inset 0px 2px 4px 0px rgba(255, 255, 255, 0.5),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.3);
  --shadow-drop-light: 2px 2px 4px rgba(0, 0, 0, 0.25);
  --shadow-drop-medium: 4px 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-drop-heavy: 0px 8px 16px rgba(0, 0, 0, 0.2);
  --shadow-drop-extra: 0px 10px 25px rgba(0, 0, 0, 0.3);
  --shadow-button: 4px 4px 4px 0px rgba(0, 0, 0, 0.25);
  --shadow-button-hover: 0px 10px 25px rgba(0, 0, 0, 0.2);

  /* 特殊效果阴影 Special Effect Shadows */
  --shadow-primary-glow: 0px 8px 16px 0px rgba(0, 238, 255, 0.3);
  --shadow-danger-glow: 0px 8px 16px 0px rgba(255, 82, 82, 0.4);

  /* 边框渐变 Border Gradients */
  --border-gradient-glass: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0.05) 100%);

  /* 背景模糊 Backdrop Blur */
  --blur-light: blur(10px);
  --blur-medium: blur(20px);

  /* 字体系统 Typography System */
  --font-family-primary: 'Inter', sans-serif;
  --font-weight-light: 200;
  --font-weight-normal: 300;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --line-height-standard: 1.21;

  /* 字体大小 Font Sizes */
  --font-size-xs: 24px;
  --font-size-sm: 36px;
  --font-size-md: 48px;
  --font-size-lg: 64px;
  --font-size-xl: 72px;
  --font-size-2xl: 96px;
  --font-size-3xl: 128px;

  /* 过渡效果 Transitions */
  --transition-standard: all 0.3s ease;
  --transition-fast: all 0.2s ease;

  /* 变换效果 Transform Effects */
  --transform-lift-sm: translateY(-4px);
  --transform-lift-md: translateY(-8px);
  --transform-lift-lg: translateY(-12px);
  --transform-press: translateY(-2px);
  --transform-scale-hover: scale(1.05);
}

/* 公共样式类 Utility Classes */

/* 玻璃效果基础类 */
.glass-effect {
  backdrop-filter: var(--blur-light);
  border: 3px solid var(--glass-border-light);
  box-shadow: var(--shadow-glass-inset);
}

.glass-effect-heavy {
  backdrop-filter: var(--blur-medium);
  border: 3px solid var(--glass-border-light);
  box-shadow: var(--shadow-glass-inset);
}

/* 悬停效果类 */
.hover-lift {
  transition: var(--transition-standard);
  cursor: pointer;
}

.hover-lift:hover {
  transform: var(--transform-lift-sm);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-drop-heavy);
}

.hover-lift-md:hover {
  transform: var(--transform-lift-md);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-drop-extra);
}

/* 按钮基础效果 */
.button-glass {
  background: var(--glass-bg-light);
  border: 5px solid transparent;
  border-image: var(--border-gradient-glass) 1;
  box-shadow: var(--shadow-glass-inset);
  transition: var(--transition-standard);
  cursor: pointer;
}

.button-glass:hover {
  transform: var(--transform-lift-md);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-button-hover);
}

.button-glass:active {
  transform: var(--transform-press);
}

/* 字体样式类 */
.text-primary {
  font-family: var(--font-family-primary);
  color: var(--color-white);
  line-height: var(--line-height-standard);
}

.text-light {
  font-weight: var(--font-weight-light);
}

.text-normal {
  font-weight: var(--font-weight-normal);
}

.text-medium {
  font-weight: var(--font-weight-medium);
}

.text-semibold {
  font-weight: var(--font-weight-semibold);
}

/* 状态样式类 */
.state-primary {
  background: rgba(0, 238, 255, 0.2);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-primary-glow), var(--shadow-glass-inset);
}

.state-danger {
  background: rgba(255, 82, 82, 0.15);
  border-color: rgba(255, 82, 82, 0.4);
}

.state-danger:hover {
  background: rgba(255, 82, 82, 0.25);
  border-color: rgba(255, 82, 82, 0.6);
}

.state-danger.active {
  background: rgba(255, 82, 82, 0.3);
  border-color: var(--color-danger);
  box-shadow: var(--shadow-danger-glow), var(--shadow-glass-inset);
}

/* 卡片基础样式 */
.card-base {
  background: var(--glass-bg-normal);
  border: 3px solid var(--glass-border-light);
  backdrop-filter: var(--blur-light);
  border-radius: var(--radius-md);
  transition: var(--transition-standard);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: var(--shadow-glass-inset);
}

.card-base:hover {
  background: var(--glass-bg-medium);
  border-color: var(--glass-border-medium);
  transform: var(--transform-lift-sm);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-drop-heavy);
}

/* 图标容器样式 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-placeholder {
  background: var(--glass-bg-medium);
  border-radius: var(--radius-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  color: var(--color-white);
}

/* 响应式缩放 */
@media screen and (max-width: 2160px) {
  .responsive-scale {
    transform: scale(calc(100vw / 2160));
    transform-origin: left top;
  }
}

/* 滚动容器样式 Scrollable Container Styles */
.scrollable-content-area {
  position: absolute;
  left: 0;
  right: 0;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 10;

  /* 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) rgba(255, 255, 255, 0.1);
}

/* WebKit浏览器滚动条样式 */
.scrollable-content-area::-webkit-scrollbar {
  width: 12px;
}

.scrollable-content-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.scrollable-content-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  min-height: 30px;
}

.scrollable-content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
  background-clip: content-box;
}

.scrollable-content-area::-webkit-scrollbar-track:vertical {
  background: rgba(255, 255, 255, 0.1);
}

/* 主要内容区域样式 */
.main-content-container {
  position: relative;
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

/* 不同页面的内容区域padding变体 */
.main-content-container--home {
  padding: 169px;
}

.main-content-container--shop {
  padding: 112px;
}

.main-content-container--detail {
  padding: 60px 64px;
}

.main-content-container--standard {
  padding: 60px 120px;
}

.main-content-container--facility {
  padding: 0;
  position: relative;
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

/* 响应式布局容器样式 */
.responsive-page-container {
  position: relative;
  width: 2160px;
  height: 3840px;
  overflow: hidden;
  font-family: var(--font-family-primary);
  transform-origin: top left;

  /* 设置动态背景色，默认为绿色，可通过CSS变量动态更改 */
  background-color: var(--color-background, #016513);
}

/* 页面背景色变体 - 已移除，统一使用 responsive-page-container 的动态背景色 */
/* 
.page-bg--home {
  background-color: #016513;
}

.page-bg--shop {
  background-color: #0F04A9;
}

.page-bg--facility {
  background-color: #016513;
}

.page-bg--food {
  background-color: #DC2626;
}

.page-bg--office {
  background-color: #4A4A4A;
}

.page-bg--transport {
  background-color: #1E90FF;
}

.page-bg--poster {
  background-color: #FF6B6B;
}

.page-bg--video {
  background-color: #9B59B6;
}

.page-bg--web {
  background-color: #2C3E50;
}

.page-bg--worldtime {
  background-color: #E67E22;
}

.page-bg--about {
  background-color: #34495E;
}

.page-bg--ai {
  background-color: #FF69B4;
}
*/

/* 固定元素样式 */
.page-title-fixed {
  position: absolute;
  top: 240px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  height: 155px;
  z-index: 15;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  color: var(--color-white);
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-standard);
  margin: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title-fixed >.app-title {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  color: white;
  font-size: 100px;
  line-height: 1.21;
  margin: 0;
}

/* 不同页面的标题位置变体 */
.page-title-fixed--office {
  left: 894px;
  transform: none;
  width: 373px;
  height: 155px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title-fixed--facility {
  left: 917px;
  transform: none;
  width: 327px;
  height: 116px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}