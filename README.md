# EMAP AI

使用 Vue 2 + TypeScript + TailwindCSS + Capacitor 构建的移动应用项目。

## 技术栈

- **Vue 2** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供类型支持
- **TailwindCSS** - 实用优先的 CSS 框架
- **Capacitor** - 跨平台原生应用构建工具

## 开始使用

### 开发环境

```bash
# 启动开发服务器
npm run serve
```

### 构建项目

```bash
# 构建生产版本
npm run build
```

### 打包 APK

1. 首先添加 Android 平台（仅需执行一次）：
   ```bash
   npm run cap:add
   ```

2. 构建并同步到 Android：
   ```bash
   npm run build:apk
   ```

3. 在 Android Studio 中打开项目进行最终的 APK 构建

### 其他命令

- `npm run cap:sync` - 同步 Web 资源到原生项目
- `npm run cap:open` - 在 Android Studio 中打开项目

## 项目结构

```
emap-ai/
├── src/
│   ├── components/     # Vue 组件
│   ├── views/         # 页面组件
│   ├── router/        # 路由配置
│   ├── store/         # Vuex 状态管理
│   ├── styles/        # 样式文件
│   ├── App.vue        # 根组件
│   └── main.ts        # 应用入口
├── public/            # 静态资源
├── android/           # Android 原生项目（cap:add 后生成）
└── dist/             # 构建输出目录
```

## 注意事项

- 确保已安装 Android Studio 和相关 SDK
- 首次运行 `npm run cap:add` 可能需要较长时间
- 开发时使用 `npm run serve`，构建 APK 前先运行 `npm run build` 