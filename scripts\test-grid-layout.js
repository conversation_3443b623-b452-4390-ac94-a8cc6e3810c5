#!/usr/bin/env node

/**
 * 测试Shop页面4列网格布局
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 检查Shop页面4列布局...\n')

let allGood = true

function checkFileContent(filePath, searchTexts, description) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${description}: 文件不存在 - ${filePath}`)
    allGood = false
    return false
  }
  
  const content = fs.readFileSync(filePath, 'utf8')
  const results = searchTexts.map(text => ({
    text,
    found: content.includes(text)
  }))
  
  const allFound = results.every(r => r.found)
  console.log(`${allFound ? '✅' : '❌'} ${description}`)
  
  if (!allFound) {
    results.forEach(r => {
      if (!r.found) {
        console.log(`   ❌ 缺少: ${r.text}`)
        allGood = false
      }
    })
  }
  
  return allFound
}

console.log('📐 检查网格布局配置...')

// 检查Shop.vue的网格配置
checkFileContent(
  'src/views/Shop.vue',
  [
    'grid-template-columns: repeat(4, 428px) !important',
    'gap: 91px 70px !important',
    'width: 1922px !important',
    'display: grid !important'
  ],
  'Shop.vue网格布局强制配置'
)

// 检查卡片尺寸强制配置
checkFileContent(
  'src/views/Shop.vue',
  [
    'width: 428px !important',
    'height: 428px !important',
    'flex-shrink: 0 !important'
  ],
  'Shop.vue卡片尺寸强制配置'
)

console.log('\n🎴 检查ShopCardOptimized组件...')

// 检查ShopCardOptimized的尺寸配置
checkFileContent(
  'src/components/ShopCardOptimized.vue',
  [
    'width: 428px !important',
    'height: 428px !important',
    'min-width: 428px !important',
    'max-width: 428px !important',
    'display: flex !important',
    'flex-shrink: 0 !important'
  ],
  'ShopCardOptimized尺寸强制配置'
)

console.log('\n🎨 检查CSS优化文件...')

// 检查shop-performance.css的网格配置
checkFileContent(
  'src/styles/shop-performance.css',
  [
    'grid-template-columns: repeat(4, 428px) !important',
    'display: grid !important',
    'width: 428px !important',
    'height: 428px !important'
  ],
  'shop-performance.css网格和卡片配置'
)

console.log('\n📊 布局配置总结:')

if (allGood) {
  console.log('🎉 4列网格布局配置检查通过！')
  
  console.log('\n✅ 布局配置:')
  console.log('• 网格: 4列 × N行')
  console.log('• 卡片尺寸: 428px × 428px')
  console.log('• 列间距: 70px')
  console.log('• 行间距: 91px')
  console.log('• 总宽度: 1922px')
  console.log('• 强制布局: 所有关键属性都使用 !important')
  
  console.log('\n🔧 如果仍显示单列:')
  console.log('1. 检查浏览器开发者工具中的CSS')
  console.log('2. 确认没有其他CSS覆盖网格布局')
  console.log('3. 检查容器宽度是否足够（需要至少1922px）')
  console.log('4. 尝试在浏览器中手动设置CSS')
  
  console.log('\n🚀 测试步骤:')
  console.log('1. 运行: npm run dev')
  console.log('2. 在浏览器中打开Shop页面')
  console.log('3. 检查是否显示4列布局')
  console.log('4. 如果正常，则构建APK测试')
  
} else {
  console.log('❌ 发现布局配置问题，请检查上述错误')
  
  console.log('\n🔧 修复建议:')
  console.log('1. 确保所有文件都已正确保存')
  console.log('2. 检查CSS语法是否正确')
  console.log('3. 重新运行构建命令')
}

console.log('\n💡 调试提示:')
console.log('• 在浏览器开发者工具中检查 .shop-grid 元素')
console.log('• 确认 grid-template-columns 属性是否生效')
console.log('• 检查每个卡片的计算样式')
console.log('• 如果容器宽度不够，网格会自动换行')

process.exit(allGood ? 0 : 1)
