<template>
  <BaseCard
    :title="transportName"
    :icon-src="iconSrc"
    size="small"
    variant="transport"
    layout="vertical"
    :class="{ 
      'state-primary': isActive && !isSpecial,
      'state-danger': isSpecial && isActive,
      'transport-special': isSpecial 
    }"
    @click="$emit('click')"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import BaseCard from './BaseCard.vue'

@Component({
  components: {
    BaseCard
  }
})
export default class TransportButton extends Vue {
  @Prop({ required: true })
  transportName!: string
  
  @Prop()
  iconSrc?: string
  
  @Prop({ default: false })
  isActive!: boolean
  
  @Prop({ default: false })
  isSpecial!: boolean // 用于MTR的特殊样式
}
</script>

<style scoped>
/* 特殊样式（MTR红色主题） */
.transport-special {
  background: rgba(255, 82, 82, 0.15) !important;
  border-color: rgba(255, 82, 82, 0.4) !important;
}

.transport-special:hover {
  background: rgba(255, 82, 82, 0.25) !important;
  border-color: rgba(255, 82, 82, 0.6) !important;
}

/* 特殊文字颜色处理 */
.transport-special .card-title {
  text-shadow: 0px 2px 4px rgba(255, 82, 82, 0.5);
}

.state-primary .card-title {
  text-shadow: 0px 2px 4px rgba(0, 238, 255, 0.5);
}
</style> 