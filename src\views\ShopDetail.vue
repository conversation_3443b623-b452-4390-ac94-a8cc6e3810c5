<template>
  <div class="shop-detail-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />

    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />

    <!-- Shop主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--shop">
      <h1 class="app-title">{{ $t('pageTitle.shopDetail') }}</h1>
    </div>

    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--detail">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">{{ $t('common.loading') || '加载中...' }}</div>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">加载商店信息失败，即将返回商店列表...</div>
        </div>
        
        <!-- 商店详情内容 -->
        <template v-else>
          <!-- 地图容器 -->
          <div class="map-container">
            <!-- 楼层标识 -->
            <div class="floor-indicator">2/F</div>
            <img src="/img/shops/shop-map.png" alt="Shop Location Map" class="map-image" />
          </div>

          <!-- 商店信息容器 -->
          <div class="shop-info-container" :style="containerHeightStyle">
            <!-- 商店Logo -->
            <div class="shop-logo">
              <img v-if="shopData.logo" :src="shopData.logo" :alt="shopDisplayName" class="logo-image" />
              <div v-else class="logo-placeholder">{{ shopDisplayName.charAt(0) }}</div>
            </div>

            <!-- 商店详细信息 -->
            <div class="shop-details" :style="detailsHeightStyle">
              <div class="shop-name-large">{{ shopDisplayName }}</div>
              <div class="shop-location-text">{{ shopData.location }}</div>
              <div class="shop-hours-text">{{ shopData.hours }}</div>
              <div v-if="shopData.tel" class="shop-tel-text">{{ shopData.tel }}</div>
              <div class="shop-description-container">
                <div class="shop-description-text" :class="{ 'expanded': isDescriptionExpanded }" v-html="renderedDescription"></div>
                <div v-if="shopData.description && shopData.description.length > 100" class="expand-button" @click="toggleDescription">
                  <svg class="expand-icon" :class="{ 'rotated': isDescriptionExpanded }" viewBox="0 0 24 24" fill="none">
                    <path d="M19 9L12 16L5 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 返回按钮 -->
            <div class="back-button" @click="goBackToShop">
              <svg class="back-icon" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 5L5 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span class="back-text">{{ $t('common.back') }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar @home-clicked="onHomeClick" @language-changed="onLanguageChange" @ai-clicked="onAIClick" />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import SoundMixin from '@/mixins/SoundMixin'
import { ShopData, getShopData } from '@/services/shopService'
import { marked } from 'marked'

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class ShopDetail extends Mixins(ResponsiveMixin, I18nMixin, SoundMixin) {
  getBackgroundColor(): string {
    return '#0F04A9' // Shop页面的蓝色背景
  }

  shopData: ShopData = {
    id: '',
    name: '',
    name_tc: '',
    name_zh: '',
    location: '',
    hours: '',
    tel: '',
    description: '',
    logo: ''
  }

  private loading = true
  private error = false
  private isDescriptionExpanded = false
  private renderedDescriptionCache = ''

  // 根据当前语言获取店铺显示名称
  get shopDisplayName(): string {
    // 安全地访问i18n，如果未初始化则使用默认值
    const currentLang = (this as any).$i18n?.locale || 'en'
    
    switch (currentLang) {
      case 'zh-TW':
      case 'zh-CN':
        return this.shopData.name_tc || this.shopData.name_zh || this.shopData.name
      case 'en':
      default:
        return this.shopData.name
    }
  }

  // 计算描述文本的行数
  get descriptionLineCount(): number {
    if (!this.shopData.description) return 0
    
    // 估算每行能容纳的字符数（基于64px字体大小和1557px宽度）
    const charactersPerLine = Math.floor(1557 / 40) // 大约每个字符40px宽度
    const totalLines = Math.ceil(this.shopData.description.length / charactersPerLine)
    
    return totalLines
  }

  // 动态计算容器高度
  get containerHeightStyle() {
    if (!this.isDescriptionExpanded) {
      return { height: '879px' }
    }
    
    // 基础高度 + 额外描述文本高度
    const baseHeight = 879
    const lineHeight = 77 // 64px字体 * 1.21行高 ≈ 77px
    const collapsedLines = 4
    const totalLines = this.descriptionLineCount
    const extraLines = Math.max(0, totalLines - collapsedLines)
    const extraHeight = extraLines * lineHeight
    
    const newHeight = baseHeight + extraHeight
    return { height: `${newHeight}px` }
  }

  // 动态计算详情区域高度
  get detailsHeightStyle() {
    if (!this.isDescriptionExpanded) {
      return { height: '553px' }
    }
    
    const baseHeight = 553
    const lineHeight = 77
    const collapsedLines = 4
    const totalLines = this.descriptionLineCount
    const extraLines = Math.max(0, totalLines - collapsedLines)
    const extraHeight = extraLines * lineHeight
    
    const newHeight = baseHeight + extraHeight
    return { height: `${newHeight}px` }
  }

  // 渲染markdown格式的描述文本
  get renderedDescription(): string {
    if (!this.shopData.description) return ''
    
    // 如果缓存存在且描述没有变化，直接返回缓存
    if (this.renderedDescriptionCache) {
      return this.renderedDescriptionCache
    }
    
    // 同步渲染markdown（使用parse方法）
    try {
      const result = marked.parse(this.shopData.description, {
        breaks: true,
        gfm: true
      })
      
      // 如果是字符串，直接返回；如果是Promise，返回原文本并异步更新
      if (typeof result === 'string') {
        this.renderedDescriptionCache = result
        return result
      } else {
        // 异步处理Promise结果
        result.then((html: string) => {
          this.renderedDescriptionCache = html
          this.$forceUpdate() // 强制更新视图
        })
        return this.shopData.description // 临时显示原文本
      }
    } catch (error) {
      console.error('Markdown渲染错误:', error)
      return this.shopData.description // 出错时显示原文本
    }
  }

  mounted() {
    // 监听原生返回按钮（Capacitor应用）
    const capacitor = (window as any).Capacitor
    if (capacitor && capacitor.Plugins && capacitor.Plugins.App) {
      capacitor.Plugins.App.addListener('backButton', this.handleBackButton)
    }
    
    // 监听浏览器返回按钮
    window.addEventListener('popstate', this.handlePopState)
  }

  beforeDestroy() {
    // 清理监听器
    const capacitor = (window as any).Capacitor
    if (capacitor && capacitor.Plugins && capacitor.Plugins.App) {
      capacitor.Plugins.App.removeAllListeners()
    }
    window.removeEventListener('popstate', this.handlePopState)
  }

  async created() {
    const shopId = this.$route.params.id
    if (shopId) {
      try {
        this.loading = true
        console.log('正在加载商店详情:', shopId)
        
        const shopData = await getShopData(shopId)
        if (shopData) {
          this.shopData = shopData
          this.renderedDescriptionCache = '' // 清除缓存，重新渲染
          console.log('成功加载商店详情:', shopData)
        } else {
          console.log('未找到商店数据，返回商店列表')
          this.$router.push('/shop')
        }
      } catch (error) {
        console.error('加载商店详情失败:', error)
        this.error = true
        // 发生错误时也返回商店列表
        setTimeout(() => {
          this.$router.push('/shop')
        }, 2000)
      } finally {
        this.loading = false
      }
    } else {
      // 如果没有ID，直接返回商店列表
      this.$router.push('/shop')
    }
  }

  goBackToShop() {
    // 播放点击音效
    this.playClickSound()
    
    // 检查是否有历史记录可以返回
    if (window.history.length > 1) {
      // 使用路由返回上一页
      this.$router.go(-1)
    } else {
      // 如果没有历史记录，默认返回商店页面
      this.$router.push('/shop')
    }
  }

  handleBackButton() {
    // 处理原生应用的返回按钮
    this.goBackToShop()
  }

  handlePopState() {
    // 处理浏览器的返回按钮
    // 这里可以添加额外的处理逻辑
  }

  toggleDescription() {
    // 播放点击音效
    this.playClickSound()
    this.isDescriptionExpanded = !this.isDescriptionExpanded
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */

/* 地图容器 - 根据设计稿调整位置和尺寸 */
.map-container {
  position: relative;
  width: 2031px;
  height: 1636px;
  margin: 0 auto; /* 居中且无底部间距 */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40px;
  backdrop-filter: blur(10px);
  box-shadow:
    2px 2px 4px 0px rgba(0, 0, 0, 0.25),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}

/* 楼层标识 */
.floor-indicator {
  position: absolute;
  top: 295px;
  left: 225px;
  width: 201px;
  height: 155px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 128px;
  line-height: 1.21;
  color: #FFFFFF;
  z-index: 10;
}

/* 渐变边框效果 */
.map-container::before {
  content: '';
  position: absolute;
  top: -9px;
  left: -9px;
  right: -9px;
  bottom: -9px;
  border-radius: 49px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: inset 0px -2px 4px rgba(0, 0, 0, 0.2), inset 0px 2px 4px rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  z-index: -1;
}

.map-image {
  position: absolute;
  top: 295px;
  left: 44px;
  width: 1950px;
  height: 1087px;
  object-fit: contain;
  border-radius: 20px;
}

/* 商店信息容器 - 调整为相对定位 */
.shop-info-container {
  position: relative;
  width: 2022px;
  height: 879px;
  margin: 0 auto; /* 居中 */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40px;
  backdrop-filter: blur(10px);
  box-shadow:
    2px 2px 4px 0px rgba(0, 0, 0, 0.25),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
  transition: height 0.3s ease;
}

/* 商店信息容器渐变边框效果 */
.shop-info-container::before {
  content: '';
  position: absolute;
  top: -9px;
  left: -9px;
  right: -9px;
  bottom: -9px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: inset 0px -2px 4px rgba(0, 0, 0, 0.2), inset 0px 2px 4px rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border-radius: 49px;
  z-index: -1;
}

.shop-logo {
  position: absolute;
  left: 61px;
  top: 79px;
  width: 228px;
  height: 147px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 72px;
  font-weight: 600;
  color: white;
}

.shop-details {
  position: absolute;
  left: 384px;
  top: 56px;
  width: 1557px;
  height: 553px;
  transition: height 0.3s ease;
}

.shop-name-large {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 96px;
  line-height: 1.21;
  color: #FFFFFF;
  margin: 0 0 16px 0;
}

.shop-location-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 64px;
  line-height: 1.2102272510528564em;
  color: #FFFFFF;
  margin: 0 0 16px 0;
}

.shop-hours-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 64px;
  line-height: 1.2102272510528564em;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.shop-tel-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 48px;
  line-height: 1.2102272510528564em;
  color: #FFFFFF;
  /* color: #00EEFF; */
  margin: 0 0 24px 0;
}

.shop-description-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 48px;
  line-height: 1.2102272510528564em;
  color: #FFFFFF;
  margin: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.3s ease;
}

.shop-description-text.expanded {
  display: block;
  -webkit-line-clamp: unset;
  overflow: visible;
}

/* Markdown元素样式 */
.shop-description-text h1,
.shop-description-text h2,
.shop-description-text h3,
.shop-description-text h4,
.shop-description-text h5,
.shop-description-text h6 {
  color: #FFFFFF;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  margin: 16px 0 12px 0;
  line-height: 1.3;
}

.shop-description-text h1 { font-size: 52px; }
.shop-description-text h2 { font-size: 50px; }
.shop-description-text h3 { font-size: 48px; }
.shop-description-text h4 { font-size: 46px; }
.shop-description-text h5 { font-size: 44px; }
.shop-description-text h6 { font-size: 42px; }

.shop-description-text p {
  margin: 0 0 16px 0;
  color: #FFFFFF;
  font-size: 48px;
  line-height: 1.2102272510528564em;
}

.shop-description-text strong {
  font-weight: 700;
  color: #00EEFF;
}

.shop-description-text em {
  font-style: italic;
  color: #CCCCCC;
}

.shop-description-text ul,
.shop-description-text ol {
  margin: 16px 0;
  padding-left: 40px;
  color: #FFFFFF;
}

.shop-description-text li {
  margin: 8px 0;
  font-size: 48px;
  line-height: 1.2102272510528564em;
  color: #FFFFFF;
}

.shop-description-text ul li {
  list-style-type: disc;
}

.shop-description-text ol li {
  list-style-type: decimal;
}

.shop-description-text li strong {
  color: #00EEFF;
  font-weight: 700;
}

.shop-description-text blockquote {
  border-left: 4px solid #00EEFF;
  padding-left: 20px;
  margin: 16px 0;
  color: #CCCCCC;
  font-style: italic;
}

.shop-description-text code {
  background: rgba(0, 238, 255, 0.1);
  color: #00EEFF;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 44px;
}

.shop-description-container {
  position: relative;
  padding-bottom: 20px;
}

.expand-button {
  position: absolute;
  bottom: -10px;
  right: 0;
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.expand-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.expand-icon {
  width: 32px;
  height: 32px;
  color: #FFFFFF;
  transition: transform 0.3s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  color: white;
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-top: 6px solid #00EEFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-family: 'Inter', sans-serif;
  font-size: 48px;
  font-weight: 300;
  color: white;
}

.error-icon {
  font-size: 120px;
  margin-bottom: 30px;
}

/* 返回按钮样式已移除，设计稿中未显示 */
/* 恢复返回按钮样式 */
.back-button {
  position: absolute;
  left: 61px;
  bottom: 60px;
  width: 240px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border: 4px solid transparent;
  border-radius: 40px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  transition: all 0.3s ease;
  box-shadow: 
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4), 
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2),
    2px 2px 8px 0px rgba(0, 0, 0, 0.25);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.5), 
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.3),
    4px 8px 16px 0px rgba(0, 0, 0, 0.3);
}

.back-button:active {
  transform: translateY(0px);
  box-shadow: 
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.3), 
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.4),
    1px 1px 4px 0px rgba(0, 0, 0, 0.2);
}

.back-icon {
  width: 32px;
  height: 32px;
  color: #FFFFFF;
  flex-shrink: 0;
}

.back-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 40px;
  line-height: 1.2;
  color: #FFFFFF;
  white-space: nowrap;
}
</style>