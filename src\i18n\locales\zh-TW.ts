// 繁體中文語言包
export default {
  // 通用
  common: {
    loading: '載入中...',
    error: '錯誤',
    success: '成功',
    cancel: '取消',
    confirm: '確認',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '關閉',
    save: '儲存',
    delete: '刪除',
    edit: '編輯',
    search: '搜尋',
    filter: '篩選',
    all: '全部',
    none: '無',
    yes: '是',
    no: '否'
  },

  // 導航
  nav: {
    home: '首頁',
    shop: '商店',
    food: '美食',
    office: '辦公室',
    facility: '設施',
    poster: '海報',
    transport: '交通',
    worldTime: '世界時間',
    video: '影片',
    about: '關於',
    web: '網站',
    aiSearch: 'AI 搜尋'
  },

  // 页面标题
  pageTitle: {
    eMap: 'eMap',
    shop: '商店',
    food: '美食', 
    office: '辦公室',
    facility: '設施',
    poster: '海报',
    transport: '交通',
    worldTime: '世界時間',
    video: '運動影片',
    about: '關於',
    web: '香港機場航班資訊',
    airQuality: '空氣質量指數',
    aiSearch: 'AI 搜尋',
    aiChat: 'AI 對話',
    shopDetail: '商店詳情'
  },

  // 商店相關
  shop: {
    name: '商店名稱',
    location: '位置',
    hours: '營業時間',
    description: '描述',
    searchPlaceholder: '搜尋商店名稱',
    noResults: '找不到相關商店',
    tryOtherKeywords: '請嘗試使用其他關鍵詞搜尋',
    startSearch: '開始搜尋',
    searchPrompt: '在上方搜尋框中輸入商店名稱來查找您想要的商店'
  },

  // 辦公室相關
  office: {
    companyName: '公司名稱',
    roomNumber: '房間號碼',
    floor: '樓層',
    byFloor: '依樓層',
    byName: '依名稱',
    filterBy: '篩選方式'
  },

  // 設施相關
  facility: {
    men: '男廁',
    women: '女廁', 
    baby: '育嬰室',
    services: '服務',
    lift: '電梯',
    escalator: '手扶梯',
    accessibly: '無障礙設施',
    locker: '置物櫃'
  },

  // 海報相關
  poster: {
    title: '標題',
    description: '描述',
    previous: '上一張',
    next: '下一張',
    pause: '暫停',
    play: '播放',
    autoplay: '自動播放',
    defaultTitle: '海報',
    defaultDescription: '查看精彩內容'
  },

  // 交通相關
  transport: {
    bus: '巴士',
    mtr: '地鐵',
    lightRail: '輕鐵',
    miniBus: '小巴',
    nearby: '附近交通',
    schedule: '時間表',
    route: '路線'
  },

  // 美食相關
  food: {
    title: '餐飲服務',
    comingSoon: '即將推出'
  },

  // 關於頁面相關
  about: {
    title: '關於 eMap AI',
    techStack: '技術棧',
    features: '功能特色',
    version: '版本信息',
    team: '團隊信息'
  },

  // 世界時間相關
  worldTime: {
    title: '世界時間',
    realtimeTitle: '實時世界時間',
    hongkong: '香港',
    tokyo: '東京',
    newyork: '紐約',
    london: '倫敦',
    paris: '巴黎',
    sydney: '雪梨',
    beijing: '北京',
    seoul: '首爾',
    dubai: '杜拜',
    currentTime: '目前時間',
    timezone: '時區'
  },

  // 影片相關
  video: {
    title: '標題',
    description: '描述',
    duration: '時長',
    category: '分類',
    mute: '靜音',
    unmute: '取消靜音',
    fullscreen: '全螢幕',
    mutedNotice: '視頻預設靜音播放'
  },

  // 天氣相關
  weather: {
    temperature: '溫度',
    feelsLike: '體感溫度',
    humidity: '濕度',
    sunny: '晴天',
    cloudy: '多雲',
    rainy: '雨天',
    snowy: '雪天',
    stormy: '雷雨'
  },

  // 語言相關
  language: {
    current: '目前語言',
    switch: '切換語言',
    traditionalChinese: '繁體中文',
    english: '英文',
    spanish: '西班牙文',
    short: '中'
  },

  // 關於頁面詳細內容
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - 漸進式 JavaScript 框架',
      typescript: 'TypeScript - JavaScript 的超集，添加了類型系統',
      tailwind: 'TailwindCSS - 實用優先的 CSS 框架',
      capacitor: 'Capacitor - 跨平台原生應用構建工具'
    },
    features: {
      smartNavigation: '智能導航系統',
      realtimeLocation: '實時位置服務',
      multiLanguage: '多語言支持',
      crossPlatform: '跨平台兼容'
    },
    version: {
      current: '當前版本: v2.1.0',
      releaseDate: '發布日期: 2024年',
      updateFrequency: '更新頻率: 月度更新',
      supportedPlatforms: '支持平台: iOS, Android, Web'
    },
    team: {
      frontend: '前端開發: Vue.js + TypeScript',
      mobile: '移動開發: Capacitor 跨平台',
      design: 'UI/UX 設計: 現代化玻璃風格',
      data: '數據支持: 實時同步'
    }
  },

  // 城市名稱
  cities: {
    hongkong: '香港 Hong Kong',
    tokyo: '東京 Tokyo',
    newyork: '紐約 New York',
    london: '倫敦 London',
    paris: '巴黎 Paris',
    sydney: '雪梨 Sydney',
    beijing: '北京 Beijing',
    seoul: '首爾 Seoul',
    dubai: '杜拜 Dubai',
    losangeles: '洛杉磯 Los Angeles'
  },

  // 海報內容
  posterContent: {
    splus: {
      title: 'S+ REWARDS會員',
      description: '為生活十多一點，立即登記成為S+ REWARDS會員，驚喜獎賞不斷'
    },
    ikea: {
      title: 'IKEA家新意',
      description: 'HomeSquare IKEA 促銷活動，家具優惠不容錯過'
    },
    more: {
      title: '更多促銷信息',
      description: '查看更多商場促銷和活動詳情'
    }
  },

  // 視頻相關
  videoContent: {
    sound: {
      on: '開啟聲音',
      off: '關閉聲音',
      notice: '點擊下方按鈕或視頻播放器上的聲音圖標開啟音量'
    },
    videos: {
      basketball: {
        title: 'Hotel Sport Event - 籃球比賽精彩集錦',
        description: '酒店體育活動籃球比賽的精彩瞬間回顧',
        category: '籃球'
      },
      swimming: {
        title: '游泳比賽精彩瞬間',
        description: '游泳比賽的激烈角逐和精彩表現',
        category: '游泳'
      },
      tennis: {
        title: '網球錦標賽決賽',
        description: '網球錦標賽決賽的精彩對決',
        category: '網球'
      }
    }
  },

  // AI搜索
  aiSearch: {
    placeholder: '搜索商店...'
  },

  // AI对话
  aiChat: {
    welcomeTitle: '您好，我是Winnie！',
    welcomeMessage: '我是這個商場的智能客服助手，有什麼可以幫助您的嗎？',
    inputPlaceholder: '請輸入您的問題...',
    listening: '正在聆聽...',
    sendMessage: '發送',
    voiceInput: '語音輸入',
    voiceMessage: '[語音消息]',
    typing: 'Winnie正在回覆...',
    error: '抱歉，我現在無法回覆您的消息。請稍後再試。',
    newChat: '新對話',
    clearChat: '清除對話',
    recordingGuide: '正在錄音中，請說話...'
  },
  
  // Web 页面
  web: {
    loading: '正在載入...',
    error: '載入失敗',
    refresh: '重新整理',
    back: '返回'
  }
}
