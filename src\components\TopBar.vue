<template>
  <div class="top-bar">
    <!-- Logo区域 -->
    <div class="logo-section">
      <img src="/img/logo.png" alt="Logo" class="logo-image" />
    </div>

    <!-- 时间显示 -->
    <div class="datetime-section">
      {{ currentDateTime }}
    </div>
    
    <!-- 天气信息 -->
    <div class="weather-section">
      <div class="weather-icon">{{ weatherIcon }}</div>
      <div class="weather-temp">{{ temperature }}°C</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import weatherService, { WeatherData } from '@/services/weather'
import I18nMixin from '@/mixins/I18nMixin'

@Component
export default class TopBar extends Mixins(I18nMixin) {
  currentDateTime = ''
  weatherData: WeatherData | null = null
  
  private weatherUpdateInterval: any = null

  get temperature(): number {
    return this.weatherData?.temperature || 25
  }

  get weatherIcon(): string {
    if (!this.weatherData) return '🌤️'
    return weatherService.getWeatherIcon(this.weatherData.icon)
  }

  mounted() {
    this.updateDateTime()
    this.loadWeatherData()
    
    // 每秒更新时间
    setInterval(this.updateDateTime, 1000)
    
    // 每10分钟更新天气数据
    this.weatherUpdateInterval = setInterval(this.loadWeatherData, 10 * 60 * 1000)
    
    // 監聽語言變更，更新時間格式
    this.$root.$on('language-changed', this.updateDateTime)
  }

  beforeDestroy() {
    if (this.weatherUpdateInterval) {
      clearInterval(this.weatherUpdateInterval)
    }
    // 清理語言變更監聽
    this.$root.$off('language-changed', this.updateDateTime)
  }

  updateDateTime() {
    const now = new Date()
    
    // 根據當前語言格式化日期時間
    let timeFormat: Intl.DateTimeFormatOptions
    
    switch (this.$currentLanguage) {
      case 'en':
        timeFormat = {
          year: 'numeric',
          month: 'short', 
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }
        break
      case 'es':
        timeFormat = {
          year: 'numeric',
          month: 'short',
          day: 'numeric', 
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }
        break
      case 'zh-TW':
      default:
        timeFormat = {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false
        }
        break
    }

    // 使用相應語言的locale格式化時間
    const locale = this.getLocaleForLanguage()
    
    // 自定义格式：YYYY-MM-DD HH:MM:SS
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    
    this.currentDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  private getLocaleForLanguage(): string {
    switch (this.$currentLanguage) {
      case 'en':
        return 'en-US'
      case 'es':
        return 'es-ES'
      case 'zh-TW':
      default:
        return 'zh-TW'
    }
  }

  async loadWeatherData() {
    try {
      this.weatherData = await weatherService.getHongKongWeather()
    } catch (error) {
      // 天氣服務已經靜默處理錯誤
    }
  }
}
</script>

<style scoped>
.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px; /* TopBar高度，匹配Figma设计稿 */
  z-index: 20;
  background-color: rgba(0, 0, 0, 0.4); /* 根据Figma设计稿调整为黑色半透明背景 */
}

.logo-section {
  position: absolute;
  top: 28px; /* 根据Figma设计稿调整位置 */
  left: 46px; /* 根据Figma设计稿调整位置 */
  width: 273px;
  height: 172px;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.datetime-section {
  position: absolute;
  top: 66px; /* 根据Figma设计稿调整位置 */
  left: 1019px; /* 根据Figma设计稿调整位置 */
  width: 983px; /* 根据Figma设计稿调整宽度 */
  height: 97px; /* 根据Figma设计稿调整高度 */
  font-family: 'Inter', sans-serif;
  font-weight: 200; /* 根据Figma设计稿使用Light字重 */
  color: white;
  font-size: 80px; /* 根据Figma设计稿调整字体大小 */
  line-height: 1.21;
  text-align: left;
}

.weather-section {
  position: absolute;
  top: 69px; /* 根据Figma设计稿调整位置 */
  right: 32px; /* 根据Figma设计稿调整位置 */
  width: 200px; /* 增加宽度以容纳图标和温度 */
  height: 90px; /* 根据Figma设计稿调整高度 */
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  gap: 10px; /* 减少间距 */
}

.weather-icon {
  font-size: 80px; /* 调整图标大小以适应容器 */
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.weather-temp {
  font-family: 'Inter', sans-serif;
  font-weight: 200; /* 根据Figma设计稿使用Light字重 */
  color: white;
  font-size: 80px; /* 调整字体大小以适应容器 */
  line-height: 1.21;
  white-space: nowrap;
  flex-shrink: 0; /* 防止文字被压缩 */
}
</style> 