import { Module } from 'vuex'
import apiService, { EMapConfig, VideoConfig, ImageConfig } from '@/services/api'

export interface ConfigState {
  config: EMapConfig | null
  loading: boolean
  error: string | null
  lastUpdateTime: number | null
}

const configModule: Module<ConfigState, any> = {
  namespaced: true,
  
  state: (): ConfigState => ({
    config: null,
    loading: false,
    error: null,
    lastUpdateTime: null
  }),

  getters: {
    // 获取基础颜色
    baseColor: (state) => state.config?.baseColor || '#016513',
    
    // 获取视频配置
    videos: (state): VideoConfig[] => state.config?.video || [],
    
    // 获取背景图片配置
    backgroundImage: (state): ImageConfig | null => state.config?.backgrondImage || null,
    
    // 获取海报配置
    posters: (state): ImageConfig[] => state.config?.poster || [],
    
    // 获取背景图片URL
    backgroundImageUrl: (state, getters) => {
      const bgImage = getters.backgroundImage
      if (!bgImage) return ''
      return apiService.getBestImageUrl(bgImage, 'large')
    },
    
    // 获取第一个视频（兼容现有代码）
    firstVideo: (state, getters): VideoConfig | null => {
      const videos = getters.videos
      return videos.length > 0 ? videos[0] : null
    },
    
    // 获取第二个视频（兼容现有代码）
    secondVideo: (state, getters): VideoConfig | null => {
      const videos = getters.videos
      return videos.length > 1 ? videos[1] : null
    },
    
    // 是否有配置数据
    hasConfig: (state) => !!state.config,
    
    // 是否正在加载
    isLoading: (state) => state.loading,
    
    // 获取错误信息
    errorMessage: (state) => state.error
  },

  mutations: {
    SET_LOADING(state, loading: boolean) {
      state.loading = loading
    },
    
    SET_CONFIG(state, config: EMapConfig) {
      state.config = config
      state.error = null
      state.lastUpdateTime = Date.now()
    },
    
    SET_ERROR(state, error: string) {
      state.error = error
      state.loading = false
    },
    
    CLEAR_ERROR(state) {
      state.error = null
    },
    
    UPDATE_BASE_COLOR(state, color: string) {
      if (state.config) {
        state.config.baseColor = color
      }
    }
  },

  actions: {
    // 初始化配置（应用启动时调用）
    async initConfig({ commit, dispatch }) {
      commit('SET_LOADING', true)
      commit('CLEAR_ERROR')
      
      try {
        const config = await apiService.getConfig()
        if (config) {
          commit('SET_CONFIG', config)
          // 应用基础颜色到CSS
          dispatch('applyBaseColor', config.baseColor)
          console.log('配置初始化成功')
        } else {
          commit('SET_ERROR', '无法获取配置数据')
        }
      } catch (error) {
        console.error('初始化配置失败:', error)
        commit('SET_ERROR', error instanceof Error ? error.message : '初始化配置失败')
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 刷新配置
    async refreshConfig({ commit, dispatch }) {
      commit('SET_LOADING', true)
      commit('CLEAR_ERROR')
      
      try {
        const config = await apiService.forceRefresh()
        if (config) {
          commit('SET_CONFIG', config)
          // 应用基础颜色到CSS
          dispatch('applyBaseColor', config.baseColor)
          console.log('配置刷新成功')
        } else {
          commit('SET_ERROR', '无法刷新配置数据')
        }
      } catch (error) {
        console.error('刷新配置失败:', error)
        commit('SET_ERROR', error instanceof Error ? error.message : '刷新配置失败')
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 应用基础颜色到CSS变量
    applyBaseColor({ commit }, color: string) {
      try {
        // 更新CSS变量
        document.documentElement.style.setProperty('--color-background', color)
        
        // 移除旧的动态样式（如果存在）
        const existingStyle = document.getElementById('dynamic-background-colors')
        if (existingStyle) {
          existingStyle.remove()
        }
        
        // 创建新的样式规则，直接设置响应式容器的背景色
        const style = document.createElement('style')
        style.id = 'dynamic-background-colors'
        style.innerHTML = `
          .responsive-page-container {
            background-color: ${color} !important;
          }
        `
        
        document.head.appendChild(style)
        commit('UPDATE_BASE_COLOR', color)
        console.log('基础颜色已应用到responsive-page-container:', color)
      } catch (error) {
        console.error('应用基础颜色失败:', error)
      }
    },
    
    // 获取海报图片URL
    getPosterUrl({ state }, index: number): string {
      if (!state.config?.poster || !state.config.poster[index]) {
        return ''
      }
      return apiService.getBestImageUrl(state.config.poster[index], 'large')
    }
  }
}

export default configModule 