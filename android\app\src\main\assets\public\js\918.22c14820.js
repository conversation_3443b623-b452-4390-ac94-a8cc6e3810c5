"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[918],{150:(t,e,a)=>{a.d(e,{A:()=>u});var s=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{class:t.cardClasses,on:{click:function(e){return t.$emit("click")}}},[t.showIcon?e("div",{class:t.iconContainerClasses},[t.iconSrc?e("img",{class:t.iconImageClasses,attrs:{src:t.iconSrc,alt:t.title}}):t.showPlaceholder?e("div",{class:t.placeholderClasses},[t._v(" "+t._s(t.placeholderText)+" ")]):t._e()]):t._e(),t.showContent?e("div",{class:t.contentClasses},[t._t("default",function(){return[t.title?e("div",{class:t.titleClasses},[t._v(" "+t._s(t.title)+" ")]):t._e(),t.subtitle?e("div",{class:t.subtitleClasses},[t._v(" "+t._s(t.subtitle)+" ")]):t._e()]})],2):t._e(),t._t("custom")],2)},r=[],i=a(635),o=a(233);let l=class extends o.lD{title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const t=["card-base","text-primary"],e={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},a={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},s={vertical:"card-vertical",horizontal:"card-horizontal"};return[...t,e[this.size],a[this.variant],s[this.layout]]}get iconContainerClasses(){const t=["icon-container"],e={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...t,e[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const t=["icon-placeholder"],e={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...t,e[this.size]]}get contentClasses(){const t=["card-content"],e={vertical:"content-vertical",horizontal:"content-horizontal"};return[...t,e[this.layout]]}get titleClasses(){const t=["card-title","text-primary"],e={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},a={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...t,e[this.size],a[this.variant]]}get subtitleClasses(){const t=["card-subtitle","text-primary"],e={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...t,e[this.size]]}};(0,i.Cg)([(0,o.kv)({required:!0})],l.prototype,"title",void 0),(0,i.Cg)([(0,o.kv)()],l.prototype,"subtitle",void 0),(0,i.Cg)([(0,o.kv)()],l.prototype,"iconSrc",void 0),(0,i.Cg)([(0,o.kv)({default:"medium"})],l.prototype,"size",void 0),(0,i.Cg)([(0,o.kv)({default:"facility"})],l.prototype,"variant",void 0),(0,i.Cg)([(0,o.kv)({default:"vertical"})],l.prototype,"layout",void 0),(0,i.Cg)([(0,o.kv)({default:!0})],l.prototype,"showIcon",void 0),(0,i.Cg)([(0,o.kv)({default:!0})],l.prototype,"showContent",void 0),(0,i.Cg)([(0,o.kv)({default:!0})],l.prototype,"showPlaceholder",void 0),l=(0,i.Cg)([o.uA],l);const n=l,c=n;var p=a(656),d=(0,p.A)(c,s,r,!1,null,"022da4db",null);const u=d.exports},918:(t,e,a)=>{a.r(e),a.d(e,{default:()=>B});var s=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"transport-page responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--transport"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.transport")))])]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--standard"},[e("div",{staticClass:"transport-image-container"},[e("img",{staticClass:"transport-image",attrs:{src:"/img/transports/img.png",alt:"Transport Information"}})]),e("div",{staticClass:"transport-buttons"},t._l(t.transportOptions,function(a){return e("TransportButton",{key:a.id,attrs:{"transport-id":a.id,"transport-name":a.name,"icon-src":a.icon,"is-selected":t.selectedTransport===a.id},on:{"transport-selected":t.selectTransport}})}),1)])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},r=[],i=a(635),o=a(233),l=a(14),n=a(958),c=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("BaseCard",{class:{"state-primary":t.isActive&&!t.isSpecial,"state-danger":t.isSpecial&&t.isActive,"transport-special":t.isSpecial},attrs:{title:t.transportName,"icon-src":t.iconSrc,size:"small",variant:"transport",layout:"vertical"},on:{click:function(e){return t.$emit("click")}}})},p=[],d=a(150);let u=class extends o.lD{transportName;iconSrc;isActive;isSpecial};(0,i.Cg)([(0,o.kv)({required:!0})],u.prototype,"transportName",void 0),(0,i.Cg)([(0,o.kv)()],u.prototype,"iconSrc",void 0),(0,i.Cg)([(0,o.kv)({default:!1})],u.prototype,"isActive",void 0),(0,i.Cg)([(0,o.kv)({default:!1})],u.prototype,"isSpecial",void 0),u=(0,i.Cg)([(0,o.uA)({components:{BaseCard:d.A}})],u);const g=u,m=g;var v=a(656),h=(0,v.A)(m,c,p,!1,null,"a7dcbc8c",null);const C=h.exports;var f=a(256),y=a(185),_=a(959);let k=class extends((0,o.Xe)(y.A,_.A)){getBackgroundColor(){return"#1E90FF"}selectedTransport="mtr";transportOptions=[{id:"mtr",name:"MTR",icon:"/img/transports/mtr.svg",type:"transport"},{id:"light-rail",name:"Light Rail",icon:"/img/transports/light-rail.svg",type:"transport"},{id:"bus",name:"Bus",icon:"/img/transports/bus.svg",type:"transport"},{id:"mini-bus",name:"Mini Bus",icon:"/img/transports/mini-bus.svg",type:"transport"},{id:"map",name:"Map",icon:"/img/transports/map.svg",type:"map"}];selectTransport(t){this.selectedTransport=t,console.log("Selected transport:",t)}};k=(0,i.Cg)([(0,o.uA)({components:{TopBar:l.A,BottomBar:n.A,TransportButton:C,BackgroundImage:f.A}})],k);const x=k,b=x;var A=(0,v.A)(b,s,r,!1,null,"cb281cb6",null);const B=A.exports}}]);