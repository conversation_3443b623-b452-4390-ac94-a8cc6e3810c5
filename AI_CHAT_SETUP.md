# AI对话模块设置指南

## 概述
本指南将帮助您配置和使用新的AI对话模块，该模块基于Google Gemini 2.5 Flash模型，提供智能客服功能。

## 功能特性

### 🤖 AI智能客服 - Winnie
- **角色设定**: Winnie是商场的智能客服助手
- **多语言支持**: 中文、英文、西班牙语
- **智能对话**: 基于Google Gemini 2.5 Flash模型
- **语音输入**: 支持语音转文字功能
- **现代化设计**: 玻璃态UI设计，符合项目整体风格

### 🗣️ 交互功能
- **文字输入**: 支持键盘输入和Enter键发送
- **语音输入**: 点击麦克风图标进行语音输入
- **实时对话**: 每次对话都会自动保存到数据库
- **新会话**: 每次打开页面都是全新的对话

### 💾 数据存储
- **会话管理**: 每个对话会话都有独立的ID
- **消息记录**: 用户消息和AI回复都会被记录
- **元数据**: 包含时间戳、模型信息、字符统计等
- **数据库**: 使用PocketBase进行数据存储

## 安装与配置

### 1. 环境变量配置
创建或编辑 `.env` 文件：

```env
# Gemini API配置
VUE_APP_GEMINI_API_KEY=your_actual_gemini_api_key_here

# PocketBase配置
VUE_APP_POCKETBASE_URL=https://base.bwaiwork.xyz
```

### 2. 获取Gemini API密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 创建新的API密钥
3. 将密钥替换到 `.env` 文件中的 `VUE_APP_GEMINI_API_KEY`

### 3. PocketBase数据库配置
确保PocketBase中已创建以下表结构：

#### 表1: ai_sessions_byemap (会话表)
```sql
- id (text, primary key)
- title (text, 会话标题)
- summary (text, 会话摘要)
- created (datetime, 创建时间)
- updated (datetime, 更新时间)
- _byemap (text, 自定义标识)
```

#### 表2: ai_conversations_byemap (消息表)
```sql
- id (text, primary key)
- session_id (text, 关联会话ID)
- role (text, 角色: user/assistant)
- content (text, 消息内容)
- model (text, 使用的模型)
- metadata (json, 元数据)
- created (datetime, 创建时间)
- _byemap (text, 自定义标识)
```

## 使用方法

### 1. 访问AI对话页面
- 点击底部导航栏的AI按钮
- 或直接访问 `#/ai-chat` 路由

### 2. 开始对话
1. 在输入框中输入您的问题
2. 按Enter键或点击发送按钮
3. 或点击麦克风图标使用语音输入

### 3. 语音输入
1. 确保浏览器支持语音识别功能
2. 点击麦克风图标开始录音
3. 说出您的问题
4. 系统会自动将语音转换为文字

## 技术架构

### 前端组件
- **AIChat.vue**: 主要的对话界面组件
- **GlassButton.vue**: 玻璃态按钮组件
- **ResponsiveMixin.ts**: 响应式布局混入
- **I18nMixin.ts**: 国际化支持混入

### API服务
- **geminiApi.ts**: Gemini AI接口封装
- **pocketbaseApi.ts**: PocketBase数据库接口

### 样式系统
- **design-system.css**: 统一的设计系统
- **玻璃态效果**: 透明度、模糊和阴影效果
- **动画系统**: 淡入、打字指示器等动画

## 自定义配置

### 修改AI角色设定
编辑 `src/api/geminiApi.ts` 中的 `SYSTEM_PROMPT` 变量：

```typescript
const SYSTEM_PROMPT = `
你是Winnie，一个位于商场的智能AI客服助手。你的主要职责是：
1. 友好地欢迎顾客，提供热情周到的服务
2. 帮助顾客了解商场内的商店、设施、服务等信息
// ... 更多设定
`
```

### 修改模型参数
在 `callGemini` 函数中调整生成配置：

```typescript
generationConfig: {
  temperature: 0.7,    // 创造性 (0-1)
  topK: 40,           // 候选词数量
  topP: 0.95,         // 核心采样
  maxOutputTokens: 2048, // 最大输出长度
}
```

### 添加新的语言支持
1. 在 `src/i18n/locales/` 中添加新语言文件
2. 更新 `getSpeechLanguage()` 函数映射
3. 在 `I18nMixin.ts` 中添加语言配置

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的密钥是否正确
   - 确保API密钥有效且未过期

2. **语音识别不工作**
   - 确保使用支持 `webkitSpeechRecognition` 的浏览器
   - 检查浏览器权限设置

3. **数据库连接失败**
   - 检查PocketBase服务是否运行
   - 确认数据库表结构是否正确

4. **样式显示异常**
   - 检查CSS变量是否正确导入
   - 确认设计系统文件是否加载

### 调试技巧
1. 打开浏览器开发者工具
2. 查看控制台错误信息
3. 检查网络请求状态
4. 验证环境变量是否正确加载

## 部署注意事项

1. **生产环境配置**
   - 确保API密钥安全存储
   - 配置适当的CORS设置
   - 启用HTTPS以支持语音功能

2. **性能优化**
   - 考虑实现消息分页
   - 添加请求缓存机制
   - 优化图片资源加载

3. **安全考虑**
   - 不要在客户端暴露敏感信息
   - 实现适当的速率限制
   - 考虑添加用户身份验证

## 未来扩展

### 可能的改进方向
1. **功能增强**
   - 添加消息历史记录
   - 实现对话导出功能
   - 支持文件上传

2. **用户体验**
   - 添加打字指示器
   - 实现消息搜索功能
   - 支持表情包和贴纸

3. **技术优化**
   - 实现消息流式传输
   - 添加离线支持
   - 集成更多AI模型

---

如有问题或需要进一步的帮助，请参考项目文档或联系开发团队。
