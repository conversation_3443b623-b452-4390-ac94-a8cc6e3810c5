import Vue from 'vue'
import VueRouter, { RouteConfig } from 'vue-router'
import Home from '../views/Home.vue'

Vue.use(VueRouter)

const routes: Array<RouteConfig> = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/shop',
    name: 'Shop',
    component: () => import('../views/Shop.vue')
  },
  {
    path: '/shop/:id',
    name: 'ShopDetail',
    component: () => import('../views/ShopDetail.vue')
  },
  {
    path: '/food',
    name: 'Food',
    component: () => import('../views/Food.vue')
  },
  {
    path: '/office',
    name: 'Office',
    component: () => import('../views/Office.vue')
  },
  {
    path: '/facility',
    name: 'Facility',
    component: () => import('../views/Facility.vue')
  },
  {
    path: '/transport',
    name: 'Transport',
    component: () => import('../views/Transport.vue')
  },
  {
    path: '/video',
    name: 'Video',
    component: () => import('../views/Video.vue')
  },
  {
    path: '/worldtime',
    name: 'WorldTime',
    component: () => import('../views/WorldTime.vue')
  },
  {
    path: '/web2',
    name: 'Web2',
    component: () => import('../views/Web2.vue')
  },

  {
    path: '/poster',
    name: 'Poster',
    component: () => import('../views/Poster.vue')
  },
  {
    path: '/ai-search',
    name: 'AISearch',
    component: () => import('../views/AISearch.vue')
  },
  {
    path: '/ai-chat',
    name: 'AIChat',
    component: () => import('../views/AIChat.vue')
  }
]

const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes
})

export default router 