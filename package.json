{"name": "emap-ai", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "cap:init": "cap init", "cap:add": "cap add android", "cap:sync": "cap sync", "cap:sync:android": "cap sync android", "cap:open": "cap open android", "cap:run:android": "cap run android", "cap:build:android": "cap build android", "build:apk": "npm run build:prod && cap sync android && cap open android", "build:apk:release": "npm run build:prod && cap sync android && cap build android --release", "clean": "rm -rf dist node_modules/.cache"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@capacitor/android": "^7.4.0", "@capacitor/browser": "^7.0.1", "@capacitor/cli": "^7.4.0", "@capacitor/core": "^7.4.0", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@google/generative-ai": "^0.24.1", "axios": "^1.10.0", "capacitor-voice-recorder": "^7.0.6", "marked": "^16.1.1", "pocketbase": "^0.26.1", "swiper": "^5.4.5", "vue": "^2.7.16", "vue-awesome-swiper": "^4.1.1", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.4", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "autoprefixer": "^10.4.21", "compression-webpack-plugin": "^11.1.0", "css-loader": "^7.1.2", "postcss": "^8.5.6", "style-loader": "^4.0.0", "tailwindcss": "^4.1.11", "ts-loader": "^8.4.0", "typescript": "^5.8.3", "vue-loader": "^15.11.1", "vue-template-compiler": "^2.7.16", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}