import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.example.emapai',
  appName: 'EMAP AI',
  webDir: 'dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    StatusBar: {
      style: 'DARK',
      backgroundColor: '#000000',
      androidStyle: 'DARK',
      // 移除overlay设置，避免初始化时的布局问题
      overlay: false
    },
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      launchFadeOutDuration: 3000,
      backgroundColor: "#000000",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: true,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#999999"
    }
  },
  android: {
    // 性能优化配置
    backgroundColor: '#000000',
    webContentsDebuggingEnabled: false,
    allowMixedContent: true,
    appendUserAgent: 'EMAP-AI-WebView',
    overrideUserAgent: 'EMAP-AI-WebView Mozilla/5.0',

    // Webview滚动性能优化
    hardwareAccelerated: true,
    scrollingEnabled: true,
    scrollDeceleration: 0.99,

    // 启用GPU加速渲染
    mixedContentMode: 'compatibility',

    // 新增性能优化配置
    webViewRenderProcessLimit: 1,
    webViewCacheMode: 'LOAD_DEFAULT',
    webViewDatabaseEnabled: true,
    webViewDomStorageEnabled: true,
    webViewJavaScriptEnabled: true,
    webViewJavaScriptCanOpenWindowsAutomatically: false,
    webViewLoadWithOverviewMode: true,
    webViewUseWideViewPort: true,
    webViewBuiltInZoomControls: false,
    webViewDisplayZoomControls: false,
    webViewAllowFileAccess: true,
    webViewAllowContentAccess: true,
    webViewAllowFileAccessFromFileURLs: false,
    webViewAllowUniversalAccessFromFileURLs: false,
    webViewMediaPlaybackRequiresUserGesture: false,
    webViewMixedContentMode: 'MIXED_CONTENT_COMPATIBILITY_MODE'
  }
};

export default config;
