# Android 麦克风权限配置说明

## 问题描述

在 Vue 应用中使用 `navigator.mediaDevices.getUserMedia()` API 请求麦克风权限，通过 Capacitor 打包成 Android 应用后，无法正常调用系统级别的麦克风。

## 问题原因

1. **WebView 限制**：Capacitor 使用 Android WebView 来运行 Vue 应用，而 WebView 对 `getUserMedia` API 的支持有限制。

2. **权限配置不完整**：
   - 仅在 `AndroidManifest.xml` 中添加权限声明是不够的
   - Android 6.0+ 需要在运行时动态请求权限
   - WebView 需要特殊配置才能支持音频录制

3. **安全协议要求**：`getUserMedia` API 只能在安全上下文（HTTPS）中使用。

## 解决方案

### 1. 添加权限声明（已完成）

在 `android/app/src/main/AndroidManifest.xml` 中添加：

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
```

### 2. 使用 Capacitor Voice Recorder 插件

#### 安装插件

```bash
npm install capacitor-voice-recorder
```

#### 更新代码实现

在 `AIChat.vue` 中，我们已经修改了代码以支持：

1. **平台检测**：区分 Web 平台和原生平台
2. **权限请求**：在原生平台上使用插件的权限请求方法
3. **录音功能**：使用插件的录音 API 替代 `getUserMedia`

主要代码改动：

```typescript
// 导入必要的模块
import { Capacitor } from '@capacitor/core'
import { VoiceRecorder } from 'capacitor-voice-recorder'

// 初始化时检查平台
if (Capacitor.isNativePlatform()) {
  // 原生平台：请求权限
  const hasPermission = await VoiceRecorder.hasAudioRecordingPermission()
  if (!hasPermission) {
    await VoiceRecorder.requestAudioRecordingPermission()
  }
} else {
  // Web 平台：使用 getUserMedia
  const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
}

// 录音时根据平台选择不同方法
if (Capacitor.isNativePlatform()) {
  // 开始录音
  await VoiceRecorder.startRecording()
  
  // 停止录音并获取数据
  const result = await VoiceRecorder.stopRecording()
  // 处理 result.value.recordDataBase64
} else {
  // 使用 MediaRecorder API
}
```

### 3. 同步到 Android 项目

每次修改后都需要同步：

```bash
npx cap sync android
```

### 4. 构建和测试

```bash
# 构建 Vue 项目
npm run build:prod

# 同步到 Android
npx cap sync android

# 在 Android Studio 中打开
npx cap open android
```

## 优势

使用 Capacitor Voice Recorder 插件的优势：

1. **原生性能**：直接调用 Android 原生录音 API，性能更好
2. **权限管理**：自动处理运行时权限请求
3. **兼容性好**：支持更多 Android 设备和版本
4. **格式支持**：支持多种音频格式输出
5. **跨平台**：同时支持 iOS 和 Android

## 注意事项

1. **测试环境**：需要在真实设备或模拟器上测试，浏览器环境仍使用 `getUserMedia`
2. **权限提示**：首次使用时会弹出系统权限请求对话框
3. **错误处理**：需要处理用户拒绝权限的情况
4. **音频格式**：插件返回的是 base64 编码的音频数据，需要转换为 Blob

## 相关链接

- [Capacitor Voice Recorder 文档](https://github.com/tchvu3/capacitor-voice-recorder)
- [Capacitor 官方文档](https://capacitorjs.com/)
- [Android 权限最佳实践](https://developer.android.com/training/permissions/requesting)
