// Korean language pack
export default {
  // 공통
  common: {
    loading: '로딩 중...',
    error: '오류',
    success: '성공',
    cancel: '취소',
    confirm: '확인',
    back: '뒤로',
    next: '다음',
    previous: '이전',
    close: '닫기',
    save: '저장',
    delete: '삭제',
    edit: '편집',
    search: '검색',
    filter: '필터',
    all: '모두',
    none: '없음',
    yes: '예',
    no: '아니오'
  },

  // 탐색
  nav: {
    home: '홈',
    shop: '상점',
    food: '음식',
    office: '사무실',
    facility: '시설',
    poster: '포스터',
    transport: '교통',
    worldTime: '세계시간',
    video: '비디오',
    about: '소개',
    web: '웹',
    aiSearch: 'AI 검색'
  },

  // 페이지 제목
  pageTitle: {
    eMap: 'eMap',
    shop: '상점',
    food: '음식',
    office: '사무실',
    facility: '시설',
    poster: '포스터',
    transport: '교통',
    worldTime: '세계시간',
    video: '스포츠 비디오',
    about: '소개',
    web: '웹 브라우저',
    airQuality: '대기질 지수',
    aiSearch: '검색',
    aiChat: 'AI 어시스턴트',
    shopDetail: '상점'
  },

  // 상점 관련
  shop: {
    name: '상점명',
    location: '위치',
    hours: '운영시간',
    description: '설명',
    searchPlaceholder: '상점명 검색',
    noResults: '상점을 찾을 수 없습니다',
    tryOtherKeywords: '다른 키워드를 사용해 보세요',
    startSearch: '검색 시작',
    searchPrompt: '위의 검색 상자에 상점명을 입력하여 원하는 상점을 찾으세요'
  },

  // 사무실 관련
  office: {
    companyName: '회사명',
    roomNumber: '방 번호',
    floor: '층',
    byFloor: '층별',
    byName: '이름별',
    filterBy: '필터링'
  },

  // 시설 관련
  facility: {
    men: '남성',
    women: '여성',
    baby: '아기',
    services: '서비스',
    lift: '엘리베이터',
    escalator: '에스컬레이터',
    accessibly: '접근성',
    locker: '사물함'
  },

  // 포스터 관련
  poster: {
    title: '제목',
    description: '설명',
    previous: '이전',
    next: '다음',
    pause: '일시정지',
    play: '재생',
    autoplay: '자동재생',
    defaultTitle: '포스터',
    defaultDescription: '흥미로운 콘텐츠 보기'
  },

  // 교통 관련
  transport: {
    bus: '버스',
    mtr: '지하철',
    lightRail: '경전철',
    miniBus: '미니버스',
    nearby: '근처 교통수단',
    schedule: '시간표',
    route: '노선'
  },

  // 음식 관련
  food: {
    title: '레스토랑 서비스',
    comingSoon: '곧 출시'
  },

  // 소개 페이지 관련
  about: {
    title: 'eMap AI 소개',
    techStack: '기술 스택',
    features: '기능',
    version: '버전 정보',
    team: '팀 정보'
  },

  // 세계시간 관련
  worldTime: {
    title: '세계시간',
    realtimeTitle: '실시간 세계시간',
    hongkong: '홍콩',
    tokyo: '도쿄',
    newyork: '뉴욕',
    london: '런던',
    paris: '파리',
    sydney: '시드니',
    beijing: '베이징',
    seoul: '서울',
    dubai: '두바이',
    currentTime: '현재 시간',
    timezone: '시간대'
  },

  // 비디오 관련
  video: {
    title: '제목',
    description: '설명',
    duration: '길이',
    category: '카테고리',
    mute: '음소거',
    unmute: '음소거 해제',
    fullscreen: '전체화면',
    mutedNotice: '비디오는 기본적으로 음소거되어 재생됩니다'
  },

  // 날씨 관련
  weather: {
    temperature: '온도',
    feelsLike: '체감온도',
    humidity: '습도',
    sunny: '맑음',
    cloudy: '흐림',
    rainy: '비',
    snowy: '눈',
    stormy: '폭풍'
  },

  // 언어 관련
  language: {
    current: '현재 언어',
    switch: '언어 변경',
    traditionalChinese: '중국어 번체',
    english: '영어',
    spanish: '스페인어',
    japanese: '일본어',
    korean: '한국어',
    thai: '태국어',
    short: 'KO'
  },

  // 소개 페이지 상세 내용
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - 프로그레시브 JavaScript 프레임워크',
      typescript: 'TypeScript - 타입 시스템이 있는 JavaScript 슈퍼셋',
      tailwind: 'TailwindCSS - 유틸리티 우선 CSS 프레임워크',
      capacitor: 'Capacitor - 크로스 플랫폼 네이티브 앱 빌드 도구'
    },
    features: {
      smartNavigation: '스마트 내비게이션 시스템',
      realtimeLocation: '실시간 위치 서비스',
      multiLanguage: '다국어 지원',
      crossPlatform: '크로스 플랫폼 호환성'
    },
    version: {
      current: '현재 버전: v2.1.0',
      releaseDate: '출시일: 2024년',
      updateFrequency: '업데이트 빈도: 월간 업데이트',
      supportedPlatforms: '지원 플랫폼: iOS, Android, Web'
    },
    team: {
      frontend: '프론트엔드 개발: Vue.js + TypeScript',
      mobile: '모바일 개발: Capacitor 크로스 플랫폼',
      design: 'UI/UX 디자인: 모던 글래스 스타일',
      data: '데이터 지원: 실시간 동기화'
    }
  },

  // 도시명
  cities: {
    hongkong: '홍콩',
    tokyo: '도쿄',
    newyork: '뉴욕',
    london: '런던',
    paris: '파리',
    sydney: '시드니',
    beijing: '베이징',
    seoul: '서울',
    dubai: '두바이',
    losangeles: '로스앤젤레스'
  },

  // 포스터 콘텐츠
  posterContent: {
    splus: {
      title: 'S+ REWARDS 멤버',
      description: '생활에 더 많은 것을 추가하고, 지금 S+ REWARDS 멤버로 등록하여 지속적인 놀라움과 보상을 받으세요'
    },
    ikea: {
      title: 'IKEA 홈 아이디어',
      description: 'HomeSquare IKEA 프로모션 활동, 놓치지 말아야 할 가구 할인'
    },
    more: {
      title: '더 많은 프로모션 정보',
      description: '쇼핑몰 프로모션 및 이벤트 세부 정보 더 보기'
    }
  },

  // 비디오 관련
  videoContent: {
    sound: {
      on: '소리 켜기',
      off: '소리 끄기',
      notice: '아래 버튼이나 비디오 플레이어의 소리 아이콘을 클릭하여 볼륨을 활성화하세요'
    },
    videos: {
      basketball: {
        title: '호텔 스포츠 이벤트 - 농구 하이라이트',
        description: '호텔 스포츠 농구 경기의 흥미진진한 순간들 되돌아보기',
        category: '농구'
      },
      swimming: {
        title: '수영 경기 하이라이트',
        description: '수영 경기의 치열한 경쟁과 흥미진진한 성과',
        category: '수영'
      },
      tennis: {
        title: '테니스 챔피언십 결승',
        description: '테니스 챔피언십 결승의 흥미진진한 대결',
        category: '테니스'
      }
    }
  },

  // AI 검색
  aiSearch: {
    placeholder: '상점 검색...'
  },

  // AI 채팅
  aiChat: {
    welcomeTitle: '안녕하세요, 저는 Winnie입니다!',
    welcomeMessage: '이 쇼핑몰의 스마트 고객 서비스 어시스턴트입니다. 어떻게 도와드릴까요?',
    inputPlaceholder: '질문을 입력해 주세요...',
    listening: '듣고 있습니다...',
    sendMessage: '보내기',
    voiceInput: '음성 입력',
    voiceMessage: '[음성 메시지]',
    typing: 'Winnie가 입력 중...',
    error: '죄송합니다. 지금은 메시지에 응답할 수 없습니다. 나중에 다시 시도해 주세요.',
    newChat: '새 채팅',
    clearChat: '채팅 지우기',
    recordingGuide: '녹음 중... 말씀해 주세요...'
  },

  // 웹 페이지
  web: {
    loading: '로딩 중...',
    error: '로딩 실패',
    refresh: '새로고침',
    back: '뒤로'
  }
}