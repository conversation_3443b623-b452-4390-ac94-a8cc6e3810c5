<template>
  <div class="video-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Video主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--video">
      <h1 class="app-title">{{ $t('pageTitle.video') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 双视频播放区域 -->
        <div class="dual-video-section">
          <!-- 第一个视频 -->
          <div class="video-display-box">
            <div class="video-container">
              <iframe
                :src="getVideoEmbedUrl(displayVideo1.url, video1Muted)"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
                class="youtube-player"
                :key="`video1-${video1Muted}`"
              ></iframe>
            </div>
            
            <!-- 第一个视频信息 -->
            <div class="video-info">
              <h3 class="video-title-text">{{ displayVideo1.title }}</h3>
              <p class="video-description-text">{{ displayVideo1.description }}</p>
            </div>

            <!-- 第一个视频控制按钮 -->
            <div class="video-controls">
              <button 
                v-click-sound
                @click="toggleVideo1Mute" 
                class="mute-control-button"
                :class="{ 'unmuted': !video1Muted }"
              >
                <span class="button-icon">{{ video1Muted ? '🔇' : '🔊' }}</span>
                <span class="button-text">{{ video1Muted ? $t('videoContent.sound.on') : $t('videoContent.sound.off') }}</span>
              </button>
            </div>
          </div>

          <!-- 第二个视频 -->
          <div class="video-display-box">
            <div class="video-container">
              <iframe
                :src="getVideoEmbedUrl(displayVideo2.url, video2Muted)"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
                class="youtube-player"
                :key="`video2-${video2Muted}`"
              ></iframe>
            </div>
            
            <!-- 第二个视频信息 -->
            <div class="video-info">
              <h3 class="video-title-text">{{ displayVideo2.title }}</h3>
              <p class="video-description-text">{{ displayVideo2.description }}</p>
            </div>

            <!-- 第二个视频控制按钮 -->
            <div class="video-controls">
              <button 
                v-click-sound
                @click="toggleVideo2Mute" 
                class="mute-control-button"
                :class="{ 'unmuted': !video2Muted }"
              >
                <span class="button-icon">{{ video2Muted ? '🔇' : '🔊' }}</span>
                <span class="button-text">{{ video2Muted ? $t('videoContent.sound.on') : $t('videoContent.sound.off') }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 音量提示区域 -->
        <div class="volume-notice">
          <div class="notice-content">
            <div class="notice-icon">🔇</div>
            <div class="notice-text">
                              <h3 class="notice-title">{{ $t('video.mutedNotice') }}</h3>
              <p class="notice-description">{{ $t('videoContent.sound.notice') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import { mapGetters } from 'vuex'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { VideoConfig } from '@/services/api'

interface VideoItem {
  id?: number
  title: string
  description: string
  url: string
  duration: string
  category: string
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  },
  computed: {
    ...mapGetters('config', ['firstVideo', 'secondVideo', 'hasConfig'])
  }
})
export default class Video extends Mixins(ResponsiveMixin, I18nMixin) {
  // 视频静音状态管理
  video1Muted: boolean = true
  video2Muted: boolean = true

  // 从store获取的计算属性
  firstVideo!: VideoConfig | null
  secondVideo!: VideoConfig | null
  hasConfig!: boolean

  getBackgroundColor(): string {
    return '#9B59B6' // Video页面的紫色背景
  }

  // 本地默认视频数据（作为回退）
  get defaultVideoDisplay1(): VideoItem {
    return {
      id: 1,
      title: this.$t('videoContent.videos.basketball.title'),
      description: this.$t('videoContent.videos.basketball.description'),
      url: 'https://www.youtube.com/watch?v=Ufhfsx0PfTY',
      duration: '5:32',
      category: this.$t('videoContent.videos.basketball.category')
    }
  }

  get defaultVideoDisplay2(): VideoItem {
    return {
      id: 2,
      title: this.$t('videoContent.videos.swimming.title'),
      description: this.$t('videoContent.videos.swimming.description'),
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: '8:15',
      category: this.$t('videoContent.videos.swimming.category')
    }
  }

  /**
   * 获取第一个视频的显示数据
   * 优先级：远程配置 > 本地默认
   */
  get displayVideo1(): VideoItem {
    if (this.hasConfig && this.firstVideo) {
      console.log('使用远程第一个视频配置')
      return this.firstVideo
    }
    console.log('使用本地默认第一个视频')
    return this.defaultVideoDisplay1
  }

  /**
   * 获取第二个视频的显示数据
   * 优先级：远程配置 > 本地默认
   */
  get displayVideo2(): VideoItem {
    if (this.hasConfig && this.secondVideo) {
      console.log('使用远程第二个视频配置')
      return this.secondVideo
    }
    console.log('使用本地默认第二个视频')
    return this.defaultVideoDisplay2
  }

  // 切换第一个视频的静音状态
  toggleVideo1Mute() {
    this.video1Muted = !this.video1Muted
  }

  // 切换第二个视频的静音状态
  toggleVideo2Mute() {
    this.video2Muted = !this.video2Muted
  }

  /**
   * 生成YouTube嵌入URL
   */
  getVideoEmbedUrl(videoUrl: string, isMuted: boolean): string {
    const videoId = this.getVideoId(videoUrl)
    if (!videoId) return ''
    
    const muteParam = isMuted ? 1 : 0
    return `https://www.youtube.com/embed/${videoId}?autoplay=1&mute=${muteParam}&loop=1&playlist=${videoId}&rel=0&modestbranding=1&controls=1`
  }

  // Mock 视频数据 - 后期可以从后台API获取
  get videoList(): VideoItem[] {
    return [
      {
        id: 1,
        title: this.$t('videoContent.videos.basketball.title'),
        description: this.$t('videoContent.videos.basketball.description'),
        url: 'https://www.youtube.com/watch?v=Ufhfsx0PfTY',
        duration: '5:32',
        category: this.$t('videoContent.videos.basketball.category')
      },
      {
        id: 2,
        title: this.$t('videoContent.videos.swimming.title'),
        description: this.$t('videoContent.videos.swimming.description'),
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        duration: '8:15',
        category: this.$t('videoContent.videos.swimming.category')
      },
      {
        id: 3,
        title: this.$t('videoContent.videos.tennis.title'),
        description: this.$t('videoContent.videos.tennis.description'),
        url: 'https://www.youtube.com/watch?v=oHg5SJYRHA0',
        duration: '12:45',
        category: this.$t('videoContent.videos.tennis.category')
      }
    ]
  }

  currentVideo: VideoItem | null = null

  mounted() {
    // 默认播放第一个视频
    if (this.videoList.length > 0) {
      this.currentVideo = this.videoList[0]
    }
  }

  // 选择视频
  selectVideo(video: VideoItem) {
    this.currentVideo = video
  }

  // 上一个视频
  previousVideo() {
    if (!this.currentVideo) return
    const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideo!.id)
    if (currentIndex > 0) {
      this.currentVideo = this.videoList[currentIndex - 1]
    }
  }

  // 下一个视频
  nextVideo() {
    if (!this.currentVideo) return
    const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideo!.id)
    if (currentIndex < this.videoList.length - 1) {
      this.currentVideo = this.videoList[currentIndex + 1]
    }
  }

  // 检查是否有上一个视频
  get hasPrevious(): boolean {
    if (!this.currentVideo) return false
    const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideo!.id)
    return currentIndex > 0
  }

  // 检查是否有下一个视频
  get hasNext(): boolean {
    if (!this.currentVideo) return false
    const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideo!.id)
    return currentIndex < this.videoList.length - 1
  }

  // 提取 YouTube 视频 ID
  getVideoId(url: string): string {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
    const match = url.match(regExp)
    return (match && match[2].length === 11) ? match[2] : ''
  }

  // 获取视频缩略图
  getVideoThumbnail(url: string): string {
    const videoId = this.getVideoId(url)
    return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
  }

  // 全屏播放
  toggleFullscreen() {
    const iframe = document.querySelector('.youtube-player') as HTMLIFrameElement
    if (iframe) {
      if (iframe.requestFullscreen) {
        iframe.requestFullscreen()
      }
    }
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
/* 双视频播放区域 */
.dual-video-section {
  display: flex;
  flex-direction: column;
  gap: 90px;
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
}

.video-display-box {
  display: flex;
  flex-direction: column;
  gap: 50px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  backdrop-filter: blur(15px);
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 45%; /* 16:9 比例的高度 */
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: #000000;
}

.youtube-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-info {
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  backdrop-filter: blur(20px);
  padding: 50px;
  
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
}

.video-title-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 56px;
  color: #FFFFFF;
  margin-bottom: 20px;
  line-height: 1.2;
}

.video-description-text {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: 36px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin: 0;
}

/* 视频控制按钮区域 */
.video-controls {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mute-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 25px 50px;
  background: rgba(255, 255, 255, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 60px;
  backdrop-filter: blur(20px);
  cursor: pointer;
  transition: all 0.3s ease;
  
  /* 毛玻璃效果 */
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1);
}

.mute-control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  
  box-shadow: 
    6px 6px 12px 0px rgba(0, 0, 0, 0.2),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.15);
}

.mute-control-button.unmuted {
  background: rgba(46, 204, 113, 0.2);
  border-color: rgba(46, 204, 113, 0.4);
}

.mute-control-button.unmuted:hover {
  background: rgba(46, 204, 113, 0.3);
  border-color: rgba(46, 204, 113, 0.5);
}

.button-icon {
  font-size: 48px;
  line-height: 1;
}

.button-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 44px;
  color: #FFFFFF;
  line-height: 1.2;
}

/* 音量提示區域 */
.volume-notice {
  margin-top: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  padding: 40px;
  backdrop-filter: blur(15px);
  
  /* 毛玻璃效果 */
  box-shadow: 
    2px 2px 6px 0px rgba(0, 0, 0, 0.1),
    inset 0px 1px 3px 0px rgba(255, 255, 255, 0.1);
}

.notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: left;
}

.notice-icon {
  font-size: 80px;
  margin-right: 40px;
  opacity: 0.8;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 56px;
  color: #FFFFFF;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

.notice-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 44px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.3;
}
</style> 