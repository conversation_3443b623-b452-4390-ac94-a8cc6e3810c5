// 語言自動檢測器
import { Language, SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, i18nService } from './index'

/**
 * 檢測瀏覽器語言
 */
function detectBrowserLanguage(): string | null {
  if (typeof window === 'undefined' || !window.navigator) {
    return null
  }

  // 優先使用 navigator.language
  const browserLang = window.navigator.language

  // 如果不存在，嘗試其他屬性
  if (!browserLang) {
    // @ts-ignore - 某些瀏覽器可能支持這些屬性
    return window.navigator.userLanguage || window.navigator.browserLanguage || null
  }

  return browserLang
}

/**
 * 將瀏覽器語言代碼映射到應用支持的語言
 */
function mapBrowserLanguageToSupported(browserLang: string): Language | null {
  if (!browserLang) return null

  // 規範化語言代碼（小寫）
  const normalizedLang = browserLang.toLowerCase()

  // 語言映射表
  const languageMap: Record<string, Language> = {
    // 中文變體
    'zh': 'zh-TW',
    'zh-tw': 'zh-TW',
    'zh-hk': 'zh-TW',
    'zh-hant': 'zh-TW',
    'zh-cn': 'zh-TW', // 簡體中文也映射到繁體
    'zh-hans': 'zh-TW',
    
    // 英文變體
    'en': 'en',
    'en-us': 'en',
    'en-gb': 'en',
    'en-au': 'en',
    'en-ca': 'en',
    
    // 西班牙文變體
    'es': 'es',
    'es-es': 'es',
    'es-mx': 'es',
    'es-ar': 'es',
    'es-co': 'es'
  }

  // 精確匹配
  if (languageMap[normalizedLang]) {
    return languageMap[normalizedLang]
  }

  // 嘗試匹配語言代碼的前綴（例如 zh-SG -> zh）
  const langPrefix = normalizedLang.split('-')[0]
  if (languageMap[langPrefix]) {
    return languageMap[langPrefix]
  }

  // 檢查是否直接支持
  const supportedLang = SUPPORTED_LANGUAGES.find(
    lang => lang.code.toLowerCase() === normalizedLang
  )
  if (supportedLang) {
    return supportedLang.code
  }

  return null
}

/**
 * 自動檢測並設置語言
 * @param vueInstance - Vue 實例，用於觸發響應式更新
 */
export function detectAndSetLanguage(vueInstance?: any): Language {
  // 先檢查是否已經有儲存的語言設置
  const storedLang = localStorage.getItem('emap_language')
  
  if (storedLang && SUPPORTED_LANGUAGES.some(l => l.code === storedLang)) {
    // 如果有有效的儲存語言，使用它
    console.log('使用已儲存的語言:', storedLang)
    i18nService.setCurrentLanguage(storedLang as Language)
    
    // 也觸發事件以確保組件更新
    if (vueInstance && vueInstance.$root) {
      vueInstance.$root.$emit('language-changed', storedLang)
    }
    
    return storedLang as Language
  }

  // 檢測瀏覽器語言
  const browserLang = detectBrowserLanguage()
  console.log('檢測到瀏覽器語言:', browserLang)

  // 映射到支持的語言
  const mappedLang = mapBrowserLanguageToSupported(browserLang || '')
  console.log('映射後的語言:', mappedLang)

  // 設置語言
  const finalLang = mappedLang || DEFAULT_LANGUAGE
  i18nService.setCurrentLanguage(finalLang)
  
  // 如果提供了 Vue 實例，觸發語言變更事件
  if (vueInstance && vueInstance.$root) {
    // 立即觸發
    vueInstance.$root.$emit('language-changed', finalLang)
    
    // 強制所有組件更新
    vueInstance.$root.$forceUpdate()
    
    // 延遲觸發以確保所有組件都已掛載
    setTimeout(() => {
      vueInstance.$root.$emit('language-changed', finalLang)
    }, 100)
  }
  
  console.log('已設置全局語言為:', finalLang)
  return finalLang
}

/**
 * 檢測並返回建議的語言（不自動設置）
 */
export function detectLanguage(): Language {
  const browserLang = detectBrowserLanguage()
  const mappedLang = mapBrowserLanguageToSupported(browserLang || '')
  return mappedLang || DEFAULT_LANGUAGE
}

/**
 * 檢查系統語言是否為某種語言
 */
export function isSystemLanguage(targetLang: Language): boolean {
  const detectedLang = detectLanguage()
  return detectedLang === targetLang
}

/**
 * 獲取系統語言信息
 */
export function getSystemLanguageInfo() {
  const browserLang = detectBrowserLanguage()
  const mappedLang = mapBrowserLanguageToSupported(browserLang || '')
  const currentLang = i18nService.getCurrentLanguage()
  
  return {
    browserLanguage: browserLang,
    detectedLanguage: mappedLang || DEFAULT_LANGUAGE,
    currentLanguage: currentLang,
    isAutoDetected: !localStorage.getItem('emap_language')
  }
}

export default {
  detectAndSetLanguage,
  detectLanguage,
  isSystemLanguage,
  getSystemLanguageInfo
}
