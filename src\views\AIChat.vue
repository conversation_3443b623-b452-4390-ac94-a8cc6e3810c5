<template>
  <div class="ai-chat-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Chat主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--ai">
      <h1 class="app-title">{{ $t('pageTitle.aiChat') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 欢迎消息 - 只在没有对话时显示 -->
        <div class="welcome-container" v-if="messages.length === 0">
          <div class="welcome-card">
            <div class="winnie-avatar">
              <div class="avatar-circle">
                <span class="avatar-text">🤖</span>
              </div>
            </div>
            <div class="welcome-content">
              <h2 class="welcome-title">{{ $t('aiChat.welcomeTitle') }}</h2>
              <p class="welcome-message">{{ $t('aiChat.welcomeMessage') }}</p>
            </div>
          </div>
        </div>
        
        <!-- 对话区域 -->
        <div class="chat-messages" ref="chatMessages">
          <div 
            v-for="message in messages" 
            :key="message.id"
            class="message-wrapper"
            :class="message.role"
          >
            <div class="message-bubble">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
          
          <!-- 加载指示器 -->
          <div v-if="isLoading" class="loading-message">
            <div class="loading-bubble">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 输入区域 - 动态位置 -->
    <div class="input-area" :style="inputAreaStyle">
      <!-- 录音动画效果 - 显示在语音按钮上方 -->
      <div class="recording-animation-container" v-if="isRecording">
        <!-- 录音引导文字 -->
        <div class="recording-guide-text">
          {{ $t('aiChat.recordingGuide') }}
        </div>
        <div class="recording-box">
          <div class="sound-wave"></div>
          <div class="sound-wave"></div>
          <div class="sound-wave"></div>
          <div class="sound-wave"></div>
          <div class="sound-wave"></div>
        </div>
      </div>
      
      <div class="input-container">
        <div class="input-wrapper">
          <GlassButton 
            size="small"
            variant="normal"
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="cancelRecording"
            @touchstart.prevent="startRecording"
            @touchend.prevent="stopRecording"
            @touchcancel="cancelRecording"
            class="voice-button"
            :class="{ recording: isRecording }"
          >
            <div v-if="isRecording" class="voice-animation">
              <div class="sound-wave"></div>
              <div class="sound-wave"></div>
              <div class="sound-wave"></div>
            </div>
            <div v-else class="voice-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
              </svg>
            </div>
          </GlassButton>
          <input 
            ref="messageInput"
            v-model="userMessage" 
            type="text" 
            :placeholder="$t('aiChat.inputPlaceholder')"
            @keydown.enter="sendMessage"
            @input="onInputChange"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @paste="onPaste"
            class="message-input"
            :disabled="isLoading"
            contenteditable="true"
          />
          <GlassButton 
            size="small"
            variant="highlight"
            @click="sendMessage"
            class="send-button"
            :class="{ disabled: !canSend }"
          >
            <div class="send-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </div>
          </GlassButton>
        </div>
        <div class="voice-status" v-if="isListening">
          <div class="voice-indicator">
            <div class="voice-wave"></div>
            <div class="voice-wave"></div>
            <div class="voice-wave"></div>
          </div>
          <span class="voice-text">{{ $t('aiChat.listening') }}</span>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import GlassButton from '@/components/GlassButton.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { callGemini, ChatMessage } from '@/api/geminiApi'
import { saveConversation, saveMessage } from '@/api/pocketbaseApi'
import { Capacitor } from '@capacitor/core'
import { VoiceRecorder } from 'capacitor-voice-recorder'

interface Message {
  id: number
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage,
    GlassButton
  }
})
export default class AIChat extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#016513' // AIChat页面使用默认绿色背景
  }

  userMessage = ''
  messages: Message[] = []
  isLoading = false
  isListening = false
  isRecording = false
  recordingStartTime = 0
  minRecordingDuration = 300 // 防误触，最小录音时长300ms
  sessionId: string | null = null
  recognition: any = null
  abortController: AbortController | null = null
  mediaRecorder: MediaRecorder | null = null
  audioChunks: Blob[] = []
  isInputFocused = false
  inputKeyboardOffset = 0

  // 计算属性
  get canSend(): boolean {
    return this.userMessage.trim().length > 0 && !this.isLoading
  }

  // 输入区域样式计算
  get inputAreaStyle() {
    const baseBottom = 530 // 默认底部距离
    const adjustedBottom = this.isInputFocused ? baseBottom + this.inputKeyboardOffset : baseBottom
    
    return {
      position: 'absolute',
      bottom: `${adjustedBottom}px`,
      left: '50%',
      transform: 'translateX(-50%)',
      width: '1400px',
      zIndex: 20,
      transition: 'bottom 0.3s ease'
    }
  }

  async mounted() {
    // 确保权限初始化在所有操作之前
    try {
      await this.initAudioRecording()
    } catch (error) {
      console.error('初始化音频录制失败:', error)
    }
    this.scrollToBottom()
  }

  // 初始化音频录制
  async initAudioRecording() {
    // 检查是否是原生平台
    if (Capacitor.isNativePlatform()) {
      // 在原生平台上请求权限
      try {
        const hasPermission = await VoiceRecorder.hasAudioRecordingPermission()
        if (!hasPermission) {
          const permission = await VoiceRecorder.requestAudioRecordingPermission()
          if (!permission || !permission.value) {
            console.error('用户拒绝了麦克风权限')
            alert('请在设置中允许麦克风权限以使用语音功能')
            return
          }
        }
        console.log('原生音频录制权限已获得')
      } catch (error) {
        console.error('请求音频权限失败:', error)
      }
    } else {
      // 在 Web 平台上使用 getUserMedia
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        this.mediaRecorder = new MediaRecorder(stream)
        
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data)
          }
        }
        
        this.mediaRecorder.onstop = async () => {
          // 只有在正常停止录音时才发送（不是取消的情况）
          if (this.audioChunks.length > 0) {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' })
            // 发送音频到 Gemini
            await this.sendAudioMessage(audioBlob)
          }
          // 清空音频数据
          this.audioChunks = []
        }
      } catch (error) {
        console.error('无法访问麦克风:', error)
        alert('请允许访问麦克风以使用语音功能')
      }
    }
  }

  // 获取语音识别语言
  getSpeechLanguage(): string {
    const langMap: { [key: string]: string } = {
      'zh-TW': 'zh-TW',
      'en': 'en-US',
      'es': 'es-ES'
    }
    return langMap[this.$currentLanguage] || 'zh-TW'
  }

  // 发送消息
  async sendMessage() {
    if (!this.canSend) return

    const userMessage: Message = {
      id: Date.now(),
      role: 'user',
      content: this.userMessage.trim(),
      timestamp: new Date()
    }

    this.messages.push(userMessage)
    const prompt = this.userMessage.trim()
    this.userMessage = ''
    this.isLoading = true

    try {
      // 检测文字输入的语言并自动切换
      await this.detectAndSwitchLanguage(prompt)

      // 构建聊天历史
      const chatHistory: ChatMessage[] = this.messages
        .slice(0, -1) // 排除刚添加的用户消息
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }))

      // 调用Gemini API
      const aiResponse = await callGemini(prompt, chatHistory)
      
      const aiMessage: Message = {
        id: Date.now() + 1,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      }
      
      this.messages.push(aiMessage)
      
      // 保存到PocketBase
      await this.saveConversationToPocketBase(prompt, aiResponse)
      
    } catch (error) {
      console.error('Error calling AI:', error)
      const errorMessage: Message = {
        id: Date.now() + 1,
        role: 'assistant',
        content: this.$t('aiChat.error'),
        timestamp: new Date()
      }
      this.messages.push(errorMessage)
    } finally {
      this.isLoading = false
      this.scrollToBottom()
    }
  }

  // 开始录音
  async startRecording() {
    // 如果已经在录音，直接返回
    if (this.isRecording) return
    
    this.isRecording = true
    this.recordingStartTime = Date.now()
    
    try {
      if (Capacitor.isNativePlatform()) {
        // 在原生平台上使用 VoiceRecorder
        await VoiceRecorder.startRecording()
      } else {
        // 在 Web 平台上使用 MediaRecorder
        if (!this.mediaRecorder) {
          // 尝试重新初始化
          await this.initAudioRecording()
          if (!this.mediaRecorder) {
            alert('无法访问麦克风，请检查权限设置')
            this.isRecording = false
            return
          }
        }
        this.audioChunks = []
        this.mediaRecorder.start()
      }
    } catch (error) {
      console.error('开始录音失败:', error)
      this.isRecording = false
      alert('无法开始录音，请检查麦克风权限')
    }
  }

// 停止录音并发送
  async stopRecording() {
    if (!this.isRecording) return;

    const duration = Date.now() - this.recordingStartTime;

    // 防误触：录音时长小于最小时长则取消
    if (duration < this.minRecordingDuration) {
      this.cancelRecording();
      return;
    }

    this.isRecording = false;

    try {
      if (Capacitor.isNativePlatform()) {
        // 在原生平台上停止录音
        const result = await VoiceRecorder.stopRecording();

        if (result.value && result.value.recordDataBase64) {
          // 将 base64 转换为 Blob
          const base64Data = result.value.recordDataBase64;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const audioBlob = new Blob([byteArray], { type: result.value.mimeType || 'audio/webm' });

          // 发送音频到 Gemini
          await this.sendAudioMessage(audioBlob);
        }
      } else {
        // 在 Web 平台上停止录音
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop();
        }
      }
    } catch (error) {
      console.error('停止录音失败:', error);
    }
  }
  // 取消录音
  async cancelRecording() {
    if (!this.isRecording) return
    
    this.isRecording = false
    
    try {
      if (Capacitor.isNativePlatform()) {
        // 在原生平台上取消录音
        await VoiceRecorder.stopRecording()
      } else {
        // 在 Web 平台上取消录音
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          // 先清空音频数据，这样onstop回调就不会发送
          this.audioChunks = []
          this.mediaRecorder.stop()
        }
      }
    } catch (error) {
      console.error('取消录音失败:', error)
    }
  }

  // 切换语音输入（保留原有的点击功能）
  async toggleVoiceInput() {
    if (Capacitor.isNativePlatform()) {
      // 使用 Capacitor Voice Recorder 插件
      if (this.isListening) {
        // 停止录音
        try {
          const result = await VoiceRecorder.stopRecording()
          this.isListening = false
          
          if (result.value && result.value.recordDataBase64) {
            // 将 base64 转换为 Blob
            const base64Data = result.value.recordDataBase64
            const byteCharacters = atob(base64Data)
            const byteNumbers = new Array(byteCharacters.length)
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i)
            }
            const byteArray = new Uint8Array(byteNumbers)
            const audioBlob = new Blob([byteArray], { type: result.value.mimeType || 'audio/webm' })
            
            // 发送音频到 Gemini
            await this.sendAudioMessage(audioBlob)
          }
        } catch (error) {
          console.error('停止录音失败:', error)
          this.isListening = false
        }
      } else {
        // 开始录音
        try {
          await VoiceRecorder.startRecording()
          this.isListening = true
        } catch (error) {
          console.error('开始录音失败:', error)
          alert('无法开始录音，请检查麦克风权限')
        }
      }
    } else {
      // Web 平台使用原有逻辑
      if (!this.mediaRecorder) {
        alert('无法访问麦克风，请检查权限设置')
        return
      }

      if (this.isListening) {
        // 停止录音
        this.mediaRecorder.stop()
        this.isListening = false
      } else {
        // 开始录音
        this.audioChunks = []
        this.mediaRecorder.start()
        this.isListening = true
      }
    }
  }

  // 发送音频消息
  async sendAudioMessage(audioBlob: Blob) {
    this.isLoading = true

    try {
      // 添加用户消息（表示这是音频输入）
      const userMessage: Message = {
        id: Date.now(),
        role: 'user',
        content: `🎤 ${this.$t('aiChat.voiceMessage')}`,
        timestamp: new Date()
      }
      this.messages.push(userMessage)

      // 构建聊天历史
      const chatHistory: ChatMessage[] = this.messages
        .slice(0, -1) // 排除刚添加的用户消息
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }))

      // 转换音频为 ArrayBuffer
      const arrayBuffer = await audioBlob.arrayBuffer()
      
      // 调用 Gemini API with audio
      const aiResponse = await callGemini(
        { audioData: arrayBuffer, mimeType: 'audio/webm' },
        chatHistory
      )
      
      // 提取语种信息
      const languageMatch = aiResponse.match(/\[LANGUAGE:([^\]]+)\]/)
      if (languageMatch) {
        const detectedLanguage = languageMatch[1]
        console.log('🌍 检测到语种:', detectedLanguage)
        
        // 映射并设置全局语言
        await this.mapAndSetGlobalLanguage(detectedLanguage)
        
        // 移除语种标记，只显示实际回复内容
        const cleanResponse = aiResponse.replace(/\[LANGUAGE:[^\]]+\]\s*\n?/, '')
        
        const aiMessage: Message = {
          id: Date.now() + 1,
          role: 'assistant',
          content: cleanResponse,
          timestamp: new Date()
        }
        
        this.messages.push(aiMessage)
        
        // 保存到 PocketBase
        await this.saveConversationToPocketBase(`🎤 ${this.$t('aiChat.voiceMessage')}`, cleanResponse)
      } else {
        // 没有检测到语种标记，使用原始响应
        console.log('⚠️ 未能识别语种')
        
        const aiMessage: Message = {
          id: Date.now() + 1,
          role: 'assistant',
          content: aiResponse,
          timestamp: new Date()
        }
        
        this.messages.push(aiMessage)
        
        // 保存到 PocketBase
        await this.saveConversationToPocketBase(`🎤 ${this.$t('aiChat.voiceMessage')}`, aiResponse)
      }
      
    } catch (error) {
      console.error('Error processing audio:', error)
      const errorMessage: Message = {
        id: Date.now() + 1,
        role: 'assistant',
        content: this.$t('aiChat.error'),
        timestamp: new Date()
      }
      this.messages.push(errorMessage)
    } finally {
      this.isLoading = false
      this.scrollToBottom()
    }
  }

  // 输入变化处理
  onInputChange() {
    // 这里可以添加打字指示器逻辑
  }

  // 输入框聚焦处理
  onInputFocus() {
    this.isInputFocused = true
    // 在移动设备上检测键盘高度
    this.adjustForKeyboard()
  }

  // 输入框失焦处理
  onInputBlur() {
    this.isInputFocused = false
    this.inputKeyboardOffset = 0
  }
  
  // 处理粘贴事件
  onPaste(event: ClipboardEvent) {
    // 允许默认粘贴行为
    // 如果需要自定义粘贴行为，可以在这里添加代码
    console.log('粘贴内容到输入框')
  }

  // 调整输入框位置以适应虚拟键盘
  adjustForKeyboard() {
    // 检测是否在移动设备上
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    
    if (isMobile) {
      // 监听视窗高度变化来检测键盘
      const initialHeight = window.innerHeight
      
      const handleResize = () => {
        const currentHeight = window.innerHeight
        const heightDifference = initialHeight - currentHeight
        
        // 如果高度差超过150px，认为是键盘弹出
        if (heightDifference > 150) {
          this.inputKeyboardOffset = Math.max(0, heightDifference - 100)
        } else {
          this.inputKeyboardOffset = 0
        }
      }
      
      window.addEventListener('resize', handleResize)
      
      // 组件销毁时清理监听器
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', handleResize)
      })
    }
  }

  // 保存对话到PocketBase
  async saveConversationToPocketBase(userMessage: string, aiResponse: string) {
    try {
      if (!this.sessionId) {
        // 每次打开页面都创建新会话
        this.sessionId = await saveConversation(userMessage, aiResponse)
      } else {
        // 继续在现有会话中保存
        // 使用 saveMessage 方法直接保存消息到现有会话
        // 保存用户消息
        await saveMessage(this.sessionId, 'user', userMessage)
        
        // 保存AI回复
        await saveMessage(this.sessionId, 'assistant', aiResponse)
      }
    } catch (error) {
      // 忽略AbortError，这通常是由于组件卸载导致的
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('保存对话请求被取消（可能是页面切换）')
        return
      }
      console.error('保存对话失败:', error)
      // 不要让保存失败影响用户体验
    }
  }

  // 格式化时间
  formatTime(timestamp: Date): string {
    return timestamp.toLocaleTimeString(this.$currentLanguage, {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 滚动到底部
  scrollToBottom() {
    this.$nextTick(() => {
      const scrollContainer = document.querySelector('.scrollable-content-area') as HTMLElement
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    })
  }

  // 重置对话（每次打开页面都是新对话）
  resetChat() {
    this.messages = []
    this.sessionId = null
    this.userMessage = ''
    this.isLoading = false
    this.isListening = false
  }

  // 检测文字输入的语言并自动切换
  async detectAndSwitchLanguage(text: string) {
    try {
      // 检测文字中的语言特征
      const detectedLanguage = this.detectTextLanguage(text)
      
      if (detectedLanguage) {
        console.log(`🔍 检测到文字语言: ${detectedLanguage}`)
        
        // 映射并设置全局语言
        await this.mapAndSetGlobalLanguage(detectedLanguage)
      }
    } catch (error) {
      console.error('检测文字语言失败:', error)
    }
  }

  // 基于文字内容检测语言
  detectTextLanguage(text: string): string | null {
    // 移除空格和标点符号进行更准确的检测
    const cleanText = text.trim()
    
    // 如果文本太短，不进行检测
    if (cleanText.length < 2) {
      return null
    }

    // 中文检测（包含中文字符）
    const chineseRegex = /[\u4e00-\u9fff]/
    if (chineseRegex.test(cleanText)) {
      return 'zh'
    }

    // 日语检测（包含平假名、片假名）
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/
    if (japaneseRegex.test(cleanText)) {
      return 'ja'
    }

    // 韩语检测（包含韩文字符）
    const koreanRegex = /[\uac00-\ud7af]/
    if (koreanRegex.test(cleanText)) {
      return 'ko'
    }

    // 泰语检测（包含泰文字符）
    const thaiRegex = /[\u0e00-\u0e7f]/
    if (thaiRegex.test(cleanText)) {
      return 'th'
    }

    // 西班牙语检测（包含西班牙语特殊字符或关键词）
    const spanishRegex = /[ñáéíóúü]/i
    const spanishWords = /\b(hola|gracias|por favor|buenos días|buenas tardes|cómo|qué|dónde|cuándo|español|sí|no|muy|bien|mal|grande|pequeño|casa|tiempo|agua|comida|trabajo|familia|amigo|ciudad|país|mundo|vida|año|día|hora|minuto|segundo|dinero|persona|gente|mujer|hombre|niño|niña|padre|madre|hijo|hija|hermano|hermana|escuela|universidad|médico|hospital|restaurante|hotel|aeropuerto|estación|banco|tienda|mercado|iglesia|museo|parque|playa|montaña|río|lago|mar|océano|cielo|sol|luna|estrella|nube|lluvia|viento|calor|frío|primavera|verano|otoño|invierno|lunes|martes|miércoles|jueves|viernes|sábado|domingo|enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)\b/i
    if (spanishRegex.test(cleanText) || spanishWords.test(cleanText)) {
      return 'es'
    }

    // 英语检测（如果包含常见英语词汇且不包含其他语言特征）
    const englishWords = /\b(hello|hi|thank you|thanks|please|good morning|good afternoon|good evening|how|what|where|when|why|who|english|yes|no|very|good|bad|big|small|house|time|water|food|work|family|friend|city|country|world|life|year|day|hour|minute|second|money|person|people|woman|man|child|boy|girl|father|mother|son|daughter|brother|sister|school|university|doctor|hospital|restaurant|hotel|airport|station|bank|store|shop|market|church|museum|park|beach|mountain|river|lake|sea|ocean|sky|sun|moon|star|cloud|rain|wind|hot|cold|spring|summer|autumn|winter|monday|tuesday|wednesday|thursday|friday|saturday|sunday|january|february|march|april|may|june|july|august|september|october|november|december)\b/i
    if (englishWords.test(cleanText)) {
      return 'en'
    }

    // 如果没有检测到特定语言，返回 null
    return null
  }

  // 映射并设置全局语言
  async mapAndSetGlobalLanguage(detectedLanguage: string) {
    try {
      // 语种映射表：Gemini API 返回的语种代码 -> 应用支持的语言代码
      const languageMapping: { [key: string]: string } = {
        // 中文映射
        'zh': 'zh-TW',
        'zh-CN': 'zh-TW',
        'zh-TW': 'zh-TW',
        'zh-HK': 'zh-TW',
        'chinese': 'zh-TW',
        'mandarin': 'zh-TW',
        'cantonese': 'zh-TW',
        
        // 英文映射
        'en': 'en',
        'en-US': 'en',
        'en-GB': 'en',
        'english': 'en',
        
        // 西班牙文映射
        'es': 'es',
        'es-ES': 'es',
        'es-MX': 'es',
        'spanish': 'es',
        'español': 'es',
        
        // 日文映射
        'ja': 'ja',
        'ja-JP': 'ja',
        'japanese': 'ja',
        '日本語': 'ja',
        
        // 韩文映射
        'ko': 'ko',
        'ko-KR': 'ko',
        'korean': 'ko',
        '한국어': 'ko',
        
        // 泰文映射
        'th': 'th',
        'th-TH': 'th',
        'thai': 'th',
        'ภาษาไทย': 'th'
      }
      
      // 规范化检测到的语种代码（转小写并去除空格）
      const normalizedDetected = detectedLanguage.toLowerCase().trim()
      
      // 查找映射的语言代码
      let mappedLanguage = languageMapping[normalizedDetected]
      
      // 如果没有直接映射，尝试部分匹配
      if (!mappedLanguage) {
        for (const [key, value] of Object.entries(languageMapping)) {
          if (normalizedDetected.includes(key) || key.includes(normalizedDetected)) {
            mappedLanguage = value
            break
          }
        }
      }
      
      // 如果找到了有效的映射
      if (mappedLanguage && this.$supportedLanguages.some(lang => lang.code === mappedLanguage)) {
        const currentLang = this.$currentLanguage
        
        if (currentLang !== mappedLanguage) {
          console.log(`🔄 语言切换: ${currentLang} -> ${mappedLanguage}`)
          
          // 设置全局语言
          this.$setLanguage(mappedLanguage as any)
          
          // 等待一帧，确保语言设置生效
          await this.$nextTick()
          
          // 显示语言切换提示
          const langConfig = this.$getLanguageConfig(mappedLanguage as any)
          if (langConfig) {
            // 使用 setTimeout 确保消息在 UI 更新后显示
            setTimeout(() => {
              console.log(`✅ 已自动切换语言为: ${langConfig.nativeName}`)
              // 可以在这里添加 toast 通知
            }, 100)
          }
        } else {
          console.log(`📌 检测到当前语言 ${mappedLanguage}，无需切换`)
        }
      } else {
        console.warn(`⚠️ 无法映射语种 "${detectedLanguage}" 到支持的语言`)
      }
      
    } catch (error) {
      console.error('设置全局语言失败:', error)
    }
  }
  
  // 页面创建时重置对话
  created() {
    this.resetChat()
  }

  // 组件销毁时清理
  beforeDestroy() {
    // 取消所有正在进行的请求
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
    
    // 停止语音识别
    if (this.recognition && this.isListening) {
      this.recognition.stop()
    }
    
    console.log('AIChat组件已销毁，清理完成')
  }
}
</script>

<style scoped>
/* 欢迎卡片 */
.welcome-container {
  position: relative;
  width: 1400px;
  margin: 100px auto 60px;
  z-index: 15;
}

.welcome-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 60px;
  background: var(--glass-bg-normal);
  border: 3px solid var(--glass-border-light);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--blur-light);
  box-shadow: var(--shadow-glass-inset);
  transition: var(--transition-standard);
}

.welcome-card:hover {
  background: var(--glass-bg-medium);
  border-color: var(--glass-border-medium);
  transform: translateY(-4px);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-drop-heavy);
}

.winnie-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--glass-bg-heavy);
  border: 3px solid var(--glass-border-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 72px;
  transition: var(--transition-standard);
}

.avatar-text {
  font-size: 72px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.welcome-content {
  flex: 1;
  text-align: center;
}

.welcome-title {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-white);
  margin: 0 0 20px 0;
  line-height: var(--line-height-standard);
}

.welcome-message {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.4;
}

/* 聊天消息区域 */
.chat-messages {
  width: 100%;
  min-height: 200px;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  animation: fadeInUp 0.3s ease-out;
}

.message-wrapper.user {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.message-wrapper.assistant {
  flex-direction: row;
  justify-content: flex-start;
}

.message-bubble {
  max-width: 60%;
  padding: 30px 40px;
  border-radius: var(--radius-md);
  position: relative;
  backdrop-filter: var(--blur-light);
  border: 2px solid var(--glass-border-light);
  box-shadow: var(--shadow-glass-inset);
  transition: var(--transition-standard);
}

.message-wrapper.user .message-bubble {
  background: var(--glass-bg-medium);
  border-color: rgba(0, 238, 255, 0.3);
  margin-left: auto;
}

.message-wrapper.assistant .message-bubble {
  background: var(--glass-bg-normal);
  border-color: var(--glass-border-light);
  margin-right: auto;
}

.message-content {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--color-white);
  line-height: 1.5;
  margin-bottom: 15px;
  word-wrap: break-word;
}

.message-time {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.6);
  text-align: right;
  font-weight: var(--font-weight-light);
}

.message-wrapper.user .message-time {
  text-align: left;
}

/* 加载指示器 */
.loading-message {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  animation: fadeInUp 0.3s ease-out;
}

.loading-bubble {
  max-width: 60%;
  padding: 30px 40px;
  border-radius: var(--radius-md);
  background: var(--glass-bg-normal);
  border: 2px solid var(--glass-border-light);
  backdrop-filter: var(--blur-light);
  box-shadow: var(--shadow-glass-inset);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 输入区域 */
.input-area {
  /* 样式通过计算属性动态设置 */
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: var(--glass-bg-normal);
  border: 3px solid var(--glass-border-light);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--blur-light);
  box-shadow: var(--shadow-glass-inset);
  transition: var(--transition-standard);
}

.input-wrapper:hover {
  background: var(--glass-bg-medium);
  border-color: var(--glass-border-medium);
  box-shadow: var(--shadow-glass-inset-hover), var(--shadow-drop-medium);
}

.message-input {
  flex: 1;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  color: var(--color-white);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  outline: none;
  transition: var(--transition-standard);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.message-input:focus {
  border-color: var(--color-primary);
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 2px rgba(0, 238, 255, 0.3);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  gap: 15px;
}

.voice-button,
.send-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-standard);
}

.voice-button.active {
  background: rgba(255, 82, 82, 0.2);
  border-color: rgba(255, 82, 82, 0.5);
  box-shadow: var(--shadow-danger-glow), var(--shadow-glass-inset);
}

.voice-button.active .voice-icon {
  color: #FF5252;
  animation: pulse 2s infinite;
}

.send-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-icon,
.send-icon {
  color: var(--color-white);
  transition: var(--transition-standard);
}

.send-button:hover .send-icon {
  color: var(--color-primary);
  transform: scale(1.1);
}

/* 语音状态指示器 */
.voice-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 82, 82, 0.1);
  border: 2px solid rgba(255, 82, 82, 0.3);
  border-radius: var(--radius-md);
  backdrop-filter: var(--blur-light);
}

.voice-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.voice-wave {
  width: 4px;
  height: 20px;
  background: #FF5252;
  border-radius: 2px;
  animation: voiceWave 1.2s infinite;
}

.voice-wave:nth-child(1) {
  animation-delay: 0s;
}

.voice-wave:nth-child(2) {
  animation-delay: 0.2s;
}

.voice-wave:nth-child(3) {
  animation-delay: 0.4s;
}

.voice-text {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  color: #FF5252;
  font-weight: var(--font-weight-medium);
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes voiceWave {
  0%, 100% {
    height: 8px;
  }
  50% {
    height: 24px;
  }
}
/* 滚动条样式已在design-system.css中定义 */

/* 录音按钮状态 */
.voice-button.recording {
  background: rgba(255, 82, 82, 0.3) !important;
  border-color: #FF5252 !important;
  animation: recordingPulse 1.5s infinite;
}

@keyframes recordingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(255, 82, 82, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 82, 82, 0);
  }
}

/* 音波动画 */
.voice-animation {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
}

.voice-animation .sound-wave {
  width: 6px;
  background: #FFFFFF;
  border-radius: 3px;
  animation: soundWave 1s ease-in-out infinite;
}

.voice-animation .sound-wave:nth-child(1) {
  height: 15px;
  animation-delay: 0s;
}

.voice-animation .sound-wave:nth-child(2) {
  height: 25px;
  animation-delay: 0.15s;
}

.voice-animation .sound-wave:nth-child(3) {
  height: 20px;
  animation-delay: 0.3s;
}

@keyframes soundWave {
  0%, 100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1.2);
  }
}

/* 录音动画容器 - 显示在语音按钮上方 */
.recording-animation-container {
  position: absolute;
  bottom: 160px; /* 在输入框上方 */
  left: 20px; /* 对齐语音按钮 */
  width: 900px; /* 放大3倍: 300px * 3 */
  height: 460px; /* 增加高度以容纳文字: 360px + 100px */
  display: flex;
  flex-direction: column; /* 垂直排列文字和动画 */
  align-items: flex-start; /* 左对齐 */
  justify-content: flex-end; /* 底部对齐 */
  pointer-events: none; /* 不影响其他交互 */
  z-index: 30;
  gap: 30px; /* 文字和动画之间的间距 */
}

/* 录音引导文字 */
.recording-guide-text {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md); /* 使用设计系统变量 48px */
  font-weight: var(--font-weight-medium);
  color: #FF5252;
  text-align: center;
  width: 100%;
  padding: 20px;
  background: rgba(255, 82, 82, 0.1);
  border: 3px solid rgba(255, 82, 82, 0.3);
  border-radius: 30px;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0 0 20px rgba(255, 82, 82, 0.2);
}

.recording-box {
  background: rgba(255, 82, 82, 0.1);
  border: 9px solid rgba(255, 82, 82, 0.3); /* 放大3倍: 3px * 3 */
  border-radius: 60px; /* 放大3倍: 20px * 3 */
  padding: 90px 120px; /* 放大3倍: 30px * 3, 40px * 3 */
  backdrop-filter: blur(10px);
  display: flex;
  gap: 36px; /* 放大3倍: 12px * 3 */
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  align-self: flex-start; /* 左对齐 */
}

.recording-box .sound-wave {
  width: 24px; /* 放大3倍: 8px * 3 */
  background: #FF5252;
  border-radius: 12px; /* 放大3倍: 4px * 3 */
  animation: recordingWave 1.2s ease-in-out infinite;
}

.recording-box .sound-wave:nth-child(1) {
  height: 60px; /* 放大3倍: 20px * 3 */
  animation-delay: 0s;
}

.recording-box .sound-wave:nth-child(2) {
  height: 105px; /* 放大3倍: 35px * 3 */
  animation-delay: 0.1s;
}

.recording-box .sound-wave:nth-child(3) {
  height: 75px; /* 放大3倍: 25px * 3 */
  animation-delay: 0.2s;
}

.recording-box .sound-wave:nth-child(4) {
  height: 120px; /* 放大3倍: 40px * 3 */
  animation-delay: 0.3s;
}

.recording-box .sound-wave:nth-child(5) {
  height: 90px; /* 放大3倍: 30px * 3 */
  animation-delay: 0.4s;
}

@keyframes recordingWave {
  0%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

</style>

