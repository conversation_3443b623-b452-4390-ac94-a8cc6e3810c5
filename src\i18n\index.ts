// 國際化配置
export type Language = 'zh-TW' | 'en' | 'es' | 'ja' | 'ko' | 'th'

export interface LanguageConfig {
  code: Language
  name: string
  nativeName: string
}

// 支持的語言列表
export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  { code: 'zh-TW', name: 'Traditional Chinese', nativeName: '繁體中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'th', name: 'Thai', nativeName: 'ภาษาไทย' }
]

// 預設語言
export const DEFAULT_LANGUAGE: Language = 'zh-TW'

// 語言儲存key
export const LANGUAGE_STORAGE_KEY = 'emap_language'

// 語言服務類
class I18nService {
  private currentLanguage: Language = DEFAULT_LANGUAGE
  private translations: Record<Language, Record<string, any>> = {} as any

  constructor() {
    this.loadLanguage()
  }

  /**
   * 載入當前語言設置
   */
  private loadLanguage(): void {
    try {
      const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (stored && this.isValidLanguage(stored)) {
        this.currentLanguage = stored as Language
      }
    } catch (error) {
      console.warn('無法載入語言設置，使用預設語言')
    }
  }

  /**
   * 驗證語言代碼是否有效
   */
  private isValidLanguage(lang: string): boolean {
    return SUPPORTED_LANGUAGES.some(l => l.code === lang)
  }

  /**
   * 設置語言包
   */
  setTranslations(language: Language, translations: Record<string, any>): void {
    this.translations[language] = translations
  }

  /**
   * 獲取當前語言
   */
  getCurrentLanguage(): Language {
    return this.currentLanguage
  }

  /**
   * 設置當前語言
   */
  setCurrentLanguage(language: Language): void {
    if (this.isValidLanguage(language)) {
      this.currentLanguage = language
      try {
        localStorage.setItem(LANGUAGE_STORAGE_KEY, language)
      } catch (error) {
        console.warn('無法儲存語言設置')
      }
    }
  }

  /**
   * 翻譯文字
   */
  t(key: string, fallback?: string): string {
    const translation = this.getNestedTranslation(key)
    return translation || fallback || key
  }

  /**
   * 獲取嵌套翻譯
   */
  private getNestedTranslation(key: string): string | null {
    const keys = key.split('.')
    let translation = this.translations[this.currentLanguage]

    if (!translation) {
      console.warn(`翻譯包未加載: ${this.currentLanguage}`)
      return null
    }

    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k]
      } else {
        return null
      }
    }

    return typeof translation === 'string' ? translation : null
  }

  /**
   * 獲取語言配置
   */
  getLanguageConfig(language?: Language): LanguageConfig | null {
    const lang = language || this.currentLanguage
    return SUPPORTED_LANGUAGES.find(l => l.code === lang) || null
  }

  /**
   * 獲取所有支持的語言
   */
  getSupportedLanguages(): LanguageConfig[] {
    return SUPPORTED_LANGUAGES
  }
}

// 導出單例
export const i18nService = new I18nService()
export default i18nService 