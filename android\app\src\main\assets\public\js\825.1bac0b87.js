"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[825],{2825:(t,e,a)=>{a.r(e),a.d(e,{default:()=>_});var o=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"food-page responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--food"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.food")))])]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--standard"},[e("div",{staticClass:"food-content"},[e("div",{staticClass:"food-card"},[e("h2",{staticClass:"content-title"},[t._v(t._s(t.$t("food.title")))]),e("div",{staticClass:"features"},[e("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("food.comingSoon")))])])])])])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),e("BottomMarquee")],1)},s=[],n=a(1635),c=a(9603),i=a(3452),l=a(3205),r=a(4184),d=a(256),u=a(5185),g=a(7959);let p=class extends((0,c.Xe)(u.A,g.A)){getBackgroundColor(){return"#DC2626"}};p=(0,n.Cg)([(0,c.uA)({components:{TopBar:i.A,BottomBar:l.A,BottomMarquee:r.A,BackgroundImage:d.A}})],p);const C=p,f=C;var m=a(1656),v=(0,m.A)(f,o,s,!1,null,"30192ecc",null);const _=v.exports}}]);