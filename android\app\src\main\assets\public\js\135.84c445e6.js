"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[135],{6039:(o,e,t)=>{t.r(e),t.d(e,{default:()=>N});var a=function(){var o=this,e=o._self._c;o._self._setupProxy;return e("div",{staticClass:"office-container responsive-page-container",style:o.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--office"},[e("h1",{staticClass:"app-title"},[o._v(o._s(o.$t("pageTitle.office")))])]),e("div",{staticClass:"filter-buttons-fixed"},[e("button",{staticClass:"filter-btn by-floor",class:{active:"floor"===o.sortBy},on:{click:function(e){o.sortBy="floor"}}},[o._v(" "+o._s(o.$t("office.byFloor"))+" ")]),e("button",{staticClass:"filter-btn by-name",class:{active:"name"===o.sortBy},on:{click:function(e){o.sortBy="name"}}},[o._v(" "+o._s(o.$t("office.byName"))+" ")])]),e("div",{staticClass:"scrollable-content-area",style:o.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--office"},[e("div",{staticClass:"office-list"},o._l(o.sortedOffices,function(o){return e("OfficeCard",{key:o.id,staticClass:"office-item",attrs:{"company-name":o.name,"room-number":o.room,"logo-src":o.logo}})}),1)])]),e("BottomBar",{on:{"home-clicked":o.onHomeClick,"language-changed":o.onLanguageChange,"ai-clicked":o.onAIClick}}),e("BottomMarquee")],1)},n=[],i=t(1635),r=t(9603),s=t(3452),l=t(3205),c=t(4184),f=function(){var o=this,e=o._self._c;o._self._setupProxy;return e("BaseCard",{attrs:{title:o.companyName,subtitle:o.roomNumber,"icon-src":o.logoSrc,size:"extra-large",variant:"office",layout:"horizontal"}})},m=[],g=t(9099);let p=class extends r.lD{companyName;roomNumber;logoSrc};(0,i.Cg)([(0,r.kv)({required:!0})],p.prototype,"companyName",void 0),(0,i.Cg)([(0,r.kv)({required:!0})],p.prototype,"roomNumber",void 0),(0,i.Cg)([(0,r.kv)()],p.prototype,"logoSrc",void 0),p=(0,i.Cg)([(0,r.uA)({components:{BaseCard:g.A}})],p);const u=p,d=u;var y=t(1656),C=(0,y.A)(d,f,m,!1,null,null,null);const v=C.exports;var B=t(256),b=t(5185),k=t(7959);let A=class extends((0,r.Xe)(b.A,k.A)){getBackgroundColor(){return"#4A4A4A"}sortBy="name";offices=[{id:1,name:"Tesla Inc Kong Kong Branch",room:"104",floor:1,logo:"/img/offices/tesla-logo.png"},{id:2,name:"Merrill Lynch",room:"812",floor:8,logo:"/img/offices/merrill-lynch-logo.png"},{id:3,name:"Apple Inc Hong Kong",room:"903",floor:9,logo:"/img/offices/apple-logo.png"},{id:4,name:"Xiaomi",room:"222",floor:2,logo:"/img/offices/xiaomi-logo.png"},{id:5,name:"Microsoft Hong Kong 微軟香港",room:"888",floor:8,logo:"/img/offices/microsoft-logo.png"},{id:6,name:"Amazon",room:"232",floor:2,logo:"/img/offices/amazon-logo.png"},{id:7,name:"Facebook",room:"721",floor:7,logo:"/img/offices/facebook-logo.png"}];get sortedOffices(){const o=[...this.offices];return"name"===this.sortBy?o.sort((o,e)=>o.name.localeCompare(e.name)):o.sort((o,e)=>o.floor-e.floor)}};A=(0,i.Cg)([(0,r.uA)({components:{TopBar:s.A,BottomBar:l.A,BottomMarquee:c.A,OfficeCard:v,BackgroundImage:B.A}})],A);const _=A,h=_;var x=(0,y.A)(h,a,n,!1,null,"de78f754",null);const N=x.exports}}]);