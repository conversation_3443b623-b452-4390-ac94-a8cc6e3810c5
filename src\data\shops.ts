export interface ShopData {
  id: string
  name: string
  name_tc: string
  name_zh: string
  location: string
  hours: string
  tel: string
  description: string
  logo?: string
}

export interface ShopBasicInfo {
  id: string
  name: string
  name_tc: string
  name_zh: string
  logo?: string
}

// 重新导出服务层的方法以保持向后兼容
export { 
  getShopsData, 
  clearShopCache,
  isValidShopId 
} from '@/services/shopService'

// 为了兼容性，重新导出异步方法
export { 
  getShopsBasicInfo,
  getShopData
} from '@/services/shopService' 