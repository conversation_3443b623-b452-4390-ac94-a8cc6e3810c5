"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[126],{126:(t,a,e)=>{e.r(a),e.d(a,{default:()=>p});var s=function(){var t=this,a=t._self._c;t._self._setupProxy;return a("div",{staticClass:"about-page responsive-page-container",style:t.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--about"},[a("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.about")))])]),a("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--standard"},[a("div",{staticClass:"about-content"},[a("div",{staticClass:"about-card"},[a("h2",{staticClass:"content-title"},[t._v(t._s(t.$t("about.title")))]),a("div",{staticClass:"tech-stack"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.techStack")))]),a("ul",{staticClass:"tech-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.techStack.vue")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.typescript")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.tailwind")))]),a("li",[t._v(t._s(t.$t("aboutDetail.techStack.capacitor")))])])]),a("div",{staticClass:"features"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.features")))]),a("ul",{staticClass:"feature-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.features.smartNavigation")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.realtimeLocation")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.multiLanguage")))]),a("li",[t._v(t._s(t.$t("aboutDetail.features.crossPlatform")))])])]),a("div",{staticClass:"extra-sections"},[a("div",{staticClass:"version-info"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.version")))]),a("ul",{staticClass:"version-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.version.current")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.releaseDate")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.updateFrequency")))]),a("li",[t._v(t._s(t.$t("aboutDetail.version.supportedPlatforms")))])])]),a("div",{staticClass:"team-info"},[a("h3",{staticClass:"section-title"},[t._v(t._s(t.$t("about.team")))]),a("ul",{staticClass:"team-list"},[a("li",[t._v(t._s(t.$t("aboutDetail.team.frontend")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.mobile")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.design")))]),a("li",[t._v(t._s(t.$t("aboutDetail.team.data")))])])])])])])])]),a("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},i=[],l=e(635),o=e(233),c=e(14),n=e(958),u=e(256),r=e(185),_=e(959);let v=class extends((0,o.Xe)(r.A,_.A)){getBackgroundColor(){return"#34495E"}};v=(0,l.Cg)([(0,o.uA)({components:{TopBar:c.A,BottomBar:n.A,BackgroundImage:u.A}})],v);const d=v,b=d;var C=e(656),m=(0,C.A)(b,s,i,!1,null,"d108ef86",null);const p=m.exports}}]);