"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[367],{150:(t,e,i)=>{i.d(e,{A:()=>g});var a=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{class:t.cardClasses,on:{click:function(e){return t.$emit("click")}}},[t.showIcon?e("div",{class:t.iconContainerClasses},[t.iconSrc?e("img",{class:t.iconImageClasses,attrs:{src:t.iconSrc,alt:t.title}}):t.showPlaceholder?e("div",{class:t.placeholderClasses},[t._v(" "+t._s(t.placeholderText)+" ")]):t._e()]):t._e(),t.showContent?e("div",{class:t.contentClasses},[t._t("default",function(){return[t.title?e("div",{class:t.titleClasses},[t._v(" "+t._s(t.title)+" ")]):t._e(),t.subtitle?e("div",{class:t.subtitleClasses},[t._v(" "+t._s(t.subtitle)+" ")]):t._e()]})],2):t._e(),t._t("custom")],2)},l=[],c=i(635),s=i(233);let r=class extends s.lD{title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const t=["card-base","text-primary"],e={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},i={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},a={vertical:"card-vertical",horizontal:"card-horizontal"};return[...t,e[this.size],i[this.variant],a[this.layout]]}get iconContainerClasses(){const t=["icon-container"],e={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...t,e[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const t=["icon-placeholder"],e={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...t,e[this.size]]}get contentClasses(){const t=["card-content"],e={vertical:"content-vertical",horizontal:"content-horizontal"};return[...t,e[this.layout]]}get titleClasses(){const t=["card-title","text-primary"],e={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},i={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...t,e[this.size],i[this.variant]]}get subtitleClasses(){const t=["card-subtitle","text-primary"],e={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...t,e[this.size]]}};(0,c.Cg)([(0,s.kv)({required:!0})],r.prototype,"title",void 0),(0,c.Cg)([(0,s.kv)()],r.prototype,"subtitle",void 0),(0,c.Cg)([(0,s.kv)()],r.prototype,"iconSrc",void 0),(0,c.Cg)([(0,s.kv)({default:"medium"})],r.prototype,"size",void 0),(0,c.Cg)([(0,s.kv)({default:"facility"})],r.prototype,"variant",void 0),(0,c.Cg)([(0,s.kv)({default:"vertical"})],r.prototype,"layout",void 0),(0,c.Cg)([(0,s.kv)({default:!0})],r.prototype,"showIcon",void 0),(0,c.Cg)([(0,s.kv)({default:!0})],r.prototype,"showContent",void 0),(0,c.Cg)([(0,s.kv)({default:!0})],r.prototype,"showPlaceholder",void 0),r=(0,c.Cg)([s.uA],r);const o=r,n=o;var d=i(656),m=(0,d.A)(n,a,l,!1,null,"022da4db",null);const g=m.exports},367:(t,e,i)=>{i.r(e),i.d(e,{default:()=>w});var a=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"facility-container responsive-page-container",style:t.containerStyle},[e("BackgroundImage"),e("TopBar"),e("div",{staticClass:"page-title-fixed page-title-fixed--facility"},[e("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.facility")))])]),e("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[e("div",{staticClass:"main-content-container main-content-container--standard"},[e("div",{staticClass:"map-container"},[e("div",{staticClass:"map-placeholder"},[e("img",{staticClass:"map-image",attrs:{src:"/img/block.png",alt:"Facility Map"},on:{error:t.onImageError}})])]),e("div",{staticClass:"facilities-grid"},[t._l(t.facilities,function(i){return e("FacilityCard",{key:i.id,attrs:{"facility-id":i.id,"facility-name":i.name,"icon-src":i.icon},on:{"facility-clicked":t.onFacilityClick}})}),t._l(t.extraFacilities,function(i){return e("FacilityCard",{key:`extra-${i.id}`,attrs:{"facility-id":i.id,"facility-name":i.name,"icon-src":i.icon},on:{"facility-clicked":t.onFacilityClick}})})],2)])]),e("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}})],1)},l=[],c=i(635),s=i(233),r=i(14),o=i(958),n=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("BaseCard",{attrs:{title:t.facilityName,"icon-src":t.iconSrc,size:"medium",variant:"facility",layout:"vertical"},on:{click:function(e){return t.$emit("click")}}})},d=[],m=i(150);let g=class extends s.lD{facilityName;iconSrc};(0,c.Cg)([(0,s.kv)({required:!0})],g.prototype,"facilityName",void 0),(0,c.Cg)([(0,s.kv)()],g.prototype,"iconSrc",void 0),g=(0,c.Cg)([(0,s.uA)({components:{BaseCard:m.A}})],g);const u=g,p=u;var f=i(656),v=(0,f.A)(p,n,d,!1,null,null,null);const y=v.exports;var C=i(256),h=i(185),k=i(959);let _=class extends((0,s.Xe)(h.A,k.A)){getBackgroundColor(){return"#016513"}facilities=[{id:"men",name:"Men",icon:"/img/facilities/men.png"},{id:"women",name:"Women",icon:"/img/facilities/women.png"},{id:"baby",name:"Baby",icon:"/img/facilities/baby.png"},{id:"services",name:"Services",icon:"/img/facilities/services.png"},{id:"lift",name:"Lift",icon:"/img/facilities/lift.png"},{id:"escalator",name:"Escalator",icon:"/img/facilities/escalator.png"},{id:"accessibly",name:"Accessibly",icon:"/img/facilities/accessibly.png"},{id:"locker",name:"Locker",icon:"/img/facilities/locker.png"}];extraFacilities=[];onFacilityClick(t){console.log("点击设施:",t)}onImageError(t){const e=t.target;e&&(e.src="/img/placeholder-transport.png")}};_=(0,c.Cg)([(0,s.uA)({components:{TopBar:r.A,BottomBar:o.A,FacilityCard:y,BackgroundImage:C.A}})],_);const x=_,b=x;var A=(0,f.A)(b,a,l,!1,null,"584e57c6",null);const w=A.exports}}]);