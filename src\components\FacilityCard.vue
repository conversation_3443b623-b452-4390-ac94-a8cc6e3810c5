<template>
  <BaseCard
    :title="facilityName"
    :icon-src="iconSrc"
    size="medium"
    variant="facility"
    layout="vertical"
    @click="$emit('click')"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import BaseCard from './BaseCard.vue'

@Component({
  components: {
    BaseCard
  }
})
export default class FacilityCard extends Vue {
  @Prop({ required: true })
  facilityName!: string
  
  @Prop()
  iconSrc?: string
}
</script> 