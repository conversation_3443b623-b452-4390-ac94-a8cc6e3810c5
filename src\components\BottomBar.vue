<template>
  <div class="bottom-bar">
    <!-- AI按钮独立显示 -->
    <div class="ai-section">
      <GlassButton 
        size="small" 
        variant="normal"
        @click="onAIClick"
        class="ai-button"
      >
        <div class="ai-content">
          <img 
            src="/img/icon_ai.png" 
            alt="AI Icon" 
            class="ai-icon"
          />
          <span class="ai-text">ai</span>
        </div>
      </GlassButton>
    </div>

    <!-- 语言切换按钮组 -->
    <div class="nav-buttons">
      <!-- Home按钮 -->
      <GlassButton 
        size="small"
        variant="normal"
        @click="onHomeClick"
        class="nav-button"
      >
        <img 
          src="/img/icon_home.png" 
          alt="Home Icon" 
          class="home-icon"
        />
      </GlassButton>

      <!-- 英文按钮 -->
      <GlassButton 
        size="small"
        variant="normal"
        @click="() => switchLanguage('en')"
        class="nav-button"
      >
        <div class="lang-content">
          <span class="lang-text">Eng</span>
        </div>
      </GlassButton>

      <!-- 西班牙文按钮 -->
      <GlassButton 
        size="small"
        variant="normal"
        @click="() => switchLanguage('es')"
        class="nav-button"
      >
        <div class="lang-content">
          <span class="lang-text">Esp</span>
        </div>
      </GlassButton>

      <!-- 中文按钮 -->
      <GlassButton 
        size="small"
        variant="normal"
        @click="() => switchLanguage('zh-TW')"
        class="nav-button"
      >
        <div class="lang-content">
          <span class="lang-text">中</span>
        </div>
      </GlassButton>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import GlassButton from './GlassButton.vue'
import I18nMixin from '@/mixins/I18nMixin'
import { Language } from '@/i18n'

@Component({
  components: {
    GlassButton
  }
})
export default class BottomBar extends Mixins(I18nMixin) {
  onHomeClick() {
    this.$emit('home-clicked')
    // 避免重复导航到相同路由
    if (this.$route.path !== '/') {
      this.$router.push('/')
    }
  }

  switchLanguage(language: Language) {
    console.log(`[BottomBar] 開始切換語言到: ${language}`)
    
    // 直接切换到指定语言，确保全局生效
    this.$setLanguage(language)
    this.$emit('language-changed', language)
    
    // 添加視覺反饋
    this.showLanguageSwitchFeedback(language)
    
    // 立即触发语言更新，确保所有组件都更新
    this.$root.$emit('language-changed', language)
    this.$root.$forceUpdate()
  }

  showLanguageSwitchFeedback(language: Language) {
    // 簡單的視覺反饋，可以擴展為更豐富的動畫
    const config = this.$getLanguageConfig(language)
    if (config) {
      // 這裡可以添加toast通知或其他反饋機制
      console.log(`Language switched to: ${config.nativeName}`)
    }
  }

  onAIClick() {
    this.$emit('ai-clicked')
    // 导航到AI对话页面（新的AI功能）
    if (this.$route.path !== '/ai-chat') {
      this.$router.push('/ai-chat')
    }
  }
}
</script>

<style scoped>
.bottom-bar {
  position: absolute;
  bottom: 250px; /* 距离底部的距离 */
  left: 0;
  right: 0;
  height: 240px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

/* AI按钮区域 - 根据Figma设计稿位置 */
.ai-section {
  position: absolute;
  left: 270px; /* 根据Figma设计稿的x位置 */
  bottom: 0;
  width: 471px; /* 根据Figma设计稿的AI按钮宽度 */
  height: 232px; /* 根据Figma设计稿的AI按钮高度 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-button {
  width: 100%;
  height: 100%;
  border-radius: 22px; /* 根据Figma设计稿的圆角 */
  background: rgba(255, 255, 255, 0.11); /* 根据Figma设计稿的背景色 */
  border: 4px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: 4px 4px 4px 0px rgba(0, 0, 0, 0.25), 
              inset 0px 2px 4px 0px rgba(255, 255, 255, 0.25), 
              inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* AI按钮边框渐变效果 */
.ai-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 22px;
  padding: 4px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 1) 50%, 
    rgba(255, 255, 255, 0.05) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  pointer-events: none;
}

.ai-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 17px; /* 根据Figma设计稿的间距 */
  height: 100%;
  z-index: 1;
  position: relative;
}

.ai-icon {
  width: 108px; /* 根据Figma设计稿的图标尺寸 107.67px */
  height: 101px; /* 根据Figma设计稿的图标尺寸 101.33px */
  object-fit: contain;
  transition: all 0.3s ease;
}

.ai-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 96px; /* 根据Figma设计稿的字体大小 */
  line-height: 1.21;
  color: #00EEFF; /* 根据Figma设计稿的青色 */
  text-transform: lowercase;
  transition: all 0.3s ease;
}

/* AI按钮悬停效果 */
.ai-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  box-shadow: 4px 8px 8px 0px rgba(0, 0, 0, 0.3), 
              inset 0px 2px 4px 0px rgba(255, 255, 255, 0.3), 
              inset 0px -2px 4px 0px rgba(0, 0, 0, 0.25);
}

.ai-button:hover .ai-icon,
.ai-button:hover .ai-text {
  color: #00EEFF;
  filter: drop-shadow(0 0 8px rgba(0, 238, 255, 0.5));
}

/* 语言切换按钮组 - 根据Figma设计稿位置 */
.nav-buttons {
  position: absolute;
  left: 794px; /* 根据Figma设计稿的x位置 */
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px; /* 按钮之间的间距 */
  width: 1116px; /* 根据Figma设计稿的总宽度 */
  height: 240px; /* 根据Figma设计稿的高度 */
}

.nav-button {
  width: 236px; /* 根据Figma设计稿的按钮宽度 */
  height: 240px; /* 根据Figma设计稿的按钮高度 */
  position: relative;
  transition: all 0.3s ease;
  border-radius: 26px; /* 根据Figma设计稿的圆角 */
  background: rgba(255, 255, 255, 0.1); /* 根据Figma设计稿的背景色 */
  border: 5px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4), 
              inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}

/* 边框渐变效果 */
.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 26px;
  padding: 5px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 1) 50%, 
    rgba(255, 255, 255, 0.05) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  pointer-events: none;
}

/* 語言按鈕內容 */
.lang-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 1;
}

.lang-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500; /* 根据Figma设计稿的字重 */
  font-size: 64px; /* 根据Figma设计稿的字体大小 */
  line-height: 1.21;
  color: white;
  transition: all 0.3s ease;
}

/* Home按鈕圖標樣式 */
.home-icon {
  width: 72px; /* 根据Figma设计稿的图标尺寸 */
  height: 94px; /* 根据Figma设计稿的图标尺寸 */
  object-fit: contain;
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;
}

/* 懸停效果 */
.nav-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  box-shadow: inset 0px 2px 4px 0px rgba(255, 255, 255, 0.5), 
              inset 0px -2px 4px 0px rgba(0, 0, 0, 0.3),
              0px 8px 16px rgba(0, 0, 0, 0.2);
}

.nav-button:hover .lang-text {
  color: #FFFFFF;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

.nav-button:hover .home-icon {
  color: #00EEFF;
  filter: drop-shadow(0 0 8px rgba(0, 238, 255, 0.5));
}

/* 激活状态 */
.nav-button.active {
  background: rgba(0, 238, 255, 0.2);
  border-color: #00EEFF;
  box-shadow: 0px 8px 16px 0px rgba(0, 238, 255, 0.3),
              inset 0px 2px 4px 0px rgba(255, 255, 255, 0.4), 
              inset 0px -2px 4px 0px rgba(0, 0, 0, 0.2);
}
</style> 