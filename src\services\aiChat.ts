import PocketBase from 'pocketbase';

// PocketBase 配置
const pb = new PocketBase('https://base.bwaiwork.xyz');

// Gemini API 配置
const GEMINI_API_KEY = process.env.VUE_APP_GEMINI_API_KEY || 'YOUR_API_KEY';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';

// 类型定义
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface Session {
  id: string;
  title: string;
  summary?: string;
  created: Date;
  updated: Date;
  _byemap?: any;
}

export interface Conversation {
  id: string;
  session_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  model: string;
  metadata?: any;
  created: Date;
  _byemap?: any;
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback?: {
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  };
}

class AIChatService {
  private currentSessionId: string | null = null;

  /**
   * 创建新的会话
   */
  async createSession(title?: string): Promise<Session> {
    const sessionTitle = title || `新对话 - ${new Date().toLocaleString('zh-CN')}`;
    
    try {
      const record = await pb.collection('ai_sessions_byemap').create({
        title: sessionTitle,
        summary: '',
        _byemap: {
          source: 'mall_kiosk',
          location: 'emap'
        }
      });

      this.currentSessionId = record.id;
      return this.mapSession(record);
    } catch (error) {
      console.error('创建会话失败:', error);
      throw new Error('无法创建新会话');
    }
  }

  /**
   * 获取当前会话ID，如果没有则创建新会话
   */
  async getCurrentSessionId(): Promise<string> {
    if (!this.currentSessionId) {
      const session = await this.createSession();
      this.currentSessionId = session.id;
    }
    return this.currentSessionId;
  }

  /**
   * 保存消息到数据库
   */
  async saveMessage(
    sessionId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    model: string = 'gemini-2.0-flash-exp',
    metadata?: any
  ): Promise<Conversation> {
    try {
      const record = await pb.collection('ai_conversations_byemap').create({
        session_id: sessionId,
        role,
        content,
        model,
        metadata: metadata || {},
        _byemap: {
          source: 'mall_kiosk',
          location: 'emap'
        }
      });

      return this.mapConversation(record);
    } catch (error) {
      console.error('保存消息失败:', error);
      throw new Error('无法保存消息');
    }
  }

  /**
   * 获取会话历史
   */
  async getSessionHistory(sessionId: string): Promise<Conversation[]> {
    try {
      const records = await pb.collection('ai_conversations_byemap').getFullList({
        filter: `session_id = "${sessionId}"`,
        sort: 'created',
      });

      return records.map(record => this.mapConversation(record));
    } catch (error) {
      console.error('获取会话历史失败:', error);
      return [];
    }
  }

  /**
   * 调用 Gemini API
   */
  async callGemini(prompt: string, history: Message[] = []): Promise<string> {
    // 构建消息历史
    const contents = [
      ...history.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : msg.role,
        parts: [{ text: msg.content }]
      })),
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    // 添加系统提示
    const systemPrompt = {
      role: 'user',
      parts: [{
        text: `你是一个友好的商场AI助手，专门帮助顾客解答关于商场的各种问题。
请用简洁、友好的语言回答问题。如果问题涉及具体商店位置或营业时间，请提供准确信息。
始终保持礼貌和专业。回答时使用中文。`
      }]
    };

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [systemPrompt, ...contents],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API 错误:', errorText);
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('没有收到有效的回复');
      }

      const reply = data.candidates[0].content.parts[0].text;
      return reply || '抱歉，我没有理解您的问题。';

    } catch (error) {
      console.error('调用 Gemini API 失败:', error);
      throw new Error('AI 服务暂时不可用，请稍后再试');
    }
  }

  /**
   * 发送消息并获取回复
   */
  async sendMessage(message: string): Promise<{ userMessage: Conversation; aiReply: Conversation }> {
    try {
      // 获取或创建会话
      const sessionId = await this.getCurrentSessionId();

      // 保存用户消息
      const userMessage = await this.saveMessage(sessionId, 'user', message);

      // 获取会话历史
      const history = await this.getSessionHistory(sessionId);
      const messages: Message[] = history.slice(-10).map(conv => ({
        role: conv.role,
        content: conv.content
      }));

      // 调用 Gemini API
      const aiResponse = await this.callGemini(message, messages);

      // 保存 AI 回复
      const aiReply = await this.saveMessage(
        sessionId,
        'assistant',
        aiResponse,
        'gemini-2.0-flash-exp',
        {
          tokens: aiResponse.length, // 简单估算
          temperature: 0.7
        }
      );

      // 更新会话标题（如果是第一条消息）
      if (history.length === 0) {
        await this.updateSessionTitle(sessionId, message);
      }

      return { userMessage, aiReply };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  /**
   * 更新会话标题
   */
  private async updateSessionTitle(sessionId: string, firstMessage: string): Promise<void> {
    try {
      const title = firstMessage.length > 50 
        ? firstMessage.substring(0, 50) + '...' 
        : firstMessage;

      await pb.collection('ai_sessions_byemap').update(sessionId, {
        title,
        summary: firstMessage
      });
    } catch (error) {
      console.error('更新会话标题失败:', error);
    }
  }

  /**
   * 清除当前会话（开始新对话）
   */
  clearCurrentSession(): void {
    this.currentSessionId = null;
  }

  /**
   * 映射会话记录
   */
  private mapSession(record: any): Session {
    return {
      id: record.id,
      title: record.title,
      summary: record.summary,
      created: new Date(record.created),
      updated: new Date(record.updated),
      _byemap: record._byemap
    };
  }

  /**
   * 映射对话记录
   */
  private mapConversation(record: any): Conversation {
    return {
      id: record.id,
      session_id: record.session_id,
      role: record.role,
      content: record.content,
      model: record.model,
      metadata: record.metadata,
      created: new Date(record.created),
      _byemap: record._byemap
    };
  }

  /**
   * 获取所有会话列表
   */
  async getAllSessions(): Promise<Session[]> {
    try {
      const records = await pb.collection('ai_sessions_byemap').getFullList({
        sort: '-created',
      });

      return records.map(record => this.mapSession(record));
    } catch (error) {
      console.error('获取会话列表失败:', error);
      return [];
    }
  }

  /**
   * 删除会话及其所有消息
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      // 删除所有相关消息
      const conversations = await pb.collection('ai_conversations_byemap').getFullList({
        filter: `session_id = "${sessionId}"`
      });

      for (const conv of conversations) {
        await pb.collection('ai_conversations_byemap').delete(conv.id);
      }

      // 删除会话
      await pb.collection('ai_sessions_byemap').delete(sessionId);

      // 如果删除的是当前会话，清除当前会话ID
      if (this.currentSessionId === sessionId) {
        this.currentSessionId = null;
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      throw new Error('无法删除会话');
    }
  }
}

export const aiChatService = new AIChatService();
export default aiChatService;
