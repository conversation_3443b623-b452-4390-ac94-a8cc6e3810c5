<template>
  <div :class="containerClasses">
    <img 
      v-if="src && !showPlaceholder" 
      :src="src" 
      :alt="alt || name" 
      :class="imageClasses"
      @error="onImageError"
      @load="onImageLoad"
    />
    <div 
      v-else-if="showPlaceholder || (!src && name)"
      :class="placeholderClasses"
    >
      {{ placeholderText }}
    </div>
    <slot v-else></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
export type IconVariant = 'default' | 'facility' | 'shop' | 'transport' | 'office'

@Component
export default class BaseIcon extends Vue {
  @Prop()
  src?: string
  
  @Prop()
  name?: string
  
  @Prop()
  alt?: string
  
  @Prop({ default: 'md' })
  size!: IconSize
  
  @Prop({ default: 'default' })
  variant!: IconVariant
  
  @Prop({ default: false })
  forceShowPlaceholder!: boolean

  private imageError = false
  private imageLoaded = false

  get showPlaceholder(): boolean {
    return this.forceShowPlaceholder || this.imageError || (!this.src && !!this.name)
  }

  get placeholderText(): string {
    if (this.name) {
      return this.name.charAt(0).toUpperCase()
    }
    return '?'
  }

  get containerClasses(): string[] {
    const baseClasses = ['icon-container']
    
    const sizeClasses = {
      xs: 'icon-xs',
      sm: 'icon-sm', 
      md: 'icon-md',
      lg: 'icon-lg',
      xl: 'icon-xl',
      '2xl': 'icon-2xl'
    }
    
    const variantClasses = {
      default: 'icon-default',
      facility: 'icon-facility',
      shop: 'icon-shop',
      transport: 'icon-transport',
      office: 'icon-office'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant]
    ]
  }

  get imageClasses(): string[] {
    return ['icon-image']
  }

  get placeholderClasses(): string[] {
    const baseClasses = ['icon-placeholder']
    
    const sizeClasses = {
      xs: 'placeholder-xs',
      sm: 'placeholder-sm',
      md: 'placeholder-md', 
      lg: 'placeholder-lg',
      xl: 'placeholder-xl',
      '2xl': 'placeholder-2xl'
    }
    
    return [
      ...baseClasses,
      sizeClasses[this.size]
    ]
  }

  onImageError() {
    this.imageError = true
    this.$emit('error')
  }

  onImageLoad() {
    this.imageError = false
    this.imageLoaded = true
    this.$emit('load')
  }
}
</script>

<style scoped>
/* 图标容器基础样式 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 尺寸变体 */
.icon-xs {
  width: 24px;
  height: 24px;
}

.icon-sm {
  width: 48px;
  height: 48px;
}

.icon-md {
  width: 80px;
  height: 80px;
}

.icon-lg {
  width: 120px;
  height: 120px;
}

.icon-xl {
  width: 160px;
  height: 160px;
}

.icon-2xl {
  width: 200px;
  height: 200px;
}

/* 特定变体尺寸覆盖 */
.icon-facility {
  width: 91px;
  height: 131px;
}

.icon-shop {
  width: 320px;
  height: 206px;
}

.icon-office {
  width: 190px;
  height: 156px;
}

/* 图片样式 */
.icon-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 占位符基础样式 */
.icon-placeholder {
  background: var(--glass-bg-medium);
  border-radius: var(--radius-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  color: var(--color-white);
  font-family: var(--font-family-primary);
}

/* 占位符尺寸 */
.placeholder-xs {
  width: 20px;
  height: 20px;
  font-size: 12px;
  border-radius: 4px;
}

.placeholder-sm {
  width: 40px;
  height: 40px;
  font-size: 16px;
  border-radius: 6px;
}

.placeholder-md {
  width: 60px;
  height: 60px;
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
}

.placeholder-lg {
  width: 90px;
  height: 90px;
  font-size: 32px;
  border-radius: var(--radius-md);
}

.placeholder-xl {
  width: 120px;
  height: 120px;
  font-size: var(--font-size-md);
  border-radius: var(--radius-md);
}

.placeholder-2xl {
  width: 160px;
  height: 160px;
  font-size: var(--font-size-xl);
  border-radius: var(--radius-md);
}

/* 变体特定样式 */
.icon-facility .placeholder-md {
  width: 60px;
  height: 60px;
  font-size: var(--font-size-xs);
}

.icon-shop .placeholder-2xl {
  width: 160px;
  height: 160px;
  font-size: var(--font-size-xl);
}

.icon-transport .placeholder-md {
  width: 60px;
  height: 60px;
  font-size: 28px;
  border-radius: var(--radius-sm);
}
</style>