"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[574],{6550:(i,a,e)=>{e.r(a),e.d(a,{default:()=>F});var t=function(){var i=this,a=i._self._c;i._self._setupProxy;return a("div",{staticClass:"facility-container responsive-page-container",style:i.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--facility"},[a("h1",{staticClass:"app-title"},[i._v(i._s(i.$t("pageTitle.facility")))])]),a("div",{staticClass:"scrollable-content-area",style:i.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--facility"},[a("div",{staticClass:"map-container"},[a("img",{staticClass:"map-image",attrs:{src:"/img/facilities/facility-map.png",alt:"Facility Map"},on:{error:i.onImageError}})]),a("div",{staticClass:"facilities-grid"},i._l(i.facilities,function(e){return a("FacilityCard",{key:e.id,attrs:{"facility-id":e.id,"facility-name":e.name,"icon-src":e.icon},on:{"facility-clicked":i.onFacilityClick}})}),1)])]),a("BottomBar",{on:{"home-clicked":i.onHomeClick,"language-changed":i.onLanguageChange,"ai-clicked":i.onAIClick}}),a("BottomMarquee")],1)},c=[],n=e(1635),l=e(9603),s=e(3452),o=e(3205),r=e(4184),m=function(){var i=this,a=i._self._c;i._self._setupProxy;return a("BaseCard",{attrs:{title:i.facilityName,"icon-src":i.iconSrc,size:"medium",variant:"facility",layout:"vertical"},on:{click:function(a){return i.$emit("click")}}})},g=[],d=e(9099);let f=class extends l.lD{facilityName;iconSrc};(0,n.Cg)([(0,l.kv)({required:!0})],f.prototype,"facilityName",void 0),(0,n.Cg)([(0,l.kv)()],f.prototype,"iconSrc",void 0),f=(0,n.Cg)([(0,l.uA)({components:{BaseCard:d.A}})],f);const p=f,u=p;var y=e(1656),C=(0,y.A)(u,m,g,!1,null,null,null);const v=C.exports;var k=e(256),A=e(5185),B=e(7959);let _=class extends((0,l.Xe)(A.A,B.A)){getBackgroundColor(){return"#016513"}facilities=[{id:"men",name:"Men",icon:"/img/facilities/men.png"},{id:"women",name:"Women",icon:"/img/facilities/women.png"},{id:"baby",name:"Baby",icon:"/img/facilities/baby.png"},{id:"services",name:"Services",icon:"/img/facilities/services.png"},{id:"lift",name:"Lift",icon:"/img/facilities/lift.png"},{id:"escalator",name:"Escalator",icon:"/img/facilities/escalator.png"},{id:"accessibly",name:"Accessibly",icon:"/img/facilities/accessibly.png"},{id:"locker",name:"Locker",icon:"/img/facilities/locker.png"}];extraFacilities=[];onFacilityClick(i){console.log("点击设施:",i)}onImageError(i){const a=i.target;a&&(a.src="/img/placeholder-transport.png")}};_=(0,n.Cg)([(0,l.uA)({components:{TopBar:s.A,BottomBar:o.A,BottomMarquee:r.A,FacilityCard:v,BackgroundImage:k.A}})],_);const b=_,h=b;var x=(0,y.A)(h,t,c,!1,null,"42649c94",null);const F=x.exports}}]);