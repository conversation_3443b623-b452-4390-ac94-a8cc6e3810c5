package com.example.emapai;

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.view.View;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.graphics.Color;
import android.view.Window;
import android.view.WindowManager;
import android.os.Build;

public class WebViewActivity extends Activity {
    private WebView webView;
    private ProgressBar progressBar;
    private TextView titleTextView;
    private ImageButton backButton;
    private ImageButton closeButton;
    private RelativeLayout toolbar;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置全屏
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.BLACK);
        }
        
        // 创建布局
        RelativeLayout mainLayout = new RelativeLayout(this);
        mainLayout.setLayoutParams(new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        ));
        mainLayout.setBackgroundColor(Color.WHITE);
        
        // 创建工具栏
        toolbar = new RelativeLayout(this);
        RelativeLayout.LayoutParams toolbarParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            dpToPx(56)
        );
        toolbar.setBackgroundColor(Color.parseColor("#2C3E50"));
        toolbar.setElevation(dpToPx(4));
        toolbar.setId(View.generateViewId());
        
        // 返回按钮
        backButton = new ImageButton(this);
        RelativeLayout.LayoutParams backParams = new RelativeLayout.LayoutParams(
            dpToPx(48),
            dpToPx(48)
        );
        backParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        backParams.addRule(RelativeLayout.CENTER_VERTICAL);
        backParams.setMargins(dpToPx(8), 0, 0, 0);
        backButton.setBackgroundColor(Color.TRANSPARENT);
        backButton.setImageResource(R.drawable.ic_back_arrow);
        backButton.setScaleType(ImageButton.ScaleType.CENTER);
        backButton.setPadding(dpToPx(12), dpToPx(12), dpToPx(12), dpToPx(12));
        backButton.setOnClickListener(v -> {
            if (webView.canGoBack()) {
                webView.goBack();
            }
        });
        
        // 关闭按钮
        closeButton = new ImageButton(this);
        RelativeLayout.LayoutParams closeParams = new RelativeLayout.LayoutParams(
            dpToPx(48),
            dpToPx(48)
        );
        closeParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        closeParams.addRule(RelativeLayout.CENTER_VERTICAL);
        closeParams.setMargins(0, 0, dpToPx(8), 0);
        closeButton.setBackgroundColor(Color.TRANSPARENT);
        closeButton.setImageResource(R.drawable.ic_close);
        closeButton.setScaleType(ImageButton.ScaleType.CENTER);
        closeButton.setPadding(dpToPx(12), dpToPx(12), dpToPx(12), dpToPx(12));
        closeButton.setOnClickListener(v -> finish());
        
        // 标题
        titleTextView = new TextView(this);
        RelativeLayout.LayoutParams titleParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        titleParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        titleTextView.setTextColor(Color.WHITE);
        titleTextView.setTextSize(18);
        titleTextView.setText("香港国际机场");
        
        toolbar.addView(backButton, backParams);
        toolbar.addView(titleTextView, titleParams);
        toolbar.addView(closeButton, closeParams);
        
        // 创建进度条
        progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
        RelativeLayout.LayoutParams progressParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            dpToPx(3)
        );
        progressParams.addRule(RelativeLayout.BELOW, toolbar.getId());
        progressBar.setMax(100);
        progressBar.setProgressDrawable(getResources().getDrawable(android.R.drawable.progress_horizontal));
        progressBar.setId(View.generateViewId());
        
        // 创建WebView
        webView = new WebView(this);
        RelativeLayout.LayoutParams webViewParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        );
        webViewParams.addRule(RelativeLayout.BELOW, progressBar.getId());
        
        // 配置WebView
        setupWebView();
        
        // 添加视图
        mainLayout.addView(toolbar, toolbarParams);
        mainLayout.addView(progressBar, progressParams);
        mainLayout.addView(webView, webViewParams);
        
        setContentView(mainLayout);
        
        // 获取URL并加载
        String url = getIntent().getStringExtra("url");
        if (url == null) {
            url = "https://www.hongkongairport.com/en/flights/arrivals/passenger.page";
        }
        webView.loadUrl(url);
    }
    
    private void setupWebView() {
        WebSettings settings = webView.getSettings();
        
        // 启用JavaScript
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);
        
        // 缩放设置
        settings.setSupportZoom(true);
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        
        // 缓存设置
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 允许混合内容
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        
        // 设置User Agent
        String userAgent = settings.getUserAgentString();
        settings.setUserAgentString(userAgent + " EMAP-AI-WebView");
        
        // WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                progressBar.setVisibility(View.VISIBLE);
                progressBar.setProgress(0);
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                progressBar.setVisibility(View.GONE);
                titleTextView.setText(view.getTitle());
                backButton.setEnabled(view.canGoBack());
            }
            
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
        
        // WebChromeClient
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBar.setProgress(newProgress);
            }
            
            @Override
            public void onReceivedTitle(WebView view, String title) {
                titleTextView.setText(title);
            }
        });
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
    
    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
}
