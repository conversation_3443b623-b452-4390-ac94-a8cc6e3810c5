import { Vue, Component } from 'vue-property-decorator'

@Component
export default class ResponsiveMixin extends Vue {
  private scaleRatio = 1

  mounted() {
    this.calculateScale()
    window.addEventListener('resize', this.calculateScale)
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.calculateScale)
  }

  calculateScale() {
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    const baseWidth = 2160 // 4K基准宽度
    
    // 宽度为唯一缩放标准
    this.scaleRatio = screenWidth / baseWidth
    
    console.log(`[ResponsiveMixin] 缩放计算: 屏幕尺寸${screenWidth}×${screenHeight}px, 缩放比例${this.scaleRatio}`)
  }

  get containerStyle() {
    // 计算实际需要的高度来补偿缩放影响
    const actualHeight = (100 / this.scaleRatio) + 'vh'
    
    return {
      transform: `scale(${this.scaleRatio})`,
      transformOrigin: 'top left',
      width: '2160px',
      height: actualHeight, // 补偿缩放后的高度损失
      position: 'relative',
      overflow: 'hidden',
      fontFamily: 'Inter, sans-serif',
      backgroundColor: this.getBackgroundColor()
    }
  }

  // 子类可以重写这个方法来提供不同的背景色
  getBackgroundColor(): string {
    return '#016513' // 默认背景色
  }

  get contentAreaStyle() {
    // 计算内容区域的动态高度
    const topBarHeight = 200 // TopBar高度
    const titleHeight = 155 // 标题区域高度  
    const titleMargin = 340 - topBarHeight // 标题距离TopBar的间距
    const bottomBarHeight = 523 // BottomBar总占用高度
    const contentStartY = topBarHeight + titleMargin + titleHeight + 140 // 内容开始位置
    const bottomGap = 60 // 与BottomBar的间距
    
    return {
      position: 'absolute',
      top: `${contentStartY}px`,
      left: '0',
      right: '0', 
      bottom: `${bottomBarHeight + bottomGap}px`,
      overflowY: 'auto',
      overflowX: 'hidden',
      zIndex: 10
    }
  }

  // 通用的导航方法
  onHomeClick() {
    if (this.$route.path !== '/') {
      this.$router.push('/')
    }
  }

  onLanguageChange(lang: string) {
    console.log('语言切换到:', lang)
  }

  onAIClick() {
    console.log('打开AI助手')
    this.$router.push('/AIChat')
  }
}