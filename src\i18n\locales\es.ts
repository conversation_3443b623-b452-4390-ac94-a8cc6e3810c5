// Spanish language pack
export default {
  // Común
  common: {
    loading: 'Cargando...',
    error: 'Error',
    success: '<PERSON>xi<PERSON>',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    back: 'Atrás',
    next: '<PERSON>gu<PERSON><PERSON>',
    previous: 'Anterior',
    close: 'Cerrar',
    save: '<PERSON>ar',
    delete: 'Eliminar',
    edit: 'Editar',
    search: 'Buscar',
    filter: 'Filtrar',
    all: 'Todo',
    none: 'Ni<PERSON><PERSON>',
    yes: 'Sí',
    no: 'No'
  },

  // Navegación
  nav: {
    home: 'Inicio',
    shop: 'Tienda',
    food: 'Comida',
    office: 'Oficina',
    facility: 'Instalación',
    poster: 'Cartel',
    transport: 'Transporte',
    worldTime: 'Hora Mundial',
    video: 'Vídeo',
    about: 'Acerca de',
    web: 'Web',
    aiSearch: 'Búsqueda IA'
  },

  // Títulos de página
  pageTitle: {
    eMap: 'eMap',
    shop: 'Tienda',
    food: '<PERSON>mi<PERSON>',
    office: 'Oficina',
    facility: 'Instalación',
    poster: 'Cartel',
    transport: 'Transporte',
    worldTime: 'Hora Mundial',
    video: 'Video Deportivo',
    about: 'Acerca de',
    web: 'Navegador Web',
    airQuality: 'Índice de Calidad del Aire',
    aiSearch: 'Búsqueda',
    aiChat: 'Asistente IA',
    shopDetail: 'Tienda'
  },

  // Relacionado con tiendas
  shop: {
    name: 'Nombre de la Tienda',
    location: 'Ubicación',
    hours: 'Horario de Apertura',
    description: 'Descripción',
    searchPlaceholder: 'Buscar nombre de tienda',
    noResults: 'No se encontraron tiendas',
    tryOtherKeywords: 'Pruebe con otras palabras clave',
    startSearch: 'Iniciar Búsqueda',
    searchPrompt: 'Ingrese el nombre de la tienda en el cuadro de búsqueda para encontrar la tienda deseada'
  },

  // Relacionado con oficinas
  office: {
    companyName: 'Nombre de la Empresa',
    roomNumber: 'Número de Habitación',
    floor: 'Piso',
    byFloor: 'por Piso',
    byName: 'por Nombre',
    filterBy: 'Filtrar por'
  },

  // Relacionado con instalaciones
  facility: {
    men: 'Hombres',
    women: 'Mujeres',
    baby: 'Bebé',
    services: 'Servicios',
    lift: 'Ascensor',
    escalator: 'Escalera Mecánica',
    accessibly: 'Accesibilidad',
    locker: 'Casillero'
  },

  // Relacionado con carteles
  poster: {
    title: 'Título',
    description: 'Descripción',
    previous: 'Anterior',
    next: 'Siguiente',
    pause: 'Pausar',
    play: 'Reproducir',
    autoplay: 'Reproducción Automática',
    defaultTitle: 'Cartel',
    defaultDescription: 'Ver contenido emocionante'
  },

  // Relacionado con transporte
  transport: {
    bus: 'Autobús',
    mtr: 'Metro',
    lightRail: 'Tren Ligero',
    miniBus: 'Minibús',
    nearby: 'Transporte Cercano',
    schedule: 'Horario',
    route: 'Ruta'
  },

  // Relacionado con comida
  food: {
    title: 'Servicios de Restauración',
    comingSoon: 'Próximamente'
  },

  // Relacionado con página Acerca de
  about: {
    title: 'Acerca de eMap AI',
    techStack: 'Stack Tecnológico',
    features: 'Características',
    version: 'Información de Versión',
    team: 'Información del Equipo'
  },

  // Relacionado con hora mundial
  worldTime: {
    title: 'Hora Mundial',
    realtimeTitle: 'Hora Mundial en Tiempo Real',
    hongkong: 'Hong Kong',
    tokyo: 'Tokio',
    newyork: 'Nueva York',
    london: 'Londres',
    paris: 'París',
    sydney: 'Sídney',
    beijing: 'Pekín',
    seoul: 'Seúl',
    dubai: 'Dubái',
    currentTime: 'Hora Actual',
    timezone: 'Zona Horaria'
  },

  // Relacionado con vídeos
  video: {
    title: 'Título',
    description: 'Descripción',
    duration: 'Duración',
    category: 'Categoría',
    mute: 'Silenciar',
    unmute: 'Activar Sonido',
    fullscreen: 'Pantalla Completa',
    mutedNotice: 'Los videos se reproducen silenciados por defecto'
  },

  // Relacionado con el clima
  weather: {
    temperature: 'Temperatura',
    feelsLike: 'Sensación Térmica',
    humidity: 'Humedad',
    sunny: 'Soleado',
    cloudy: 'Nublado',
    rainy: 'Lluvioso',
    snowy: 'Nevado',
    stormy: 'Tormentoso'
  },

  // Relacionado con idiomas
  language: {
    current: 'Idioma Actual',
    switch: 'Cambiar Idioma',
    traditionalChinese: 'Chino Tradicional',
    english: 'Inglés',
    spanish: 'Español',
    short: 'ES'
  },

  // Contenido detallado de la página Acerca de
  aboutDetail: {
    techStack: {
      vue: 'Vue 2 - Framework JavaScript Progresivo',
      typescript: 'TypeScript - Superconjunto de JavaScript con sistema de tipos',
      tailwind: 'TailwindCSS - Framework CSS utility-first',
      capacitor: 'Capacitor - Herramienta de construcción de aplicaciones nativas multiplataforma'
    },
    features: {
      smartNavigation: 'Sistema de Navegación Inteligente',
      realtimeLocation: 'Servicio de Ubicación en Tiempo Real',
      multiLanguage: 'Soporte Multiidioma',
      crossPlatform: 'Compatibilidad Multiplataforma'
    },
    version: {
      current: 'Versión Actual: v2.1.0',
      releaseDate: 'Fecha de Lanzamiento: 2024',
      updateFrequency: 'Frecuencia de Actualización: Actualizaciones Mensuales',
      supportedPlatforms: 'Plataformas Compatibles: iOS, Android, Web'
    },
    team: {
      frontend: 'Desarrollo Frontend: Vue.js + TypeScript',
      mobile: 'Desarrollo Móvil: Capacitor Multiplataforma',
      design: 'Diseño UI/UX: Estilo Moderno de Cristal',
      data: 'Soporte de Datos: Sincronización en Tiempo Real'
    }
  },

  // Nombres de ciudades
  cities: {
    hongkong: 'Hong Kong',
    tokyo: 'Tokio',
    newyork: 'Nueva York',
    london: 'Londres',
    paris: 'París',
    sydney: 'Sídney',
    beijing: 'Pekín',
    seoul: 'Seúl',
    dubai: 'Dubái',
    losangeles: 'Los Ángeles'
  },

  // Contenido de carteles
  posterContent: {
    splus: {
      title: 'Miembros S+ REWARDS',
      description: 'Agregue más a la vida, regístrese como miembro S+ REWARDS ahora para sorpresas y recompensas continuas'
    },
    ikea: {
      title: 'Ideas para el Hogar IKEA',
      description: 'Actividades promocionales de HomeSquare IKEA, descuentos en muebles que no se pueden perder'
    },
    more: {
      title: 'Más Información Promocional',
      description: 'Ver más promociones y detalles de eventos del centro comercial'
    }
  },

  // Relacionado con vídeos
  videoContent: {
    sound: {
      on: 'Activar Sonido',
      off: 'Desactivar Sonido',
      notice: 'Haga clic en el botón de abajo o en el ícono de sonido del reproductor de video para habilitar el volumen'
    },
    videos: {
      basketball: {
        title: 'Evento Deportivo del Hotel - Mejores Momentos de Baloncesto',
        description: 'Revisión de momentos emocionantes de la competencia de baloncesto de deportes del hotel',
        category: 'Baloncesto'
      },
      swimming: {
        title: 'Mejores Momentos de Competencia de Natación',
        description: 'Competencia intensa y actuación emocionante de partidos de natación',
        category: 'Natación'
      },
      tennis: {
        title: 'Final del Campeonato de Tenis',
        description: 'Enfrentamiento emocionante de la final del campeonato de tenis',
        category: 'Tenis'
      }
    }
  },

  // AI búsqueda
  aiSearch: {
    placeholder: 'Buscar tiendas...'
  },

  // AI chat
  aiChat: {
    welcomeTitle: '¡Hola, soy Winnie!',
    welcomeMessage: 'Soy el asistente de servicio al cliente inteligente de este centro comercial. ¿Cómo puedo ayudarte?',
    inputPlaceholder: 'Por favor ingrese su pregunta...',
    listening: 'Escuchando...',
    sendMessage: 'Enviar',
    voiceInput: 'Entrada de voz',
    voiceMessage: '[Mensaje de voz]',
    typing: 'Winnie está escribiendo...',
    error: 'Lo siento, no puedo responder a tu mensaje en este momento. Por favor, inténtalo de nuevo más tarde.',
    newChat: 'Nuevo chat',
    clearChat: 'Borrar chat',
    recordingGuide: 'Grabando... Por favor habla...'
  }
}
