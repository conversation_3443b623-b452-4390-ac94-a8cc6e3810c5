"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[901],{5097:(t,a,s)=>{s.r(a),s.d(a,{default:()=>S});var e=function(){var t=this,a=t._self._c;t._self._setupProxy;return a("div",{staticClass:"transport-page responsive-page-container",style:t.containerStyle},[a("BackgroundImage"),a("TopBar"),a("div",{staticClass:"page-title-fixed page-title-fixed--transport"},[a("h1",{staticClass:"app-title"},[t._v(t._s(t.$t("pageTitle.transport")))])]),a("div",{staticClass:"scrollable-content-area",style:t.contentAreaStyle},[a("div",{staticClass:"main-content-container main-content-container--standard"},[a("div",{staticClass:"transport-image-container"},[a("img",{staticClass:"transport-image",attrs:{src:"/img/transports/img.png",alt:"Transport Information"}})]),a("div",{staticClass:"transport-buttons"},t._l(t.transportOptions,function(s){return a("TransportButton",{key:s.id,attrs:{"transport-id":s.id,"transport-name":s.name,"icon-src":s.icon,"is-selected":t.selectedTransport===s.id},on:{"transport-selected":t.selectTransport}})}),1)])]),a("BottomBar",{on:{"home-clicked":t.onHomeClick,"language-changed":t.onLanguageChange,"ai-clicked":t.onAIClick}}),a("BottomMarquee")],1)},r=[],n=s(1635),i=s(9603),o=s(3452),c=s(3205),p=s(4184),l=function(){var t=this,a=t._self._c;t._self._setupProxy;return a("BaseCard",{class:{"state-primary":t.isActive&&!t.isSpecial,"state-danger":t.isSpecial&&t.isActive,"transport-special":t.isSpecial},attrs:{title:t.transportName,"icon-src":t.iconSrc,size:"small",variant:"transport",layout:"vertical"},on:{click:function(a){return t.$emit("click")}}})},d=[],m=s(9099);let g=class extends i.lD{transportName;iconSrc;isActive;isSpecial};(0,n.Cg)([(0,i.kv)({required:!0})],g.prototype,"transportName",void 0),(0,n.Cg)([(0,i.kv)()],g.prototype,"iconSrc",void 0),(0,n.Cg)([(0,i.kv)({default:!1})],g.prototype,"isActive",void 0),(0,n.Cg)([(0,i.kv)({default:!1})],g.prototype,"isSpecial",void 0),g=(0,n.Cg)([(0,i.uA)({components:{BaseCard:m.A}})],g);const u=g,v=u;var C=s(1656),k=(0,C.A)(v,l,d,!1,null,"a7dcbc8c",null);const y=k.exports;var f=s(256),A=s(5185),B=s(7959);let h=class extends((0,i.Xe)(A.A,B.A)){getBackgroundColor(){return"#1E90FF"}selectedTransport="mtr";transportOptions=[{id:"mtr",name:"MTR",icon:"/img/transports/mtr.svg",type:"transport"},{id:"light-rail",name:"Light Rail",icon:"/img/transports/light-rail.svg",type:"transport"},{id:"bus",name:"Bus",icon:"/img/transports/bus.svg",type:"transport"},{id:"mini-bus",name:"Mini Bus",icon:"/img/transports/mini-bus.svg",type:"transport"},{id:"map",name:"Map",icon:"/img/transports/map.svg",type:"map"}];selectTransport(t){this.selectedTransport=t,console.log("Selected transport:",t)}};h=(0,n.Cg)([(0,i.uA)({components:{TopBar:o.A,BottomBar:c.A,BottomMarquee:p.A,TransportButton:y,BackgroundImage:f.A}})],h);const _=h,T=_;var b=(0,C.A)(T,e,r,!1,null,"86bafdb4",null);const S=b.exports}}]);