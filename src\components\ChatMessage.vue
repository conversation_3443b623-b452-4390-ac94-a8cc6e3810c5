<template>
  <div :class="['chat-message', `chat-message--${message.role}`]">
    <div class="message-avatar">
      <span v-if="message.role === 'user'" class="avatar-icon">👤</span>
      <span v-else class="avatar-icon">🤖</span>
    </div>
    <div class="message-content">
      <div class="message-bubble">
        <p class="message-text">{{ message.content }}</p>
      </div>
      <div class="message-time">
        {{ formatTime(message.timestamp) }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

@Component
export default class ChatMessageComponent extends Vue {
  @Prop({ required: true }) message!: ChatMessage

  formatTime(timestamp: Date): string {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    
    return `${hours}:${minutes}`
  }
}
</script>

<style scoped>
.chat-message {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 用户消息右对齐 */
.chat-message--user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-icon {
  font-size: 40px;
}

.message-content {
  flex: 1;
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-message--user .message-content {
  align-items: flex-end;
}

.chat-message--assistant .message-content {
  align-items: flex-start;
}

.message-bubble {
  padding: 24px 32px;
  border-radius: 24px;
  backdrop-filter: blur(20px);
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 用户消息气泡 */
.chat-message--user .message-bubble {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

/* AI 消息气泡 */
.chat-message--assistant .message-bubble {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 1);
  color: #333;
}

.message-text {
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-time {
  font-family: 'Inter', sans-serif;
  font-size: 20px;
  opacity: 0.6;
  padding: 0 8px;
}

.chat-message--user .message-time {
  color: rgba(255, 255, 255, 0.6);
}

.chat-message--assistant .message-time {
  color: rgba(0, 0, 0, 0.4);
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .message-text {
    font-size: 28px;
  }
  
  .message-avatar {
    width: 60px;
    height: 60px;
  }
  
  .avatar-icon {
    font-size: 30px;
  }
}
</style>
