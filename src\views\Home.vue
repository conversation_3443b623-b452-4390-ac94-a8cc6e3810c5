<template>
  <div class="home-container responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- eMap主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--home">
      <h1 class="app-title">{{ $t('pageTitle.eMap') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--home">
        <!-- 功能按钮网格 -->
        <div class="function-grid">
          <!-- 第一行 -->
          <GlassButton 
            size="medium"
            variant="highlight"
            @click="navigateTo('shop')"
          >
            {{ $t('nav.shop') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="normal"
            @click="navigateTo('food')"
          >
            {{ $t('nav.food') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="highlight"
            @click="navigateTo('office')"
          >
            {{ $t('nav.office') }}
          </GlassButton>

          <!-- 第二行 -->
          <GlassButton 
            size="medium"
            variant="normal"
            @click="navigateTo('facility')"
          >
            {{ $t('nav.facility') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="normal"
            @click="navigateTo('poster')"
          >
            {{ $t('nav.poster') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="normal"
            @click="navigateTo('transport')"
          >
            {{ $t('nav.transport') }}
          </GlassButton>

          <!-- 第三行 -->
          <GlassButton 
            size="medium"
            variant="normal"
            @click="navigateTo('web2')"
          >
            {{ $t('nav.web') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="highlight"
            @click="navigateTo('video')"
          >
            {{ $t('nav.video') }}
          </GlassButton>
          
          <GlassButton 
            size="medium"
            variant="highlight"
            @click="navigateTo('worldtime')"
          >
            <span class="text-center">
              {{ $t('nav.worldTime')}}
            </span>
          </GlassButton>
         
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import GlassButton from '@/components/GlassButton.vue'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'
import { Capacitor } from '@capacitor/core'

@Component({
  components: {
    GlassButton,
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class Home extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#016513'
  }

  async navigateTo(page: string) {
    console.log('導航到:', page)
    
    if (page === 'web') {
      if (Capacitor.isNativePlatform()) {
        try {
          console.log('=== 使用簡化WebView方案 ===')
          console.log('平台:', Capacitor.getPlatform())
          
          // 直接使用window.location触发自定义URL scheme
          console.log('嘗試觸發自定義URL scheme...')
          window.location.href = 'webview://hongkongairport'
          console.log('URL scheme已觸發')
        } catch (error) {
          console.error('Intent調用失敗，嘗試其他方案:', error)
          
          // 備選方案：使用自定義方案
          try {
            // 創建一個自定義URL scheme來觸發原生WebView
            window.location.href = 'webview://open?url=' + encodeURIComponent('https://www.hongkongairport.com/en/flights/arrivals/passenger.page')
          } catch (urlError) {
            console.error('URL scheme也失敗:', urlError)
            alert('無法打開WebView')
          }
        }
      } else {
        // Web平台直接打開外部鏈接
        window.open('https://www.hongkongairport.com/en/flights/arrivals/passenger.page', '_blank')
      }
    } else {
      // 其他页面正常导航
      this.$router.push(`/${page}`)
    }
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(3, 550px);
  grid-template-rows: repeat(3, 550px); /* 改为4行以容纳更多按钮 */
  gap: 104px 86px;
  width: 1822px; /* 550*3 + 86*2 = 1822px */
  margin: 0 auto; /* 居中显示 */
}

/* 文字换行样式 */
.text-center {
  text-align: center;
  line-height: 1.1;
}

/* 主标题样式已在 design-system.css 中定义 */
</style> 