"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[325],{150:(e,t,o)=>{o.d(t,{A:()=>u});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{class:e.cardClasses,on:{click:function(t){return e.$emit("click")}}},[e.showIcon?t("div",{class:e.iconContainerClasses},[e.iconSrc?t("img",{class:e.iconImageClasses,attrs:{src:e.iconSrc,alt:e.title}}):e.showPlaceholder?t("div",{class:e.placeholderClasses},[e._v(" "+e._s(e.placeholderText)+" ")]):e._e()]):e._e(),e.showContent?t("div",{class:e.contentClasses},[e._t("default",function(){return[e.title?t("div",{class:e.titleClasses},[e._v(" "+e._s(e.title)+" ")]):e._e(),e.subtitle?t("div",{class:e.subtitleClasses},[e._v(" "+e._s(e.subtitle)+" ")]):e._e()]})],2):e._e(),e._t("custom")],2)},r=[],l=o(635),s=o(233);let i=class extends s.lD{title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const e=["card-base","text-primary"],t={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},o={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},a={vertical:"card-vertical",horizontal:"card-horizontal"};return[...e,t[this.size],o[this.variant],a[this.layout]]}get iconContainerClasses(){const e=["icon-container"],t={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...e,t[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const e=["icon-placeholder"],t={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...e,t[this.size]]}get contentClasses(){const e=["card-content"],t={vertical:"content-vertical",horizontal:"content-horizontal"};return[...e,t[this.layout]]}get titleClasses(){const e=["card-title","text-primary"],t={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},o={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...e,t[this.size],o[this.variant]]}get subtitleClasses(){const e=["card-subtitle","text-primary"],t={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...e,t[this.size]]}};(0,l.Cg)([(0,s.kv)({required:!0})],i.prototype,"title",void 0),(0,l.Cg)([(0,s.kv)()],i.prototype,"subtitle",void 0),(0,l.Cg)([(0,s.kv)()],i.prototype,"iconSrc",void 0),(0,l.Cg)([(0,s.kv)({default:"medium"})],i.prototype,"size",void 0),(0,l.Cg)([(0,s.kv)({default:"facility"})],i.prototype,"variant",void 0),(0,l.Cg)([(0,s.kv)({default:"vertical"})],i.prototype,"layout",void 0),(0,l.Cg)([(0,s.kv)({default:!0})],i.prototype,"showIcon",void 0),(0,l.Cg)([(0,s.kv)({default:!0})],i.prototype,"showContent",void 0),(0,l.Cg)([(0,s.kv)({default:!0})],i.prototype,"showPlaceholder",void 0),i=(0,l.Cg)([s.uA],i);const n=i,c=n;var d=o(656),m=(0,d.A)(c,a,r,!1,null,"022da4db",null);const u=m.exports},325:(e,t,o)=>{o.r(t),o.d(t,{default:()=>B});var a=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"office-container responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed page-title-fixed--office"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.office")))])]),t("div",{staticClass:"filter-buttons-fixed"},[t("button",{staticClass:"filter-btn by-floor",class:{active:"floor"===e.sortBy},on:{click:function(t){e.sortBy="floor"}}},[e._v(" "+e._s(e.$t("office.byFloor"))+" ")]),t("button",{staticClass:"filter-btn by-name",class:{active:"name"===e.sortBy},on:{click:function(t){e.sortBy="name"}}},[e._v(" "+e._s(e.$t("office.byName"))+" ")])]),t("div",{staticClass:"scrollable-content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content-container main-content-container--office"},[t("div",{staticClass:"office-list"},e._l(e.sortedOffices,function(e){return t("OfficeCard",{key:e.id,staticClass:"office-item",attrs:{"company-name":e.name,"room-number":e.room,"logo-src":e.logo}})}),1)])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}})],1)},r=[],l=o(635),s=o(233),i=o(14),n=o(958),c=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("BaseCard",{attrs:{title:e.companyName,subtitle:e.roomNumber,"icon-src":e.logoSrc,size:"extra-large",variant:"office",layout:"horizontal"}})},d=[],m=o(150);let u=class extends s.lD{companyName;roomNumber;logoSrc};(0,l.Cg)([(0,s.kv)({required:!0})],u.prototype,"companyName",void 0),(0,l.Cg)([(0,s.kv)({required:!0})],u.prototype,"roomNumber",void 0),(0,l.Cg)([(0,s.kv)()],u.prototype,"logoSrc",void 0),u=(0,l.Cg)([(0,s.uA)({components:{BaseCard:m.A}})],u);const f=u,g=f;var p=o(656),v=(0,p.A)(g,c,d,!1,null,null,null);const h=v.exports;var C=o(256),y=o(185),_=o(959);let b=class extends((0,s.Xe)(y.A,_.A)){getBackgroundColor(){return"#4A4A4A"}sortBy="name";offices=[{id:1,name:"Tesla Inc Kong Kong Branch",room:"104",floor:1},{id:2,name:"Merrill Lynch",room:"812",floor:8},{id:3,name:"Apple Inc Hong Kong",room:"903",floor:9},{id:4,name:"Xiaomi",room:"222",floor:2},{id:5,name:"Microsoft Hong Kong",room:"888",floor:8},{id:6,name:"Amazon",room:"232",floor:2},{id:7,name:"Facebook",room:"721",floor:7}];get sortedOffices(){const e=[...this.offices];return"name"===this.sortBy?e.sort((e,t)=>e.name.localeCompare(t.name)):e.sort((e,t)=>e.floor-t.floor)}};b=(0,l.Cg)([(0,s.uA)({components:{TopBar:i.A,BottomBar:n.A,OfficeCard:h,BackgroundImage:C.A}})],b);const k=b,x=k;var A=(0,p.A)(x,a,r,!1,null,"28db1e90",null);const B=A.exports}}]);