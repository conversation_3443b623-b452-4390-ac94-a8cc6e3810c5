"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[282],{8282:(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var o=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"shop-container responsive-page-container",style:e.containerStyle},[t("BackgroundImage"),t("TopBar"),t("div",{staticClass:"page-title-fixed"},[t("h1",{staticClass:"app-title"},[e._v(e._s(e.$t("pageTitle.shop")))])]),t("div",{staticClass:"content-area",style:e.contentAreaStyle},[t("div",{staticClass:"main-content"},[e.loading?t("div",{staticClass:"loading-container"},[t("div",{staticClass:"loading-spinner"}),t("div",{staticClass:"loading-text"},[e._v(e._s(e.$t("common.loading")||"加载中..."))])]):e.error?t("div",{staticClass:"error-container"},[t("div",{staticClass:"error-icon"},[e._v("⚠️")]),t("div",{staticClass:"error-text"},[e._v(e._s(e.errorMessage))]),t("button",{staticClass:"retry-button",on:{click:e.retryLoadShops}},[e._v(" "+e._s(e.$t("common.retry")||"重试")+" ")])]):t("div",{staticClass:"shop-grid"},e._l(e.shops,function(a){return t("ShopCardOptimized",{key:a.id,attrs:{"shop-name":e.getShopDisplayName(a),"logo-src":a.logo,"enable-marquee":!0,"marquee-speed":4,"marquee-delay":1.5},on:{click:function(t){return e.navigateToShop(a.id)}}})}),1)])]),t("BottomBar",{on:{"home-clicked":e.onHomeClick,"language-changed":e.onLanguageChange,"ai-clicked":e.onAIClick}}),t("BottomMarquee")],1)},s=[],r=a(1635),i=a(9603),n=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"shop-card-optimized",on:{click:e.handleClick}},[t("div",{staticClass:"icon-container"},[e.logoSrc?t("img",{staticClass:"shop-icon",attrs:{src:e.logoSrc,alt:e.shopName},on:{error:e.onImageError}}):t("div",{staticClass:"icon-placeholder"},[e._v(" "+e._s(e.placeholderText)+" ")])]),t("div",{staticClass:"title-container"},[e.enableMarquee&&e.shouldShowMarquee?t("div",{staticClass:"marquee-container"},[t("div",{staticClass:"marquee-text",style:e.marqueeStyle},[e._v(" "+e._s(e.shopName)+" ")])]):t("div",{staticClass:"static-title"},[e._v(" "+e._s(e.shopName)+" ")])])])},l=[];let c=class extends i.lD{shopName;logoSrc;enableMarquee;marqueeSpeed;marqueeDelay;shouldShowMarquee=!1;marqueeAnimationId=null;get placeholderText(){return this.shopName?.charAt(0)||"?"}get marqueeStyle(){return this.shouldShowMarquee?{animation:`marquee-scroll ${this.marqueeSpeed}s linear infinite`,animationDelay:`${this.marqueeDelay}s`}:{}}mounted(){this.enableMarquee&&this.checkMarqueeNeed()}beforeDestroy(){this.marqueeAnimationId&&cancelAnimationFrame(this.marqueeAnimationId)}checkMarqueeNeed(){this.$nextTick(()=>{const e=this.$el.querySelector(".title-container"),t=this.$el.querySelector(".static-title");e&&t&&(this.shouldShowMarquee=t.scrollWidth>e.clientWidth)})}handleClick(e){this.$emit("click",e)}onImageError(e){const t=e.target;t&&(t.style.display="none")}};(0,r.Cg)([(0,i.kv)({required:!0})],c.prototype,"shopName",void 0),(0,r.Cg)([(0,i.kv)({default:""})],c.prototype,"logoSrc",void 0),(0,r.Cg)([(0,i.kv)({default:!0})],c.prototype,"enableMarquee",void 0),(0,r.Cg)([(0,i.kv)({default:4})],c.prototype,"marqueeSpeed",void 0),(0,r.Cg)([(0,i.kv)({default:1.5})],c.prototype,"marqueeDelay",void 0),c=(0,r.Cg)([i.uA],c);const d=c,h=d;var u=a(1656),p=(0,u.A)(h,n,l,!1,null,"77cda250",null);const m=p.exports;var g=a(3452),v=a(3205),y=a(4184),C=a(256),q=a(5185),S=a(7959),_=a(8091);let k=class extends((0,i.Xe)(q.A,S.A)){getBackgroundColor(){return"#0F04A9"}shops=[];loading=!0;error=!1;errorMessage="";getShopDisplayName(e){const t=this.$i18n?.locale||"en";switch(t){case"zh-TW":case"zh-CN":return e.name_tc||e.name_zh||e.name;case"en":default:return e.name}}async mounted(){try{const{soundService:e}=await Promise.resolve().then(a.bind(a,8111));e.setEnabled(!1),console.log("Shop页面音效已禁用以提升滚动性能")}catch(e){console.warn("禁用音效失败:",e)}await this.loadShops()}beforeDestroy(){try{const{soundService:e}=a(8111);e.setEnabled(!0),console.log("离开Shop页面，音效已重新启用")}catch(e){console.warn("重新启用音效失败:",e)}}async loadShops(){this.loading=!0,this.error=!1,this.errorMessage="";try{console.log("开始加载商店数据..."),this.shops=await(0,_.aF)(),console.log(`成功加载${this.shops.length}个商店`)}catch(e){console.error("加载商店数据失败:",e),this.error=!0,this.errorMessage=e instanceof Error?`加载失败: ${e.message}`:"网络连接错误，请检查网络后重试"}finally{this.loading=!1}}async retryLoadShops(){await this.loadShops()}navigateToShop(e){console.log("导航到商店:",e),this.$router.push(`/shop/${e}`)}};k=(0,r.Cg)([(0,i.uA)({components:{ShopCardOptimized:m,TopBar:g.A,BottomBar:v.A,BottomMarquee:y.A,BackgroundImage:C.A}})],k);const f=k,A=f;var M=(0,u.A)(A,o,s,!1,null,"4437282a",null);const b=M.exports}}]);