import { WebPlugin } from '@capacitor/core';
import type { WebViewPlugin, WebViewOptions, WebViewResult } from './definitions';

export class WebViewWeb extends WebPlugin implements WebViewPlugin {
  async showEmbedded(options: WebViewOptions): Promise<WebViewResult> {
    console.warn('WebView plugin is not implemented for web. This is a native-only plugin.');
    // 在Web端显示一个iframe作为fallback
    const iframe = document.createElement('iframe');
    iframe.src = options.url;
    iframe.style.width = options.position?.width || '100%';
    iframe.style.height = options.position?.height || '100%';
    iframe.style.border = 'none';
    iframe.id = 'capacitor-webview-iframe';
    
    // 尝试添加到content区域
    const contentArea = document.querySelector('.scrollable-content-area .main-content-container');
    if (contentArea) {
      contentArea.innerHTML = '';
      contentArea.appendChild(iframe);
    }
    
    return { success: true, message: 'Running in web mode with iframe fallback' };
  }
  
  async hideEmbedded(): Promise<void> {
    const iframe = document.getElementById('capacitor-webview-iframe');
    if (iframe) {
      iframe.remove();
    }
  }
}