<template>
  <div class="food-page responsive-page-container" :style="containerStyle">
    <!-- 背景图片层 -->
    <BackgroundImage />
    
    <!-- 顶部信息栏 - 固定位置 -->
    <TopBar />
    
    <!-- Food主标题 - 固定位置 -->
    <div class="page-title-fixed page-title-fixed--food">
      <h1 class="app-title">{{ $t('pageTitle.food') }}</h1>
    </div>
    
    <!-- 动态内容区域 - 支持滚动 -->
    <div class="scrollable-content-area" :style="contentAreaStyle">
      <div class="main-content-container main-content-container--standard">
        <!-- 内容区域 -->
        <div class="food-content">
          <div class="food-card">
            <h2 class="content-title">{{ $t('food.title') }}</h2>
            
            <div class="features">
              <h3 class="section-title">{{ $t('food.comingSoon') }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航栏 - 固定位置 -->
    <BottomBar 
      @home-clicked="onHomeClick"
      @language-changed="onLanguageChange"
      @ai-clicked="onAIClick"
    />
    
    <!-- 底部走马灯 - 固定位置 -->
    <BottomMarquee />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins } from 'vue-property-decorator'
import TopBar from '@/components/TopBar.vue'
import BottomBar from '@/components/BottomBar.vue'
import BottomMarquee from '@/components/BottomMarquee.vue'
import BackgroundImage from '@/components/BackgroundImage.vue'
import ResponsiveMixin from '@/mixins/ResponsiveMixin'
import I18nMixin from '@/mixins/I18nMixin'

@Component({
  components: {
    TopBar,
    BottomBar,
    BottomMarquee,
    BackgroundImage
  }
})
export default class Food extends Mixins(ResponsiveMixin, I18nMixin) {
  getBackgroundColor(): string {
    return '#DC2626' // Food页面的红色背景
  }
}
</script>

<style scoped>
/* 自定义样式 - 使用公共样式的基础上添加页面特定样式 */
.food-content {
  width: 1800px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600px;
}

.food-card {
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  backdrop-filter: blur(20px);
  padding: 80px;
  text-align: center;
  width: 100%;
  
  /* 毛玻璃效果 */
  box-shadow: 
    4px 4px 8px 0px rgba(0, 0, 0, 0.15),
    inset 0px 2px 4px 0px rgba(255, 255, 255, 0.1),
    inset 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
}

.content-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 96px;
  color: #FFFFFF;
  margin: 0 0 60px 0;
  line-height: 1.2;
}

.section-title {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 72px;
  color: #FFFFFF;
  margin: 0;
  line-height: 1.2;
}

.features {
  margin-top: 60px;
}
</style> 