/* 移动端性能优化样式 */

/* 禁用移动端不必要的hover效果 */
@media (hover: none) and (pointer: coarse) {
  /* 移动设备上禁用所有hover效果 */
  .card-base:hover,
  .glass-button:hover,
  .card-facility:hover,
  .card-transport:hover,
  .hover-lift:hover,
  .hover-lift-md:hover,
  .button-glass:hover {
    transform: none !important;
    box-shadow: var(--shadow-glass-inset) !important;
    background: var(--glass-bg-normal) !important;
  }
  
  /* 保持基础样式但移除hover变换 */
  .card-base,
  .glass-button,
  .card-facility,
  .card-transport {
    transition: none !important;
  }
}

/* 滚动性能优化 */
.scrollable-content-area,
.content-area {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* 减少重绘和回流 */
  contain: layout style paint;
  
  /* 优化滚动性能 */
  overscroll-behavior: contain;
  scroll-behavior: auto; /* 禁用平滑滚动以提升性能 */
}

/* 图片和媒体优化 */
img, video {
  /* 启用硬件加速 */
  transform: translateZ(0);
  
  /* 优化图片渲染 */
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  
  /* 减少重绘 */
  contain: layout style;
}

/* 动画性能优化 */
.glass-button,
.card-base,
.base-card {
  /* 只在必要时启用will-change */
  will-change: auto;
  
  /* 优化合成层 */
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 移动端特定的触摸优化 */
@media (max-width: 768px) {
  /* 减少阴影复杂度 */
  .card-base,
  .glass-button {
    box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2) !important;
  }
  
  /* 简化边框效果 */
  .glass-button,
  .card-base {
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-image: none !important;
  }
  
  /* 减少backdrop-filter使用 */
  .glass-effect,
  .glass-effect-heavy {
    backdrop-filter: blur(5px) !important;
  }
}

/* 针对Android WebView的特殊优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  /* 减少GPU负载 */
  .card-base,
  .glass-button {
    transform: none !important;
    will-change: auto !important;
  }
  
  /* 简化渐变效果 */
  .glass-button {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

/* 滚动条优化 - 减少重绘 */
.scrollable-content-area::-webkit-scrollbar {
  width: 8px !important;
}

.scrollable-content-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
  border: none !important;
}

.scrollable-content-area::-webkit-scrollbar-track {
  background: transparent !important;
}

/* 禁用不必要的CSS特效 */
@media (max-width: 1024px) {
  /* 移动设备上禁用复杂的阴影 */
  .shadow-drop-heavy,
  .shadow-drop-extra,
  .shadow-button-hover {
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2) !important;
  }
  
  /* 简化毛玻璃效果 */
  .glass-bg-light,
  .glass-bg-normal,
  .glass-bg-medium {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(3px) !important;
  }
}

/* 内存优化 - 减少DOM复杂度 */
.main-content-container {
  /* 启用GPU合成 */
  transform: translateZ(0);
  
  /* 减少重绘区域 */
  contain: layout style paint;
  
  /* 优化渲染 */
  isolation: isolate;
}

/* 触摸反馈优化 */
.glass-button:active,
.card-base:active {
  transform: scale(0.98) !important;
  transition: transform 0.1s ease !important;
}

/* 字体渲染优化 */
body, * {
  /* 优化字体渲染 */
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 减少字体相关的重绘 */
  font-display: swap;
}

/* 减少重排和重绘 */
.responsive-page-container {
  /* 固定布局，减少重排 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  /* 启用硬件加速 */
  transform: translateZ(0);
  
  /* 减少重绘 */
  contain: layout style paint;
}
