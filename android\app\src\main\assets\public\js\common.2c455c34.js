"use strict";(self["webpackChunkemap_ai"]=self["webpackChunkemap_ai"]||[]).push([[76],{6004:(e,t,a)=>{a.d(t,{A:()=>r});var i=a(1635),s=a(9603),o=a(8111);let n=class extends s.lD{playClickSound(e=.4,t="buttonClick"){try{.4!==e&&o.soundService.setVolume(e),o.soundService.playButtonClick()}catch(a){}}withClickSound(e,t=!0,a=.4){return i=>{t&&this.playClickSound(a),e&&e.call(this,i)}}setSoundEnabled(e){o.soundService.setEnabled(e)}get isSoundEnabled(){return o.soundService.isEnabled()}get audioState(){return o.soundService.getAudioState()}};n=(0,i.Cg)([s.uA],n);const r=n},8091:(e,t,a)=>{a.d(t,{aF:()=>u,z:()=>h});var i=a(4335);const s="https://testapi.bwaiwork.xyz/api",o="/eshowcaseshops",n=i.A.create({baseURL:s,timeout:1e4,headers:{"Content-Type":"application/json"}}),r=e=>({id:e.documentId,name:e.name,name_tc:e.name_tc,name_zh:e.name_zh,location:e.shopno,hours:e.openinghours,tel:e.tel,description:e.desc,logo:e.logo_url||void 0}),l=async()=>{try{console.log("正在获取商店数据...");const e=await n.get(o);if(!e.data||!e.data.data)throw new Error("API响应格式错误");const t=e.data.data.map(r);return console.log(`成功获取${t.length}个商店数据`),t}catch(e){throw console.error("获取商店数据失败:",e),e}};class c{static instance;cache=null;cacheTime=0;CACHE_DURATION=3e5;static getInstance(){return c.instance||(c.instance=new c),c.instance}async getShops(){const e=Date.now();if(this.cache&&e-this.cacheTime<this.CACHE_DURATION)return console.log("使用缓存的商店数据"),this.cache;try{return this.cache=await l(),this.cacheTime=e,this.cache}catch(t){if(this.cache)return console.log("网络错误，使用旧的缓存数据"),this.cache;throw t}}async getShopBasicInfo(){const e=await this.getShops();return e.map(e=>({id:e.id,name:e.name,name_tc:e.name_tc,name_zh:e.name_zh,logo:e.logo}))}async getShopById(e){const t=await this.getShops();return t.find(t=>t.id===e)||null}clearCache(){this.cache=null,this.cacheTime=0}}const d=c.getInstance(),u=()=>d.getShopBasicInfo(),h=e=>d.getShopById(e)},9099:(e,t,a)=>{a.d(t,{A:()=>x});var i=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{class:e.cardClasses,on:{click:e.handleClick}},[e.showIcon?t("div",{class:e.iconContainerClasses},[e.iconSrc?t("img",{class:e.iconImageClasses,attrs:{src:e.iconSrc,alt:e.title}}):e.showPlaceholder?t("div",{class:e.placeholderClasses},[e._v(" "+e._s(e.placeholderText)+" ")]):e._e()]):e._e(),e.showContent?t("div",{class:e.contentClasses},[e._t("default",function(){return[e.title&&e.enableMarquee?t("MarqueeText",{attrs:{text:e.title,"container-class":e.titleContainerClass,"text-class":e.marqueeTextClasses.join(" "),"container-style":e.titleContainerStyle,"text-style":e.titleTextStyle,speed:e.marqueeSpeed,delay:e.marqueeDelay,"animation-type":e.marqueeType}}):e.title?t("div",{class:e.titleClasses},[e._v(" "+e._s(e.title)+" ")]):e._e(),e.subtitle?t("div",{class:e.subtitleClasses},[e._v(" "+e._s(e.subtitle)+" ")]):e._e()]})],2):e._e(),e._t("custom")],2)},s=[],o=a(1635),n=a(9603),r=a(6004),l=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{ref:"container",class:["marquee-container",e.containerClass,{overflowing:e.isOverflowing}],style:e.containerStyle},[t("div",{ref:"textElement",class:["marquee-text",e.textClass],style:e.computedTextStyle},[e._v(" "+e._s(e.text)+" ")])])},c=[];let d=class extends n.lD{text;containerClass;textClass;containerStyle;textStyle;speed;delay;animationType;isOverflowing=!1;animationDuration=0;resizeObserver=null;mounted(){this.checkOverflow(),this.setupResizeObserver()}beforeDestroy(){this.resizeObserver&&this.resizeObserver.disconnect()}onTextChange(){this.$nextTick(()=>{this.checkOverflow()})}setupResizeObserver(){"ResizeObserver"in window&&(this.resizeObserver=new ResizeObserver(()=>{this.checkOverflow()}),this.resizeObserver.observe(this.$refs.container))}checkOverflow(){const e=this.$refs.container,t=this.$refs.textElement;e&&t&&(t.style.animation="none",t.style.transform="none",t.style.width="auto",t.style.maxWidth="none",this.$nextTick(()=>{const a=e.offsetWidth,i=t.scrollWidth;if(this.isOverflowing=i>a,this.isOverflowing){const s=i-a,o=s;"bounce"===this.animationType?this.animationDuration=Math.max(4,o/a*this.speed*2):this.animationDuration=o/a*this.speed*2;const n=e;let r;n.style.setProperty("--marquee-distance",`-${o}px`),r="bounce"===this.animationType?`marquee-bounce-precise ${this.animationDuration}s ease-in-out ${this.delay}s infinite`:`marquee-precise ${this.animationDuration}s linear ${this.delay}s infinite`,t.style.animation=r}else t.style.animation="none",t.style.transform="none"}))}get computedTextStyle(){return{...this.textStyle,...this.isOverflowing?{display:"inline-block",whiteSpace:"nowrap"}:{display:"block",textAlign:"center"}}}};(0,o.Cg)([(0,n.kv)({required:!0})],d.prototype,"text",void 0),(0,o.Cg)([(0,n.kv)({default:""})],d.prototype,"containerClass",void 0),(0,o.Cg)([(0,n.kv)({default:""})],d.prototype,"textClass",void 0),(0,o.Cg)([(0,n.kv)({default:()=>({})})],d.prototype,"containerStyle",void 0),(0,o.Cg)([(0,n.kv)({default:()=>({})})],d.prototype,"textStyle",void 0),(0,o.Cg)([(0,n.kv)({default:3})],d.prototype,"speed",void 0),(0,o.Cg)([(0,n.kv)({default:.3})],d.prototype,"delay",void 0),(0,o.Cg)([(0,n.kv)({default:"bounce"})],d.prototype,"animationType",void 0),(0,o.Cg)([(0,n.ox)("text")],d.prototype,"onTextChange",null),d=(0,o.Cg)([n.uA],d);const u=d,h=u;var p=a(1656),m=(0,p.A)(h,l,c,!1,null,"2c347d1a",null);const g=m.exports;let v=class extends((0,n.Xe)(r.A)){title;subtitle;iconSrc;size;variant;layout;showIcon;showContent;showPlaceholder;enableSound;enableMarquee;marqueeSpeed;marqueeDelay;marqueeType;handleClick(e){this.enableSound&&this.playClickSound(),this.$emit("click",e)}get placeholderText(){return this.title?.charAt(0)||"?"}get cardClasses(){const e=["card-base","text-primary"],t={small:"card-small",medium:"card-medium",large:"card-large","extra-large":"card-extra-large"},a={facility:"card-facility",shop:"card-shop",office:"card-office",transport:"card-transport"},i={vertical:"card-vertical",horizontal:"card-horizontal"};return[...e,t[this.size],a[this.variant],i[this.layout]]}get iconContainerClasses(){const e=["icon-container"],t={small:"icon-container-small",medium:"icon-container-medium",large:"icon-container-large","extra-large":"icon-container-extra-large"};return[...e,t[this.size]]}get iconImageClasses(){return["icon-image"]}get placeholderClasses(){const e=["icon-placeholder"],t={small:"placeholder-small",medium:"placeholder-medium",large:"placeholder-large","extra-large":"placeholder-extra-large"};return[...e,t[this.size]]}get contentClasses(){const e=["card-content"],t={vertical:"content-vertical",horizontal:"content-horizontal"};return[...e,t[this.layout]]}get titleClasses(){const e=["card-title","text-primary"],t={small:"title-small",medium:"title-medium",large:"title-large","extra-large":"title-extra-large"},a={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...e,t[this.size],a[this.variant]]}get subtitleClasses(){const e=["card-subtitle","text-primary"],t={small:"subtitle-small",medium:"subtitle-medium",large:"subtitle-large","extra-large":"subtitle-extra-large"};return[...e,t[this.size]]}get marqueeTextClasses(){const e=["marquee-title","text-primary"],t={small:"marquee-title-small",medium:"marquee-title-medium",large:"marquee-title-large","extra-large":"marquee-title-extra-large"},a={facility:"title-facility",shop:"title-shop",office:"title-office",transport:"title-transport"};return[...e,t[this.size],a[this.variant]]}get titleContainerClass(){return`title-container-${this.size}`}get titleContainerStyle(){return"large"===this.size?{width:"360px",position:"absolute",bottom:"60px",left:"50%",transform:"translateX(-50%)"}:{}}get titleTextStyle(){const e={small:{fontWeight:"var(--font-weight-medium)",fontSize:"var(--font-size-xs)"},medium:{fontWeight:"var(--font-weight-normal)",fontSize:"var(--font-size-sm)"},large:{fontWeight:"var(--font-weight-medium)",fontSize:"var(--font-size-md)"},"extra-large":{fontWeight:"var(--font-weight-light)",fontSize:"var(--font-size-lg)"}};return{color:"inherit",...e[this.size]}}};(0,o.Cg)([(0,n.kv)({required:!0})],v.prototype,"title",void 0),(0,o.Cg)([(0,n.kv)()],v.prototype,"subtitle",void 0),(0,o.Cg)([(0,n.kv)()],v.prototype,"iconSrc",void 0),(0,o.Cg)([(0,n.kv)({default:"medium"})],v.prototype,"size",void 0),(0,o.Cg)([(0,n.kv)({default:"facility"})],v.prototype,"variant",void 0),(0,o.Cg)([(0,n.kv)({default:"vertical"})],v.prototype,"layout",void 0),(0,o.Cg)([(0,n.kv)({default:!0})],v.prototype,"showIcon",void 0),(0,o.Cg)([(0,n.kv)({default:!0})],v.prototype,"showContent",void 0),(0,o.Cg)([(0,n.kv)({default:!0})],v.prototype,"showPlaceholder",void 0),(0,o.Cg)([(0,n.kv)({default:!0})],v.prototype,"enableSound",void 0),(0,o.Cg)([(0,n.kv)({default:!1})],v.prototype,"enableMarquee",void 0),(0,o.Cg)([(0,n.kv)({default:3})],v.prototype,"marqueeSpeed",void 0),(0,o.Cg)([(0,n.kv)({default:1})],v.prototype,"marqueeDelay",void 0),(0,o.Cg)([(0,n.kv)({default:"bounce"})],v.prototype,"marqueeType",void 0),v=(0,o.Cg)([(0,n.uA)({components:{MarqueeText:g}})],v);const y=v,f=y;var C=(0,p.A)(f,i,s,!1,null,"841d06cc",null);const x=C.exports}}]);