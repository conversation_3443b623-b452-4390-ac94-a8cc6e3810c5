module.exports = {
  publicPath: './',
  outputDir: 'dist',
  // 生产环境关闭 source map
  productionSourceMap: false,
  // 优化构建
  configureWebpack: {
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.vue', '.json']
    },
    // 生产环境优化
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true
          }
        }
      }
    },
    // 性能优化
    performance: {
      hints: false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000
    }
  },
  chainWebpack: config => {
    config.module
      .rule('tsx')
      .test(/\.tsx$/)
      .use('vue-loader')
      .loader('vue-loader')
      .end()
      .use('ts-loader')
      .loader('ts-loader')
      .options({
        appendTsSuffixTo: [/\.vue$/],
        transpileOnly: true
      })
    
    // 图片优化
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096,
        fallback: {
          loader: 'file-loader',
          options: {
            name: 'img/[name].[hash:8].[ext]'
          }
        }
      })

    // 字体优化
    config.module
      .rule('fonts')
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096,
        fallback: {
          loader: 'file-loader',
          options: {
            name: 'fonts/[name].[hash:8].[ext]'
          }
        }
      })


  },
  // PWA 相关配置（可选）
  pwa: {
    name: 'EMAP AI',
    themeColor: '#000000',
    msTileColor: '#000000',
    manifestOptions: {
      background_color: '#000000'
    }
  }
} 